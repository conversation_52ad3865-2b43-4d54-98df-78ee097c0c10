{% extends 'base.html' %}
{% block title %}Goods Search{% endblock %}

{% block content %}
<h1>Search Goods</h1>
<form method="get" id="searchForm">
    Name: <input type="text" name="name" value="{{ filters.name }}">
    Category:
    <select name="category">
        <option value="">--All--</option>
        {% for cat in categories %}
            <option value="{{ cat }}" {% if filters.category == cat %}selected{% endif %}>{{ cat }}</option>
        {% endfor %}
    </select>
    Min Price: <input type="number" step="0.01" name="min_price" value="{{ filters.min_price }}">
    Max Price: <input type="number" step="0.01" name="max_price" value="{{ filters.max_price }}">
    <button type="submit">Search</button>
</form>
<hr>
<ul>
    {% for g in goods %}
    <li>
        <strong>{{ g.name }}</strong> - ${{ g.price }}<br>
        Category: {{ g.category }}<br>
        Description: {{ g.description }}<br>
        Contact Store: 
        {% if g.contact_store %}
            {{ g.contact_store.store_name }}
        {% else %}
            No store info
        {% endif %}
        <a href="{% url 'get_seller_contact' g.id %}">View Seller Contact</a>
    </li>
    {% empty %}
    <li>No goods found.</li>
    {% endfor %}
</ul>
{% endblock %}
