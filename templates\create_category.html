{% comment %} {% extends 'base.html' %}

{% block title %}Create Category{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h2 class="text-2xl font-bold text-gray-800 mb-6">Create New Category</h2>
        
        <form id="categoryForm" class="space-y-4">
            {% csrf_token %}
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name
                </label>
                <input 
                    type="text" 
                    id="name" 
                    name="name" 
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter category name"
                >
            </div>
            
            <button 
                type="submit" 
                class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200"
            >
                Create Category
            </button>
        </form>
        
        <div id="message" class="mt-4 hidden"></div>
        
        <div class="mt-6 text-center">
            <a href="{% url 'list_categories' %}" class="text-blue-600 hover:text-blue-800">
                View All Categories
            </a>
        </div>
    </div>
</div>

<script>
document.getElementById('categoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const messageDiv = document.getElementById('message');
    
    fetch('{% url "create_category" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        messageDiv.classList.remove('hidden');
        
        if (data.message) {
            messageDiv.className = 'mt-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded';
            messageDiv.textContent = data.message;
            document.getElementById('name').value = '';
        } else if (data.error) {
            messageDiv.className = 'mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded';
            messageDiv.textContent = data.error;
        }
    })
    .catch(error => {
        messageDiv.classList.remove('hidden');
        messageDiv.className = 'mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded';
        messageDiv.textContent = 'An error occurred. Please try again.';
    });
});
</script>
{% endblock %} {% endcomment %}