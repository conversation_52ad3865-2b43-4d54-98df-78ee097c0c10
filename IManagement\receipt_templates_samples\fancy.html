<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Fancy Professional Receipt</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Great+Vibes&family=Playfair+Display:wght@400;600;700&display=swap');
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Georgia', serif;
      line-height: 1.5;
      color: #7f4f24;
      background: linear-gradient(135deg, #fdf6f0 0%, #f4e6d3 100%);
      padding: 20px;
      font-size: 14px;
      min-height: 100vh;
    }

    .receipt-container {
      max-width: 650px;
      margin: 0 auto;
      background: #fdf6f0;
      border: 3px solid #e67e22;
      border-radius: 20px;
      overflow: hidden;
      box-shadow: 0 10px 30px rgba(231, 126, 34, 0.2);
      position: relative;
    }

    .receipt-container::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: 
        radial-gradient(circle at 20% 80%, rgba(231, 126, 34, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(211, 84, 0, 0.1) 0%, transparent 50%);
      pointer-events: none;
      z-index: 1;
    }

    .receipt-header {
      background: linear-gradient(135deg, #d35400 0%, #e67e22 50%, #f39c12 100%);
      color: #fdf6f0;
      padding: 30px 20px;
      text-align: center;
      position: relative;
      z-index: 2;
      border-bottom: 4px solid #c0392b;
    }

    .receipt-header::after {
      content: '';
      position: absolute;
      bottom: -4px;
      left: 0;
      right: 0;
      height: 4px;
      background: repeating-linear-gradient(
        90deg,
        #d35400 0px,
        #e67e22 10px,
        #f39c12 20px,
        #e67e22 30px
      );
    }

    .receipt-title {
      font-family: 'Great Vibes', cursive;
      font-size: 3.5em;
      font-weight: 400;
      margin-bottom: 10px;
      text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
      letter-spacing: 2px;
    }

    .receipt-subtitle {
      font-family: 'Playfair Display', serif;
      font-size: 1.1em;
      opacity: 0.9;
      font-weight: 400;
      letter-spacing: 3px;
      text-transform: uppercase;
    }

    .receipt-body {
      padding: 30px;
      position: relative;
      z-index: 2;
    }

    .company-info {
      text-align: center;
      margin-bottom: 30px;
      padding: 20px;
      background: rgba(249, 213, 179, 0.6);
      border-radius: 15px;
      border: 2px dashed #e67e22;
      position: relative;
    }

    .company-info::before {
      content: '✦';
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translateX(-50%);
      background: #fdf6f0;
      color: #e67e22;
      font-size: 1.2em;
      padding: 0 10px;
    }

    .company-name {
      font-family: 'Playfair Display', serif;
      font-size: 1.8em;
      font-weight: 700;
      color: #d35400;
      margin-bottom: 8px;
      letter-spacing: 2px;
    }

    .company-message {
      color: #a67c48;
      font-size: 1.1em;
      font-style: italic;
      font-weight: 600;
      letter-spacing: 1px;
    }

    .company-contact {
      margin-top: 10px;
      font-size: 0.95em;
      color: #7f4f24;
    }

    .company-contact div {
      margin-bottom: 3px;
    }

    .client-info {
      margin-bottom: 25px;
    }

    .details-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 20px;
    }

    .detail-item {
      background: rgba(255, 255, 255, 0.7);
      padding: 15px;
      border-radius: 12px;
      border: 2px solid #f9d5b3;
      border-left: 5px solid #e67e22;
    }

    .detail-label {
      font-family: 'Playfair Display', serif;
      font-size: 0.9em;
      color: #d35400;
      text-transform: uppercase;
      letter-spacing: 1px;
      margin-bottom: 8px;
      font-weight: 600;
    }

    .detail-value {
      font-size: 1em;
      font-weight: 600;
      color: #7f4f24;
      line-height: 1.4;
    }

    .items-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin-bottom: 20px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(231, 126, 34, 0.2);
      font-size: 0.95em;
    }

    .items-table thead {
      background: linear-gradient(135deg, #d35400 0%, #e67e22 100%);
      color: #fdf6f0;
    }

    .items-table th {
      padding: 15px 12px;
      text-align: left;
      font-family: 'Playfair Display', serif;
      font-weight: 700;
      text-transform: uppercase;
      letter-spacing: 1px;
      font-size: 0.9em;
    }

    .items-table tbody tr {
      border-bottom: 1px solid #f9d5b3;
      transition: all 0.3s ease;
      background: rgba(255, 255, 255, 0.8);
    }

    .items-table tbody tr:hover {
      background: rgba(249, 213, 179, 0.6);
      transform: translateY(-1px);
    }

    .items-table tbody tr:last-child {
      border-bottom: none;
    }

    .items-table td {
      padding: 12px;
      vertical-align: middle;
    }

    .item-name {
      font-weight: 600;
      color: #7f4f24;
    }

    .item-quantity, .item-price {
      font-family: 'Georgia', serif;
      text-align: right;
      font-weight: 600;
    }

    .totals-section {
      background: rgba(249, 213, 179, 0.8);
      padding: 20px;
      border-radius: 15px;
      border: 2px solid #e67e22;
      margin-bottom: 25px;
      position: relative;
    }

    .totals-section::before {
      content: '※';
      position: absolute;
      top: -10px;
      right: 20px;
      background: #fdf6f0;
      color: #e67e22;
      font-size: 1.5em;
      padding: 0 8px;
    }

    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px dotted #d35400;
    }

    .total-row:last-child {
      border-bottom: none;
      font-size: 1.5em;
      font-weight: 700;
      color: #d35400;
      padding-top: 15px;
      border-top: 3px double #e67e22;
      margin-top: 10px;
    }

    .total-label {
      font-family: 'Playfair Display', serif;
      font-weight: 600;
    }

    .total-value {
      font-family: 'Georgia', serif;
      font-weight: 700;
    }

    .signatures-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 25px;
      margin-top: 25px;
    }

    .signature-block {
      text-align: center;
      padding: 15px;
      background: rgba(255, 255, 255, 0.8);
      border: 3px dashed #e67e22;
      border-radius: 12px;
      height: 120px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      position: relative;
    }

    .signature-block::before {
      content: attr(data-icon);
      position: absolute;
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
      background: #fdf6f0;
      color: #e67e22;
      font-size: 1.3em;
      padding: 0 8px;
    }

    .signature-label {
      font-family: 'Playfair Display', serif;
      font-weight: 600;
      color: #d35400;
      margin-bottom: 10px;
      text-transform: uppercase;
      letter-spacing: 1px;
      font-size: 0.9em;
      flex-shrink: 0;
    }

    .signature-area {
      flex-grow: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 70px;
      padding-bottom: 10px;
      position: relative;
    }

    .signature-area img {
      max-width: 90%;
      max-height: 60px;
      object-fit: contain;
      border: none;
      border-radius: 5px;
      padding: 0;
      background: transparent;
    }

    .signature-line {
      width: 80%;
      height: 2px;
      background: linear-gradient(90deg, transparent, #d35400, transparent);
      margin: 5px auto 0;
    }

    .logo-image {
      width: 90px;
      height: 90px;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid #e67e22;
      background: white;
      box-shadow: 0 4px 15px rgba(231, 126, 34, 0.3);
    }

    .receipt-footer {
      background: linear-gradient(135deg, #7f4f24 0%, #a67c48 100%);
      color: #fdf6f0;
      text-align: center;
      padding: 15px;
      font-size: 0.85em;
      font-style: italic;
      position: relative;
      z-index: 2;
    }

    @media (max-width: 768px) {
      .receipt-container {
        margin: 10px;
        border-radius: 15px;
      }
      
      .receipt-body {
        padding: 20px;
      }
      
      .details-grid,
      .signatures-section {
        grid-template-columns: 1fr;
        gap: 15px;
      }
      
      .items-table th,
      .items-table td {
        padding: 10px 8px;
        font-size: 0.85em;
      }
      
      .receipt-title {
        font-size: 2.5em;
      }
    }

    @media print {
      body {
        background: white;
        padding: 0;
      }
      
      .receipt-container {
        box-shadow: none;
        border: 2px solid #e67e22;
        margin: 0;
        border-radius: 0;
      }
    }
  </style>
  <script>
    // Auto-generate receipt number and date if not provided
    document.addEventListener('DOMContentLoaded', function() {
      // Generate random receipt number
      const receiptNumberElements = document.querySelectorAll('[data-auto="receipt-number"]');
      receiptNumberElements.forEach(element => {
        if (!element.textContent.trim()) {
          const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number
          element.textContent = `#RCP${randomNumber}`;
        }
      });
      
      // Generate current date and time
      const dateElements = document.querySelectorAll('[data-auto="date"]');
      dateElements.forEach(element => {
        if (!element.textContent.trim()) {
          const now = new Date();
          const day = String(now.getDate()).padStart(2, '0');
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const year = now.getFullYear();
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          
          element.textContent = `${day}/${month}/${year} ${hours}:${minutes}`;
        }
      });
    });
  </script>
</head>
<body>
  <div class="receipt-container">
    <header class="receipt-header" style="display: flex; align-items: center; justify-content: space-between; padding: 30px 20px;">
      <div style="flex-shrink: 0;">
        {% if avatar_url %}
          <img src="{{ avatar_url }}" alt="Company Logo" class="logo-image" />
        {% else %}
          <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 50%; border: 3px solid #fdf6f0;"></div>
        {% endif %}
      </div>

      <div style="flex-grow: 1; text-align: center;">
        <h1 class="receipt-title">Receipt</h1>
        <p class="receipt-subtitle">Transaction Record</p>
      </div>

      <div style="width: 80px;"></div>
    </header>

    <main class="receipt-body">
      <section class="company-info">
        <div class="company-name">{{ company_name }}</div>
        <div class="company-contact">
          {% if phone_number %}
          <div>Phone: {{ phone_number }}</div>
          {% endif %}
          {% if secondary_email %}
          <div>Email: {{ secondary_email }}</div>
          {% endif %}
        </div>
        <div class="company-message">Thank you for your business!</div>
      </section>

      <section class="client-info">
        <div class="details-grid">
          <div class="detail-item">
            <div class="detail-label">Customer Details</div>
            <div class="detail-value">
              {% if client_name %}
                <strong>{{ client_name }}</strong><br>
              {% endif %}
              {% if client_email %}
                {{ client_email }}<br>
              {% endif %}
              {% if client_phone %}
                {{ client_phone }}<br>
              {% endif %}
              {% if client_address %}
                {{ client_address }}
              {% endif %}
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-label">Receipt Details</div>
            <div class="detail-value">
              Receipt No: 
              {% if receipt_number %}
                {{ receipt_number }}
              {% else %}
                <span data-auto="receipt-number"></span>
              {% endif %}
              <br>
              Date: 
              {% if transaction_date %}
                {{ transaction_date }}
              {% else %}
                <span data-auto="date"></span>
              {% endif %}
            </div>
          </div>
        </div>
      </section>

      <section class="items-section">
        <table class="items-table">
          <thead>
            <tr>
              <th>Item Description</th>
              <th style="text-align: center;">Qty</th>
              <th style="text-align: right;">Unit Price</th>
              <th style="text-align: right;">Total</th>
            </tr>
          </thead>
          <tbody>
            {% for item in items %}
            <tr>
              <td class="item-name">{{ item.name }}</td>
              <td class="item-quantity" style="text-align: center;">{{ item.quantity }}</td>
              <td class="item-price">{{ currency_symbol }}{{ item.unit_price }}</td>
              <td class="item-price">{{ currency_symbol }}{{ item.total_price }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </section>

      <section class="totals-section">
        <div class="total-row">
          <span class="total-label">Subtotal:</span>
          <span class="total-value">{{ currency_symbol }} {{ subtotal|default:total_price }}</span>
        </div>
        {% if tax_amount %}
        <div class="total-row">
          <span class="total-label">Tax:</span>
          <span class="total-value">{{ currency_symbol }} {{ tax_amount }}</span>
        </div>
        {% endif %}
        {% if discount_amount %}
        <div class="total-row">
          <span class="total-label">Discount:</span>
          <span class="total-value">-{{ currency_symbol }} {{ discount_amount }}</span>
        </div>
        {% endif %}
        <div class="total-row">
          <span class="total-label">Total Amount:</span>
          <span class="total-value">{{ currency_symbol }} {{ total_price }}</span>
        </div>
      </section>

      <section class="signatures-section">
        <div class="signature-block" data-icon="✍">
          <div class="signature-label">Authorized Signature</div>
          <div class="signature-area">
            {% if signature_url %}
              <img src="{{ signature_url }}" alt="Signature" />
            {% endif %}
          </div>
          <div class="signature-line"></div>
        </div>

        <div class="signature-block" data-icon="⚡">
          <div class="signature-label">Official Stamp</div>
          <div class="signature-area">
            {% if stamp_url %}
              <img src="{{ stamp_url }}" alt="Official Stamp" />
            {% endif %}
          </div>
          <div class="signature-line"></div>
        </div>
      </section>
    </main>

    <footer class="receipt-footer">
      <p>This receipt serves as proof of transaction • Please retain for your records</p>
    </footer>
  </div>
</body>
</html>