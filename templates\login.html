<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Inventory Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }


            .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-link {
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 450px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .login-header h1 {
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .login-header p {
            color: #666;
            margin: 0;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-group-text {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            z-index: 5;
        }

        .form-control.with-icon {
            padding-left: 50px;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-weight: bold;
            font-size: 16px;
            width: 100%;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 20px;
            color: #666;
        }

        .auth-links {
            text-align: center;
        }

        .auth-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .auth-links a:hover {
            color: #764ba2;
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }

        .loading-spinner {
            display: none;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            z-index: 5;
        }

        .form-check {
            padding: 15px 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(102, 126, 234, 0.1);
        }

        .form-check-input:checked {
            background-color: #667eea;
            border-color: #667eea;
        }

        .form-check-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-check-label {
            margin-left: 8px;
        }

        @media (max-width: 576px) {
            .login-card {
                padding: 30px 20px;
                margin: 10px;
            }
        }
    </style>
</head>
<body>


            <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="/">
                <i class="fas fa-boxes"></i> InventoryPro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/.">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/public/goods/">Browse Goods</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-primary px-3 ms-2" href="{% url 'register' %}">Sign Up</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <h1><i class="fas fa-boxes text-primary"></i> Inventory Pro</h1>
                <p>Sign in to your account</p>
            </div>

            <div id="alertContainer"></div>

            {% if error %}
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                {{ error }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endif %}

            {% if show_verification_link %}
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-envelope me-2"></i>
                <strong>Email Verification Required</strong><br>
                Please check your email and verify your account before logging in.
                <div class="mt-2">
                    <a href="/verify-email/?user_id={{ user_id }}&email={{ email|urlencode }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-envelope-open me-1"></i>Verify Email
                    </a>
                    <button type="button" class="btn btn-sm btn-outline-secondary ms-2" onclick="resendVerification({{ user_id }}, '{{ email }}')">
                        <i class="fas fa-paper-plane me-1"></i>Resend Code
                    </button>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endif %}

            <form method="POST" action="{% url 'login' %}" id="loginForm">
                {% csrf_token %}
                
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-user"></i>
                    </span>
                    <input type="text" class="form-control with-icon" id="username" name="username" 
                           placeholder="Username" required>
                </div>

                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" class="form-control with-icon" id="password" name="password"
                           placeholder="Password" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="fas fa-eye" id="passwordToggleIcon"></i>
                    </button>
                </div>

                <div class="form-check mb-3">
                    <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me" value="1">
                    <label class="form-check-label" for="remember_me">
                        <strong>Remember me for 30 days</strong>
                        <small class="d-block text-muted">Keep me logged in on this device</small>
                    </label>
                </div>

                <button type="submit" class="btn btn-login" id="loginBtn">
                    <span class="login-text">Login</span>
                    <span class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i> Signing In...
                    </span>
                </button>
            </form>

            <div class="divider">
                <span>or</span>
            </div>

            <div class="auth-links">
                <p class="mb-2">
                    <a href="/forget-password/">Forgot your password?</a>
                </p>
                <p class="mb-0">
                    Don't have an account? <a href="{% url 'register' %}">Sign up here</a>
                </p>
            </div>
        </div>
    </div>

     <!-- Footer -->
<footer class="bg-dark text-light py-5">
    <div class="container">
        <div class="row">
            <!-- Company Info Section -->
            <div class="col-lg-4 mb-4">
                <h5 class="fw-bold mb-3 text-white">Company House</h5>
                <p class="text-light mb-3">
                    We are a registered IT company in the UK with company number <span class="text-warning">********</span>
                </p>
            </div>

            <!-- Contact Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Contact</h6>
                <div class="contact-info">
                    <p class="text-light mb-2">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <strong>Address:</strong> 13L Queensway, Ponders End, Enfield, London EN3 4SA
                    </p>
                    <p class="text-light mb-2">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <strong>Email:</strong> <EMAIL>
                    </p>
                    <p class="text-light mb-0">
                        <i class="fas fa-phone me-2 text-primary"></i>
                        <strong>Tel:</strong> 07500503952
                    </p>
                </div>
            </div>

            <!-- Subscribe Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Subscribe Now</h6>
                <p class="text-light mb-3">
                    Don't miss our future updates! Get Subscribed Today!
                </p>
                <div class="mb-3">
            <div class="col-md-6 text-md-start">
                <a href="/register/" class="btn btn-primary">
                    <i class="fas fa-rocket me-2"></i>Register
                </a>
            </div>
                </div>
                <!-- Social Icons -->
                <div class="social-icons">
                    <a href="https://www.facebook.com/amatip.info.tech" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://x.com/amatipIT" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.tiktok.com/@amatip_it?is_from_webapp=1&sender_device=pc" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-tiktok"></i>
                    </a>
                    <a href="https://youtube.com/@amatip_it?si=eQ54UaVIM-DgLOAr" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="https://www.instagram.com/amatip_it/profilecard/?igsh=MWRzbGV5b3h1MTQ2Yw==" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="d-flex flex-wrap gap-4">
                    <a href="#features" class="text-muted text-decoration-none">Features</a>
                    <a href="/public/goods/" class="text-muted text-decoration-none">Browse Goods</a>
                    <a href="/login/" class="text-muted text-decoration-none">Login</a>
                    <a href="/register/" class="text-muted text-decoration-none">Sign Up</a>
                </div>
            </div>
        </div>

        <hr class="my-4 border-secondary">
        
        <!-- Copyright -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-muted">&copy; 2023 Amatip. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <small class="text-muted">Built with Django & Bootstrap</small>
            </div>
        </div>
    </div>
</footer>


    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();
            
            const alertHTML = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHTML);
            
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        function getAlertIcon(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + 'ToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.replace('fa-eye', 'fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.replace('fa-eye-slash', 'fa-eye');
            }
        }

        function setLoading(isLoading) {
            const loginBtn = document.getElementById('loginBtn');
            const loginText = loginBtn.querySelector('.login-text');
            const loadingSpinner = loginBtn.querySelector('.loading-spinner');
            
            if (isLoading) {
                loginBtn.disabled = true;
                loginText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
            } else {
                loginBtn.disabled = false;
                loginText.style.display = 'inline';
                loadingSpinner.style.display = 'none';
            }
        }

        // Enhanced form submission with loading state
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            setLoading(true);
            
            // Add a small delay to show the loading state
            setTimeout(() => {
                // The form will submit normally to Django
                // This just provides visual feedback
            }, 100);
        });

        // Auto-focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();
        });

        // Resend verification code function
        function resendVerification(userId, email) {
            const button = event.target;
            const originalText = button.innerHTML;

            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';
            button.disabled = true;

            fetch('/resend-verification/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name="csrfmiddlewaretoken"]').value,
                },
                body: JSON.stringify({
                    user_id: userId
                }),
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.error || 'Failed to resend verification code', 'danger');
                }
            })
            .catch(error => {
                showAlert('An error occurred while resending verification code', 'danger');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }

        // Enhance existing Django error alerts with icons
        document.addEventListener('DOMContentLoaded', function() {
            const existingAlerts = document.querySelectorAll('.alert:not([id^="alert-"])');
            existingAlerts.forEach(alert => {
                if (!alert.querySelector('.fas')) {
                    const icon = document.createElement('i');
                    icon.className = 'fas fa-exclamation-triangle me-2';
                    alert.insertBefore(icon, alert.firstChild);
                }
            });
        });
    </script>
</body>
</html>