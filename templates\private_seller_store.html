{% extends 'base.html' %}
{% load static %}

{% block title %}My Private Store - Professional Inventory Management{% endblock %}

{% block extra_css %}
<style>
    .store-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
    }
    
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        border-left: 4px solid;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
    }
    
    .stat-card.primary { border-left-color: #007bff; }
    .stat-card.success { border-left-color: #28a745; }
    .stat-card.warning { border-left-color: #ffc107; }
    .stat-card.danger { border-left-color: #dc3545; }
    .stat-card.info { border-left-color: #17a2b8; }
    .stat-card.purple { border-left-color: #6f42c1; }
    
    .goods-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: 1rem;
        margin-top: 1.5rem;
    }
    
    .goods-card {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        transition: transform 0.2s ease;
        position: relative;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .goods-card:hover {
        transform: translateY(-2px);
    }

    .goods-image-container {
        height: 160px;
        overflow: hidden;
        position: relative;
    }
    
    .goods-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .goods-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 3rem;
    }
    
    .stock-overlay {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        color: white;
    }
    
    .stock-overlay.in-stock { background: #28a745; }
    .stock-overlay.low-stock { background: #ffc107; color: #212529; }
    .stock-overlay.out-of-stock { background: #dc3545; }
    .stock-overlay.not-tracked { background: #6c757d; }

    .recent-transactions {
        margin-top: 15px;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 3px solid #007bff;
    }

    .recent-transactions strong {
        color: #495057;
        font-size: 0.9rem;
        display: block;
        margin-bottom: 8px;
    }

    .transaction-item {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 4px;
        padding: 2px 0;
    }

    .recent-transactions .btn {
        font-size: 0.75rem;
        padding: 4px 8px;
    }
    
    .goods-body {
        padding: 1.5rem;
    }
    
    .goods-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
    }
    
    .goods-sku {
        font-size: 0.8rem;
        color: #6c757d;
        font-family: 'Courier New', monospace;
        margin-bottom: 0.5rem;
    }
    
    .goods-price {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 1rem;
    }
    
    .inventory-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
        margin: 1rem 0;
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }
    
    .inventory-item {
        text-align: center;
    }
    
    .inventory-value {
        font-size: 1.2rem;
        font-weight: bold;
        color: #333;
    }
    
    .inventory-label {
        font-size: 0.8rem;
        color: #6c757d;
        text-transform: uppercase;
    }
    
    .quick-actions {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 0.5rem;
        margin-top: 1rem;
    }
    
    .action-btn {
        padding: 0.5rem;
        border: none;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .btn-sell {
        background: #dc3545;
        color: white;
    }
    
    .btn-sell:hover {
        background: #c82333;
    }
    
    .btn-add {
        background: #28a745;
        color: white;
    }
    
    .btn-add:hover {
        background: #218838;
    }
    
    .btn-edit {
        background: #007bff;
        color: white;
    }
    
    .btn-edit:hover {
        background: #0056b3;
    }
    
    .filter-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .star-rating {
        color: #ffc107;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }
    
    .profit-indicator {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
        margin-top: 0.5rem;
    }
    
    .profit-positive {
        background: #d4edda;
        color: #155724;
    }
    
    .profit-negative {
        background: #f8d7da;
        color: #721c24;
    }
    
    .recent-transactions {
        margin-top: 1rem;
        padding-top: 1rem;
        border-top: 1px solid #dee2e6;
    }
    
    .transaction-item {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.25rem;
    }
    
    @media (max-width: 768px) {
        .store-header {
            padding: 1rem 0;
        }
        
        .stats-grid {
            grid-template-columns: 1fr;
        }
        
        .goods-grid {
            grid-template-columns: 1fr;
        }
        
        .inventory-info {
            grid-template-columns: 1fr;
        }
        
        .quick-actions {
            grid-template-columns: 1fr;
        }
    }

    /* Property Cards */
    .property-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 12px;
        overflow: hidden;
    }

    .property-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .property-image-container {
        position: relative;
        height: 200px;
        overflow: hidden;
    }

    .property-image {
        width: 100%;
        height: 200px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }

    .property-card:hover .property-image {
        transform: scale(1.05);
    }

    .property-card .card-body {
        padding: 1.5rem;
    }

    .property-card .card-title {
        color: #2c3e50;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .property-card .btn-group .btn {
        border-radius: 6px;
    }

    .property-card .btn-group .btn:first-child {
        margin-right: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Store Header -->
<div class="store-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-store me-2"></i>
                    My Private Store
                </h1>
                <p class="mb-0 opacity-75">
                    Professional inventory management for your goods
                </p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="{% url 'goods_list_create' %}" class="btn btn-light btn-lg">
                    <i class="fas fa-plus me-2"></i>Add New Product
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Section -->
<div class="container">
    <div class="stats-grid">
        <div class="stat-card primary">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-primary text-white rounded-circle p-3 me-3">
                    <i class="fas fa-boxes"></i>
                </div>
                <div>
                    <h3 class="mb-1">{{ statistics.total_goods }}</h3>
                    <p class="text-muted mb-0">Total Products</p>
                </div>
            </div>
        </div>
        
        <div class="stat-card success">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-success text-white rounded-circle p-3 me-3">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                <div>
                    <h3 class="mb-1">${{ statistics.total_inventory_value|floatformat:2 }}</h3>
                    <p class="text-muted mb-0">Inventory Value</p>
                </div>
            </div>
        </div>
        
        <div class="stat-card info">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-info text-white rounded-circle p-3 me-3">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div>
                    <h3 class="mb-1">{{ statistics.total_units_sold }}</h3>
                    <p class="text-muted mb-0">Units Sold</p>
                </div>
            </div>
        </div>
        
        <div class="stat-card warning">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-warning text-white rounded-circle p-3 me-3">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div>
                    <h3 class="mb-1">{{ statistics.low_stock_count }}</h3>
                    <p class="text-muted mb-0">Low Stock</p>
                </div>
            </div>
        </div>
        
        <div class="stat-card danger">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-danger text-white rounded-circle p-3 me-3">
                    <i class="fas fa-times-circle"></i>
                </div>
                <div>
                    <h3 class="mb-1">{{ statistics.out_of_stock_count }}</h3>
                    <p class="text-muted mb-0">Out of Stock</p>
                </div>
            </div>
        </div>
        
        <div class="stat-card purple">
            <div class="d-flex align-items-center">
                <div class="stat-icon bg-purple text-white rounded-circle p-3 me-3" style="background: #6f42c1 !important;">
                    <i class="fas fa-coins"></i>
                </div>
                <div>
                    <h3 class="mb-1">${{ statistics.total_profit_potential|floatformat:2 }}</h3>
                    <p class="text-muted mb-0">Profit Potential</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="filter-section">
        <form method="GET" id="filterForm" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search Products</label>
                <input type="text" class="form-control" id="search" name="q" 
                       value="{{ filters.search_query }}" placeholder="Search by name, SKU...">
            </div>
            
            <div class="col-md-2">
                <label for="store" class="form-label">Store</label>
                <select class="form-select" id="store" name="store_id">
                    <option value="">All Stores</option>
                    {% for store in user_stores %}
                        <option value="{{ store.id }}" 
                                {% if filters.store_id == store.id|stringformat:"s" %}selected{% endif %}>
                            {{ store.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="category" class="form-label">Category</label>
                <select class="form-select" id="category" name="category_id">
                    <option value="">All Categories</option>
                    {% for category in user_categories %}
                        <option value="{{ category.id }}" 
                                {% if filters.category_id == category.id|stringformat:"s" %}selected{% endif %}>
                            {{ category.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            
            <div class="col-md-2">
                <label for="stockStatus" class="form-label">Stock Status</label>
                <select class="form-select" id="stockStatus" name="stock_status">
                    <option value="">All Status</option>
                    <option value="in_stock" {% if filters.stock_status == 'in_stock' %}selected{% endif %}>In Stock</option>
                    <option value="low_stock" {% if filters.stock_status == 'low_stock' %}selected{% endif %}>Low Stock</option>
                    <option value="out_of_stock" {% if filters.stock_status == 'out_of_stock' %}selected{% endif %}>Out of Stock</option>
                </select>
            </div>
            
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Filter
                </button>
                <a href="{% url 'private_seller_store' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Goods Grid -->
    <div class="goods-grid">
        {% for good in goods %}
        <div class="goods-card" data-goods-id="{{ good.id }}">
            <div class="goods-image-container">
                {% if good.first_image_url %}
                    <img src="/media/{{ good.first_image_url }}" 
                         class="goods-image" 
                         alt="{{ good.name }}">
                {% else %}
                    <div class="goods-placeholder">
                        <i class="fas fa-image"></i>
                    </div>
                {% endif %}
                
                <div class="stock-overlay {{ good.stock_status|lower|cut:'_' }}">
                    {{ good.stock_status_display }}
                </div>
            </div>
            
            <div class="goods-body">
                <div class="goods-title">{{ good.name }}</div>
                <div class="goods-sku">SKU: {{ good.sku }}</div>
                
                {% if good.star_rating %}
                    <div class="star-rating">
                        {{ good.star_rating_display }}
                    </div>
                {% endif %}
                
                <div class="goods-price">${{ good.price }}</div>
                
                <div class="inventory-info">
                    <div class="inventory-item">
                        <div class="inventory-value">{{ good.quantity }}</div>
                        <div class="inventory-label">In Stock</div>
                    </div>
                    <div class="inventory-item">
                        <div class="inventory-value">{{ good.units_sold }}</div>
                        <div class="inventory-label">Sold</div>
                    </div>
                </div>
                
                {% if good.profit_per_unit %}
                    <div class="profit-indicator {% if good.profit_per_unit > 0 %}profit-positive{% else %}profit-negative{% endif %}">
                        Profit: ${{ good.profit_per_unit|floatformat:2 }}/unit
                    </div>
                {% endif %}
                
                <div class="quick-actions">
                    <button class="action-btn btn-sell" 
                            onclick="sellStock({{ good.id }}, '{{ good.name|escapejs }}')"
                            {% if good.is_out_of_stock %}disabled{% endif %}>
                        <i class="fas fa-minus me-1"></i>Sell
                    </button>
                    <button class="action-btn btn-add" 
                            onclick="addStock({{ good.id }}, '{{ good.name|escapejs }}')">
                        <i class="fas fa-plus me-1"></i>Add
                    </button>
                    <a href="/goods/{{ good.id }}/" class="action-btn btn-edit">
                        <i class="fas fa-edit me-1"></i>Edit
                    </a>
                </div>
                
                <!-- Recent Activity Section -->
                {% if good.recent_transactions %}
                    <div class="recent-transactions">
                        <strong>Recent Activity:</strong>
                        {% for transaction in good.recent_transactions|slice:":3" %}
                            <div class="transaction-item">
                                <i class="fas fa-{{ transaction.icon|default:'circle' }} me-1"></i>
                                {% if transaction.action %}
                                    {{ transaction.action }}
                                {% elif transaction.transaction_type %}
                                    {{ transaction.get_transaction_type_display|default:transaction.transaction_type }}
                                {% else %}
                                    Activity
                                {% endif %}:
                                {% if transaction.quantity_change %}
                                    <span class="{% if transaction.quantity_change > 0 %}text-success{% else %}text-danger{% endif %}">
                                        {% if transaction.quantity_change > 0 %}+{% endif %}{{ transaction.quantity_change }}
                                    </span>
                                {% elif transaction.quantity %}
                                    <span class="{% if transaction.quantity > 0 %}text-success{% else %}text-danger{% endif %}">
                                        {% if transaction.quantity > 0 %}+{% endif %}{{ transaction.quantity }}
                                    </span>
                                {% endif %}
                                -
                                {% if transaction.timestamp %}
                                    {{ transaction.timestamp|timesince }} ago
                                {% elif transaction.created_at %}
                                    {{ transaction.created_at|timesince }} ago
                                {% else %}
                                    Recently
                                {% endif %}
                            </div>
                        {% endfor %}

                        <div class="text-center mt-3">
                            <!-- <a href="{% url 'goods_detail_with_activity' good.id %}" class="btn btn-primary btn-sm me-2">
                                <i class="fas fa-eye me-1"></i>View Details
                            </a> -->
                            <a href="{% url 'goods_activity_history' good.id %}" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-history me-1"></i>Read More
                            </a>
                        </div>
                    </div>
                {% else %}
                    <div class="recent-transactions">
                        <div class="text-center">
                            <small class="text-muted">No recent activity</small>
                            <div class="mt-2">
                                <a href="{% url 'goods_detail_with_activity' good.id %}" class="btn btn-primary btn-sm me-2">
                                    <i class="fas fa-eye me-1"></i>View Details
                                </a>
                                <a href="{% url 'goods_activity_history' good.id %}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-history me-1"></i>Read More
                                </a>
                            </div>
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>
        {% empty %}
        <div class="col-12 text-center py-5">
            <i class="fas fa-store fa-3x text-muted mb-3"></i>
            <h5>No Products in Your Store</h5>
            <p class="text-muted">Start building your inventory by adding products.</p>
            <a href="{% url 'goods_list_create' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-plus me-2"></i>Add Your First Product
            </a>
        </div>
        {% endfor %}
    </div>

    <!-- Properties for Sale Section -->
    {% if properties_for_sale %}
    <div class="mt-5">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3 class="text-primary">
                <i class="fas fa-home me-2"></i>Properties for Sale
            </h3>
            <a href="{% url 'property_management' %}" class="btn btn-outline-primary">
                <i class="fas fa-cog me-1"></i>Manage Properties
            </a>
        </div>

        <div class="row">
            {% for property in properties_for_sale %}
            <div class="col-lg-4 col-md-6 mb-4">
                <div class="card property-card h-100">
                    <!-- Property Image -->
                    <div class="property-image-container">
                        {% if property.images and property.images.0 %}
                            <img src="/media/{{ property.images.0 }}" class="card-img-top property-image" alt="{{ property.name }}" onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                        {% else %}
                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light property-image">
                                <i class="fas fa-home fa-3x text-muted"></i>
                            </div>
                        {% endif %}

                        <!-- Sale Badge -->
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-success">
                                <i class="fas fa-store me-1"></i>For Sale
                            </span>
                        </div>
                    </div>

                    <!-- Property Content -->
                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">{{ property.name }}</h5>
                        <p class="card-text text-muted small mb-2">
                            <i class="fas fa-tag me-1"></i>{{ property.get_property_type_display }}
                            {% if property.location %}
                                <span class="ms-2">
                                    <i class="fas fa-map-marker-alt me-1"></i>{{ property.location }}
                                </span>
                            {% endif %}
                        </p>

                        {% if property.sale_description %}
                        <p class="card-text">{{ property.sale_description|truncatewords:15 }}</p>
                        {% endif %}

                        <!-- Price -->
                        <div class="mt-auto">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h4 class="text-success mb-0">${{ property.sale_price|floatformat:0 }}</h4>
                                    {% if property.is_negotiable %}
                                        <small class="text-muted">Negotiable</small>
                                    {% endif %}
                                </div>
                                <div class="btn-group">
                                    <a href="{% url 'property_sale_detail' property.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'property_detail' property.id %}?edit=true" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Stock Management Modals -->
<div class="modal fade" id="stockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="stockModalTitle">Manage Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="stockForm">
                    <input type="hidden" id="goodsId" name="goods_id">
                    <input type="hidden" id="actionType" name="action">

                    <div class="mb-3">
                        <label for="quantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="quantity" name="quantity" min="1" required>
                    </div>

                    <div class="mb-3" id="unitPriceGroup" style="display: none;">
                        <label for="unitPrice" class="form-label">Unit Price (optional)</label>
                        <input type="number" class="form-control" id="unitPrice" name="unit_price" step="0.01">
                    </div>

                    <div class="mb-3" id="costPriceGroup" style="display: none;">
                        <label for="costPrice" class="form-label">Cost Price (optional)</label>
                        <input type="number" class="form-control" id="costPrice" name="cost_price" step="0.01">
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">Notes (optional)</label>
                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                    </div>

                    <div class="mb-3">
                        <label for="referenceNumber" class="form-label">Reference Number (optional)</label>
                        <input type="text" class="form-control" id="referenceNumber" name="reference_number">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmStockAction">Confirm</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class PrivateStoreManager {
    constructor() {
        this.stockModal = new bootstrap.Modal(document.getElementById('stockModal'));
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Form submission
        document.getElementById('filterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.applyFilters();
        });

        // Stock action confirmation
        document.getElementById('confirmStockAction').addEventListener('click', () => {
            this.processStockAction();
        });
    }

    sellStock(goodsId, goodsName) {
        this.openStockModal(goodsId, 'sell', `Sell Stock - ${goodsName}`);
        document.getElementById('unitPriceGroup').style.display = 'block';
        document.getElementById('costPriceGroup').style.display = 'none';
    }

    addStock(goodsId, goodsName) {
        this.openStockModal(goodsId, 'add', `Add Stock - ${goodsName}`);
        document.getElementById('unitPriceGroup').style.display = 'none';
        document.getElementById('costPriceGroup').style.display = 'block';
    }

    openStockModal(goodsId, action, title) {
        document.getElementById('stockModalTitle').textContent = title;
        document.getElementById('goodsId').value = goodsId;
        document.getElementById('actionType').value = action;

        // Reset form
        document.getElementById('stockForm').reset();
        document.getElementById('goodsId').value = goodsId;
        document.getElementById('actionType').value = action;

        this.stockModal.show();
    }

    async processStockAction() {
        const form = document.getElementById('stockForm');
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Validate quantity
        const quantity = parseInt(data.quantity);
        if (!quantity || quantity <= 0) {
            this.showAlert('danger', 'Please enter a valid quantity');
            return;
        }

        const confirmBtn = document.getElementById('confirmStockAction');
        const originalText = confirmBtn.textContent;
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';

        try {
            const response = await fetch('/private-seller-store/', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': this.getCookie('csrftoken'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('success', result.message);
                this.updateGoodsCard(data.goods_id, result.goods_data);
                this.stockModal.hide();
            } else {
                this.showAlert('danger', result.error || 'Operation failed');
            }
        } catch (error) {
            console.error('Error processing stock action:', error);
            this.showAlert('danger', 'An error occurred while processing the request');
        } finally {
            confirmBtn.disabled = false;
            confirmBtn.textContent = originalText;
        }
    }

    updateGoodsCard(goodsId, goodsData) {
        const card = document.querySelector(`[data-goods-id="${goodsId}"]`);
        if (card) {
            // Update stock overlay
            const stockOverlay = card.querySelector('.stock-overlay');
            if (stockOverlay) {
                stockOverlay.className = `stock-overlay ${goodsData.stock_status.replace('_', '')}`;
                stockOverlay.textContent = goodsData.stock_status_display;
            }

            // Update inventory info
            const inventoryValues = card.querySelectorAll('.inventory-value');
            if (inventoryValues.length >= 2) {
                inventoryValues[0].textContent = goodsData.new_quantity;
                inventoryValues[1].textContent = goodsData.units_sold;
            }

            // Update sell button state
            const sellBtn = card.querySelector('.btn-sell');
            if (sellBtn) {
                sellBtn.disabled = goodsData.is_out_of_stock;
            }

            // Add visual feedback
            card.style.transform = 'scale(1.02)';
            setTimeout(() => {
                card.style.transform = '';
            }, 300);
        }
    }

    showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        const container = document.querySelector('.container');
        container.insertAdjacentHTML('afterbegin', alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }

    applyFilters() {
        document.getElementById('filterForm').submit();
    }

    getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
}

// Global functions for onclick handlers
function sellStock(goodsId, goodsName) {
    storeManager.sellStock(goodsId, goodsName);
}

function addStock(goodsId, goodsName) {
    storeManager.addStock(goodsId, goodsName);
}

// Initialize the manager
let storeManager;
document.addEventListener('DOMContentLoaded', () => {
    storeManager = new PrivateStoreManager();
});
</script>
{% endblock %}
