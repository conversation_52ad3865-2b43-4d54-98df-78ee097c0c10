{% extends 'base.html' %}

{% block title %}Browse Categories{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-6xl mx-auto">
        <div class="text-center mb-8">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">Browse Categories</h2>
            <p class="text-lg text-gray-600">Discover products organized by categories</p>
        </div>
        
        {% if categories %}
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                {% for category in categories %}
                <div class="bg-white rounded-lg shadow-md hover:shadow-xl transition duration-300 group cursor-pointer">
                    <div class="p-6">
                        <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full mx-auto mb-4 group-hover:scale-110 transition duration-300">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-800 text-center mb-2 group-hover:text-blue-600 transition duration-200">
                            {{ category.name }}
                        </h3>
                        <div class="text-center">
                            <span class="inline-block bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                                Category ID: {{ category.id }}
                            </span>
                        </div>
                        <div class="mt-4 text-center">
                            <button class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200 opacity-0 group-hover:opacity-100">
                                View Products
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="bg-white rounded-lg shadow-md p-12 text-center">
                <div class="text-gray-500 mb-6">
                    <svg class="mx-auto h-24 w-24 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                </div>
                <h3 class="text-2xl font-medium text-gray-900 mb-4">No Categories Available</h3>
                <p class="text-gray-500 mb-6">There are currently no categories with products available.</p>
                <div class="text-sm text-gray-400">
                    Check back later for new categories and products!
                </div>
            </div>
        {% endif %}
        
        <div class="mt-12 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8">
            <div class="text-center">
                <h3 class="text-2xl font-semibold text-gray-800 mb-4">Why Categories Matter</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
                    <div class="text-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Easy Discovery</h4>
                        <p class="text-sm text-gray-600">Find products quickly by browsing organized categories</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Better Organization</h4>
                        <p class="text-sm text-gray-600">Products are neatly organized for better shopping experience</p>
                    </div>
                    <div class="text-center">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <h4 class="font-semibold text-gray-800 mb-2">Quick Navigation</h4>
                        <p class="text-sm text-gray-600">Navigate to specific product types with just one click</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}