{% extends 'base.html' %}

{% block title %}{{ goods.name }} - Product Details{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}">Dashboard</a></li>
            <li class="breadcrumb-item"><a href="{% url 'private_seller_store' %}">My Store</a></li>
            <li class="breadcrumb-item active">{{ goods.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Product Images -->
        <div class="col-md-6">
            <!-- Image Gallery Section -->
            {% if goods.first_image_url %}
                <!-- Main Image Display -->
                <div id="imageCarousel" class="carousel slide" data-bs-ride="carousel">
                    <div class="carousel-inner">
                        <div class="carousel-item active">
                            <img src="/media/{{ goods.first_image_url }}"
                                 class="d-block w-100 rounded"
                                 style="height: 300px; object-fit: cover;"
                                 alt="{{ goods.name }}"
                                 onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                        </div>
                    </div>
                </div>
            {% else %}
                <!-- No Image Placeholder -->
                <div class="d-flex align-items-center justify-content-center bg-light rounded" style="height: 300px;">
                    <div class="text-center">
                        <i class="fas fa-image fa-3x text-muted mb-2"></i>
                        <p class="text-muted">No images available</p>
                    </div>
                </div>
            {% endif %}
        </div>

        <!-- Product Information -->
        <div class="col-md-6">
            <h1 class="mb-3">{{ goods.name }}</h1>

            <div class="mb-4">
                <h2 class="text-success">{{ goods.currency_symbol }}{{ goods.price|floatformat:2 }}</h2>
            </div>

            {% if goods.description %}
            <div class="mb-4">
                <h5>Description</h5>
                <p class="text-muted">{{ goods.description }}</p>
            </div>
            {% endif %}

            <!-- Product Status Badges -->
            <div class="mb-3">
                {% if goods.is_used %}
                    <span class="badge bg-warning text-dark me-1">Used</span>
                {% else %}
                    <span class="badge bg-success me-1">New</span>
                {% endif %}

                {% if goods.available_for_delivery %}
                    <span class="badge bg-info me-1">Delivery Available</span>
                {% endif %}

                {% if goods.available_for_bulk_sales %}
                    <span class="badge bg-primary me-1">Bulk Sales</span>
                {% endif %}
            </div>

            <div class="mb-4">
                <h5>Details</h5>
                <ul class="list-unstyled">
                    <li><strong>Listed:</strong> {{ goods.created_at|date:"M d, Y H:i" }}</li>
                    {% if goods.category %}
                    <li><strong>Category:</strong> {{ goods.category.name }}</li>
                    {% endif %}
                    {% if goods.sku %}
                    <li><strong>SKU:</strong> {{ goods.sku }}</li>
                    {% endif %}
                    {% if goods.store %}
                    <li><strong>Store:</strong> {{ goods.store.name }}</li>
                    {% endif %}
                    {% if goods.quantity is not None %}
                    <li>
                        <strong>Available Quantity:</strong>
                        {% if goods.quantity == 0 %}
                            <span class="badge bg-danger">Out of Stock</span>
                        {% else %}
                            <span class="badge bg-success">{{ goods.quantity }} In Stock</span>
                        {% endif %}
                    </li>
                    {% endif %}
                </ul>
            </div>

            <!-- Action Buttons -->
            <div class="d-grid gap-2">
                <a href="{% url 'private_seller_store' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>Back to Store
                </a>
                <a href="/goods/{{ goods.id }}/" class="btn btn-primary">
                    <i class="fas fa-edit me-1"></i>Edit Product
                </a>
            </div>
        </div>
    </div>


    <!-- Activity History Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Activity History</h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" id="activityFilter" style="width: auto;">
                            <option value="">All Activities</option>
                            <option value="stock_add">Stock Added</option>
                            <option value="sale">Sales</option>
                            <option value="price_update">Price Updates</option>
                            <option value="adjustment">Adjustments</option>
                        </select>
                        <button class="btn btn-outline-primary btn-sm" onclick="exportActivity()">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="activity-timeline">
                        {% for activity in activities %}
                        <div class="activity-item mb-3 pb-3 {% if not forloop.last %}border-bottom{% endif %}" data-type="{{ activity.type }}">
                            <div class="d-flex align-items-start">
                                <div class="activity-icon me-3">
                                    <div class="rounded-circle p-2 {% if activity.type == 'stock_add' %}bg-success bg-opacity-10{% elif activity.type == 'sale' %}bg-primary bg-opacity-10{% elif activity.type == 'price_update' %}bg-warning bg-opacity-10{% else %}bg-info bg-opacity-10{% endif %}">
                                        <i class="fas fa-{{ activity.icon }} {% if activity.type == 'stock_add' %}text-success{% elif activity.type == 'sale' %}text-primary{% elif activity.type == 'price_update' %}text-warning{% else %}text-info{% endif %}"></i>
                                    </div>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <h6 class="mb-1">{{ activity.action }}</h6>
                                            <p class="text-muted mb-1">{{ activity.description }}</p>
                                            {% if activity.quantity_change != 0 %}
                                            <small class="{% if activity.quantity_change > 0 %}text-success{% else %}text-danger{% endif %}">
                                                <i class="fas fa-{% if activity.quantity_change > 0 %}plus{% else %}minus{% endif %} me-1"></i>
                                                {% if activity.quantity_change > 0 %}+{% endif %}{{ activity.quantity_change }} units
                                            </small>
                                            {% endif %}
                                        </div>
                                        <div class="text-end">
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                {{ activity.timestamp|timesince }} ago
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h6>No Activity Yet</h6>
                            <p class="text-muted">Activity for this product will appear here</p>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Management Section -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Stock Management</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="stock-display">
                            <span class="stock-number">{{ goods.quantity|default:0 }}</span>
                            <span class="stock-label">Units in Stock</span>
                        </div>
                        <span class="badge {% if goods.quantity == 0 %}bg-danger{% elif goods.is_low_stock %}bg-warning text-dark{% else %}bg-success{% endif %} mt-2">
                            {% if goods.quantity == 0 %}Out of Stock{% elif goods.is_low_stock %}Low Stock{% else %}In Stock{% endif %}
                        </span>
                    </div>

                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="addStock()">
                            <i class="fas fa-plus me-1"></i>Add Stock
                        </button>
                        <button class="btn btn-warning" onclick="sellStock()" {% if goods.quantity == 0 %}disabled{% endif %}>
                            <i class="fas fa-minus me-1"></i>Record Sale
                        </button>
                        <button class="btn btn-info" onclick="adjustStock()">
                            <i class="fas fa-edit me-1"></i>Adjust Stock
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Quick Stats</h5>
                </div>
                <div class="card-body">
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Total Value:</span>
                            <strong>{{ goods.currency_symbol }}{{ goods.total_value|default:0|floatformat:2 }}</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Units Sold:</span>
                            <strong>{{ goods.units_sold|default:0 }}</strong>
                        </div>
                    </div>
                    <div class="stat-item mb-3">
                        <div class="d-flex justify-content-between">
                            <span>Revenue:</span>
                            <strong>{{ goods.currency_symbol }}{{ goods.total_revenue|default:0|floatformat:2 }}</strong>
                        </div>
                    </div>
                    {% if goods.star_rating %}
                    <div class="stat-item">
                        <div class="d-flex justify-content-between">
                            <span>Rating:</span>
                            <strong>{{ goods.star_rating_display }}</strong>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.product-image-container {
    max-height: 300px;
    overflow: hidden;
}

.no-image-placeholder {
    height: 200px;
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.stock-display {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.stock-number {
    font-size: 3rem;
    font-weight: bold;
    color: #007bff;
}

.stock-label {
    font-size: 0.9rem;
    color: #6c757d;
}

.activity-icon {
    flex-shrink: 0;
}

.activity-icon .rounded-circle {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stat-item {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.stat-item:last-child {
    border-bottom: none;
}
</style>

<script>
function addStock() {
    alert('Add stock functionality will be implemented');
}

function sellStock() {
    alert('Record sale functionality will be implemented');
}

function adjustStock() {
    alert('Adjust stock functionality will be implemented');
}

function exportActivity() {
    window.location.href = '{% url "goods_activity_history" %}?goods_id={{ goods.id }}&export=csv';
}

// Activity filter
document.getElementById('activityFilter').addEventListener('change', function() {
    const filterValue = this.value;
    const activities = document.querySelectorAll('.activity-item');
    
    activities.forEach(activity => {
        if (!filterValue || activity.dataset.type === filterValue) {
            activity.style.display = 'block';
        } else {
            activity.style.display = 'none';
        }
    });
});
</script>
{% endblock %}
