{% extends 'base.html' %}

{% block title %}{{ property.name }} - Property Details{% endblock %}

{% block extra_css %}
<style>
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
}

body {
    background: #f8f9fa;
}

.page-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 0 0 20px 20px;
}

.property-detail-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.08);
    overflow: hidden;
    margin-bottom: 2rem;
}

.property-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem;
    text-align: center;
    position: relative;
}

.edit-toggle-btn {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(255,255,255,0.2);
    border: 2px solid rgba(255,255,255,0.3);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.edit-toggle-btn:hover {
    background: rgba(255,255,255,0.3);
    color: white;
}

.detail-section {
    padding: 2rem;
    border-bottom: 1px solid #e9ecef;
}

.detail-section h4 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.detail-label {
    font-weight: 600;
    color: #495057;
    flex: 1;
}

.detail-value {
    flex: 2;
    text-align: right;
    color: #6c757d;
}

.edit-form {
    display: none;
}

.edit-form.active {
    display: block;
}

.view-mode.editing {
    display: none;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.sale-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-top: 1rem;
    border: 2px solid #e9ecef;
}

.sale-section.active {
    background: #e8f5e8;
    border-color: var(--success-color);
}

.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
    display: inline-block;
}

.status-badge.for-sale {
    background: var(--success-color);
    color: white;
}

.status-badge.not-for-sale {
    background: #6c757d;
    color: white;
}

.action-buttons {
    padding: 2rem;
    background: #f8f9fa;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn-action {
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-success {
    background: var(--success-color);
    color: white;
}

.btn-danger {
    background: var(--danger-color);
    color: white;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.image-gallery img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.image-gallery img:hover {
    transform: scale(1.05);
}

@media (max-width: 768px) {
    .detail-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .detail-value {
        text-align: left;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .edit-toggle-btn {
        position: static;
        margin-top: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="page-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="page-title">{{ property.name }}</h1>
                <p class="page-subtitle">Property Details & Management</p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{% url 'property_management' %}" class="btn btn-outline-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Properties
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Property Detail Card -->
    <div class="property-detail-card">
        <!-- Property Header -->
        <div class="property-header">
            <button class="edit-toggle-btn" onclick="toggleEditMode()">
                <i class="fas fa-edit me-1"></i>
                <span id="editBtnText">Edit Property</span>
            </button>

            <h2>{{ property.name }}</h2>
            <div class="property-type-badge">
                <i class="fas fa-tag me-1"></i>{{ property.get_property_type_display }}
            </div>

            <!-- Status Badges -->
            <div class="mt-3">
                {% if property.for_sale %}
                    <span class="status-badge for-sale me-2">
                        <i class="fas fa-store me-1"></i>For Sale - ${{ property.sale_price|floatformat:0 }}
                    </span>
                {% else %}
                    <span class="status-badge not-for-sale me-2">
                        <i class="fas fa-home me-1"></i>Not For Sale
                    </span>
                {% endif %}
            </div>
        </div>

        <!-- Basic Information Section -->
        <div class="detail-section">
            <h4><i class="fas fa-info-circle"></i>Basic Information</h4>

            <!-- View Mode -->
            <div class="view-mode" id="basicInfoView">
                <div class="detail-row">
                    <span class="detail-label">Property Name</span>
                    <span class="detail-value">{{ property.name }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Type</span>
                    <span class="detail-value">{{ property.get_property_type_display }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Condition</span>
                    <span class="detail-value">{{ property.get_condition_display }}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">Location</span>
                    <span class="detail-value">{{ property.location|default:"Not specified" }}</span>
                </div>
                {% if property.description %}
                <div class="detail-row">
                    <span class="detail-label">Description</span>
                    <span class="detail-value">{{ property.description }}</span>
                </div>
                {% endif %}
            </div>

            <!-- Edit Mode -->
            <div class="edit-form" id="basicInfoEdit">
                <form id="propertyEditForm" enctype="multipart/form-data">
                    {% csrf_token %}
                    <input type="hidden" name="_method" value="PUT">

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="name" class="form-label">Property Name *</label>
                                <input type="text" class="form-control" id="name" name="name" value="{{ property.name }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="property_type" class="form-label">Property Type *</label>
                                <select class="form-control" id="property_type" name="property_type" required>
                                    <option value="house" {% if property.property_type == 'house' %}selected{% endif %}>House</option>
                                    <option value="apartment" {% if property.property_type == 'apartment' %}selected{% endif %}>Apartment</option>
                                    <option value="land" {% if property.property_type == 'land' %}selected{% endif %}>Land</option>
                                    <option value="commercial" {% if property.property_type == 'commercial' %}selected{% endif %}>Commercial</option>
                                    <option value="other" {% if property.property_type == 'other' %}selected{% endif %}>Other</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="condition" class="form-label">Condition</label>
                                <select class="form-control" id="condition" name="condition">
                                    <option value="excellent" {% if property.condition == 'excellent' %}selected{% endif %}>Excellent</option>
                                    <option value="good" {% if property.condition == 'good' %}selected{% endif %}>Good</option>
                                    <option value="fair" {% if property.condition == 'fair' %}selected{% endif %}>Fair</option>
                                    <option value="poor" {% if property.condition == 'poor' %}selected{% endif %}>Poor</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="location" class="form-label">Location</label>
                                <input type="text" class="form-control" id="location" name="location" value="{{ property.location|default:'' }}">
                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ property.description|default:'' }}</textarea>
                    </div>

                    <!-- Sale Information in Edit Mode -->
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="for_sale" name="for_sale" {% if property.for_sale %}checked{% endif %}>
                            <label class="form-check-label" for="for_sale">
                                <strong>Make Available for Sale</strong>
                            </label>
                        </div>
                    </div>

                    <div id="saleFields" class="sale-section {% if property.for_sale %}active{% endif %}" style="{% if not property.for_sale %}display: none;{% endif %}">
                        <div class="row">
                            <div class="col-md-8">
                                <div class="form-group">
                                    <label for="sale_price" class="form-label">Sale Price *</label>
                                    <div class="input-group">
                                        <span class="input-group-text">$</span>
                                        <input type="number" class="form-control" id="sale_price" name="sale_price" step="0.01" min="0" value="{{ property.sale_price|default:'' }}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="is_negotiable" name="is_negotiable" {% if property.is_negotiable %}checked{% endif %}>
                                        <label class="form-check-label" for="is_negotiable">
                                            Price is negotiable
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="sale_description" class="form-label">Sale Description</label>
                            <textarea class="form-control" id="sale_description" name="sale_description" rows="3" placeholder="Additional description for the sale listing...">{{ property.sale_description|default:'' }}</textarea>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_phone" class="form-label">Contact Phone</label>
                                    <input type="tel" class="form-control" id="contact_phone" name="contact_phone" placeholder="Phone number for inquiries" value="{{ property.contact_phone|default:'' }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="contact_email" class="form-label">Contact Email</label>
                                    <input type="email" class="form-control" id="contact_email" name="contact_email" placeholder="Email for inquiries" value="{{ property.contact_email|default:'' }}">
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Property Images -->
        {% if property.images %}
        <div class="detail-section">
            <h4><i class="fas fa-images"></i>Property Images</h4>

            <div class="image-gallery">
                {% for image in property.images %}
                    <img src="/media/{{ image }}" alt="{{ property.name }}" onclick="openImageModal('/media/{{ image }}', '{{ property.name }}')" onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <!-- Action Buttons -->
        <div class="action-buttons">
            <!-- View Mode Buttons -->
            <div class="view-mode" id="viewModeButtons">
                <button class="btn-action btn-primary" onclick="toggleEditMode()">
                    <i class="fas fa-edit"></i>Edit Property
                </button>

                {% if property.for_sale %}
                    <button class="btn-action btn-secondary" onclick="togglePropertySale({{ property.id }}, false)">
                        <i class="fas fa-store-slash"></i>Remove from Sale
                    </button>
                    <a href="{% url 'property_sale_detail' property.id %}" class="btn-action btn-success">
                        <i class="fas fa-eye"></i>View Sale Page
                    </a>
                {% else %}
                    <button class="btn-action btn-success" onclick="togglePropertySale({{ property.id }}, true)">
                        <i class="fas fa-store"></i>Make Available for Sale
                    </button>
                {% endif %}

                <button class="btn-action btn-danger" onclick="deleteProperty({{ property.id }}, '{{ property.name }}')">
                    <i class="fas fa-trash"></i>Delete Property
                </button>
            </div>

            <!-- Edit Mode Buttons -->
            <div class="edit-form" id="editModeButtons">
                <button type="submit" form="propertyEditForm" class="btn-action btn-success">
                    <i class="fas fa-save"></i>Save Changes
                </button>
                <button class="btn-action btn-secondary" onclick="cancelEdit()">
                    <i class="fas fa-times"></i>Cancel
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let editMode = false;

document.addEventListener('DOMContentLoaded', function() {
    // Check if we should start in edit mode or sale mode
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('edit') === 'true') {
        toggleEditMode();
    } else if (urlParams.get('sale') === 'true') {
        toggleEditMode();
        document.getElementById('for_sale').checked = true;
        toggleSaleFields();
    }

    // Sale fields toggle
    const forSaleCheckbox = document.getElementById('for_sale');
    if (forSaleCheckbox) {
        forSaleCheckbox.addEventListener('change', toggleSaleFields);
    }

    // Form submission
    const editForm = document.getElementById('propertyEditForm');
    if (editForm) {
        editForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch(window.location.pathname, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Property updated successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        });
    }
});

function toggleEditMode() {
    editMode = !editMode;

    const viewModes = document.querySelectorAll('.view-mode');
    const editForms = document.querySelectorAll('.edit-form');
    const editBtnText = document.getElementById('editBtnText');

    if (editMode) {
        viewModes.forEach(el => el.classList.add('editing'));
        editForms.forEach(el => el.classList.add('active'));
        if (editBtnText) editBtnText.textContent = 'Cancel Edit';
    } else {
        viewModes.forEach(el => el.classList.remove('editing'));
        editForms.forEach(el => el.classList.remove('active'));
        if (editBtnText) editBtnText.textContent = 'Edit Property';
    }
}

function cancelEdit() {
    editMode = false;
    const viewModes = document.querySelectorAll('.view-mode');
    const editForms = document.querySelectorAll('.edit-form');
    const editBtnText = document.getElementById('editBtnText');

    viewModes.forEach(el => el.classList.remove('editing'));
    editForms.forEach(el => el.classList.remove('active'));
    if (editBtnText) editBtnText.textContent = 'Edit Property';

    // Reset form
    document.getElementById('propertyEditForm').reset();
    location.reload();
}

function toggleSaleFields() {
    const forSaleCheckbox = document.getElementById('for_sale');
    const saleFields = document.getElementById('saleFields');
    const salePriceInput = document.getElementById('sale_price');

    if (forSaleCheckbox && saleFields) {
        if (forSaleCheckbox.checked) {
            saleFields.style.display = 'block';
            saleFields.classList.add('active');
            if (salePriceInput) salePriceInput.setAttribute('required', 'required');
        } else {
            saleFields.style.display = 'none';
            saleFields.classList.remove('active');
            if (salePriceInput) salePriceInput.removeAttribute('required');
        }
    }
}

function togglePropertySale(propertyId, makeForSale) {
    if (makeForSale) {
        // Enter edit mode and focus on sale section
        if (!editMode) {
            toggleEditMode();
        }
        document.getElementById('for_sale').checked = true;
        toggleSaleFields();
        document.getElementById('sale_price').focus();
    } else {
        // Remove from sale
        if (confirm('Are you sure you want to remove this property from sale?')) {
            const formData = new FormData();
            formData.append('for_sale', 'false');

            fetch(`/property/${propertyId}/toggle-sale/`, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Property removed from sale successfully!');
                    location.reload();
                } else {
                    alert('Error: ' + (data.error || 'Failed to update property'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while updating the property');
            });
        }
    }
}

function deleteProperty(propertyId, propertyName) {
    if (confirm(`Are you sure you want to delete "${propertyName}"? This action cannot be undone.`)) {
        fetch(`/properties/${propertyId}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Property deleted successfully!');
                window.location.href = '/property-management/';
            } else {
                alert('Error: ' + (data.error || 'Failed to delete property'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the property');
        });
    }
}

function openImageModal(imageSrc, imageTitle) {
    // Simple image modal
    const modal = document.createElement('div');
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0,0,0,0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        cursor: pointer;
    `;

    const img = document.createElement('img');
    img.src = imageSrc;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
    `;

    modal.appendChild(img);
    document.body.appendChild(modal);

    modal.addEventListener('click', function() {
        document.body.removeChild(modal);
    });
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
