# Inventory Management System

A comprehensive Django-based inventory management application that provides complete business management solutions including store management, goods tracking, receipt and invoice generation, and public marketplace functionality.

## 🚀 Features Overview

### 🔐 Authentication & User Management
- **User Registration & Login**: Secure user authentication with custom forms
- **Password Reset**: Email-based password reset functionality
- **User Profiles**: Extended user profiles with avatar, bio, company information
- **Multi-user Store Access**: Store owners can authorize other users to manage their stores

### 🏪 Store Management
- **Multi-Store Support**: Users can create and manage multiple stores
- **Store Authorization**: Add/remove authorized users for store management
- **Store Details**: Name, description, location, and activity status
- **Store-based Goods Organization**: Organize goods by store

### 📦 Goods & Inventory Management
- **Goods Management**: Add, edit, delete, and view goods with detailed information
- **Category Management**: Create and organize goods by categories
- **Image Support**: Multiple image uploads for goods (JSON-based storage)
- **Advanced Filtering**: Filter goods by name, price range, category, condition
- **Inventory Tracking**: Track quantities, units sold, and total values
- **Managed Goods**: Simplified inventory system with buy/sell functionality
- **Stock Alerts**: Automatic notifications for low stock or out-of-stock items

### 🧾 Receipt Management
- **Receipt Generation**: Create professional receipts with customizable templates
- **Receipt Templates**: Multiple HTML-based templates for different business needs
- **Digital Signatures & Stamps**: Upload and include signatures and stamps
- **Multi-Currency Support**: Support for different currencies with symbols
- **Tax & Discount Handling**: Calculate taxes and apply discounts
- **PDF Generation**: Generate PDF receipts for download
- **Email Receipts**: Send receipts directly to customers via email
- **Receipt Preview**: Preview receipts before finalizing

### 📄 Invoice Management
- **Professional Invoices**: Create detailed invoices with client information
- **Invoice Templates**: Customizable HTML templates for invoices
- **Client Management**: Store client details (name, email, address, phone)
- **Invoice Numbering**: Automatic unique invoice number generation
- **Due Date Tracking**: Set and track invoice due dates
- **Payment Terms**: Add custom payment terms and notes
- **Multi-Currency Invoicing**: Support for different currencies
- **PDF Export**: Generate professional PDF invoices
- **Email Invoices**: Send invoices directly to clients
- **Invoice Preview**: Preview invoices before sending

### 🌐 Public Marketplace
- **Public Goods Listing**: Display goods publicly for potential buyers
- **Advanced Search & Filtering**: Search by name, price, category, condition
- **Delivery Options**: Mark goods as available for delivery (within state/country/international)
- **Bulk Sales**: Mark goods as available for bulk purchases
- **Contact Information**: Display store and owner contact details
- **Share Links**: Generate shareable links for individual goods
- **Responsive Design**: Mobile-friendly public marketplace

### 📊 Dashboard & Analytics
- **Comprehensive Dashboard**: Overview of stores, goods, categories, receipts, and invoices
- **Real-time Statistics**: Live counts of stores, goods, categories, receipts, and invoices
- **Quick Actions**: Fast access to common management tasks
- **Notification System**: Real-time notifications for important events
- **Monthly Reports**: Track receipts and invoices by month

### 🔔 Notification System
- **Real-time Notifications**: Instant notifications for important events
- **Stock Alerts**: Notifications for low stock and out-of-stock items
- **Read/Unread Status**: Track notification status
- **Notification History**: View all past notifications

### 📧 Email Integration
- **SMTP Configuration**: Configurable email settings via environment variables
- **Receipt Emails**: Send receipts with PDF attachments
- **Invoice Emails**: Send invoices with PDF attachments
- **Password Reset Emails**: Automated password reset email system
- **Custom Email Templates**: HTML-based email templates

### 🎨 Template Management
- **Receipt Templates**: Pre-built HTML templates for receipts
- **Invoice Templates**: Professional invoice templates
- **Template Preview**: Preview templates before use
- **Custom Branding**: Include company logos, signatures, and stamps
- **Template Loading**: Management commands to load templates from files

## 🛠 Technical Features

### 🏗 Architecture
- **Django Framework**: Built on Django 5.2+ with modern best practices
- **SQLite Database**: Default SQLite with support for PostgreSQL/MySQL
- **REST API Ready**: Django REST Framework integration
- **JWT Authentication**: Token-based authentication support
- **Environment Configuration**: Environment-based settings management

### 📁 File Management
- **Media Handling**: Organized media storage for images, signatures, stamps
- **Image Processing**: JSON-based image storage for goods
- **File Upload**: Secure file upload handling
- **Static Files**: Optimized static file serving

### 🔒 Security Features
- **CSRF Protection**: Cross-site request forgery protection
- **User Authorization**: Role-based access control
- **Secure File Uploads**: Validated file upload handling
- **Environment Variables**: Secure configuration management

### 📱 Frontend Features
- **Responsive Design**: Mobile-first responsive design
- **Bootstrap Integration**: Modern UI with Bootstrap
- **AJAX Support**: Dynamic content loading
- **Form Validation**: Client and server-side validation
- **Interactive Dashboard**: Real-time dashboard updates

## 📋 Models & Database Structure

### Core Models
- **User**: Extended Django user model with profiles
- **UserProfile**: Additional user information (phone, avatar, bio, company)
- **Store**: Multi-store support with authorization
- **Category**: Goods categorization system
- **Goods**: Comprehensive goods management
- **ManagedGood**: Simplified inventory tracking

### Document Models
- **Receipt/ReceiptItem**: Receipt generation system
- **ReceiptTemplate**: Customizable receipt templates
- **Invoice/InvoiceItem**: Professional invoice system
- **InvoiceTemplate**: Customizable invoice templates

### System Models
- **Notification**: Real-time notification system

## 🚀 Getting Started

### Prerequisites
- Python 3.8+
- Django 5.2+
- SQLite (default) or PostgreSQL/MySQL

### Installation
1. Clone the repository
2. Install dependencies: `pip install -r requirements.txt`
3. Configure environment variables in `.env` file
4. Run migrations: `python manage.py migrate`
5. Load templates: `python manage.py load_receipt_templates` and `python manage.py load_invoice_templates`
6. Create superuser: `python manage.py createsuperuser`
7. Run server: `python manage.py runserver`

### Environment Configuration
Configure the following environment variables:
- `DJANGO_SECRET_KEY`: Django secret key
- `DJANGO_DEBUG`: Debug mode (True/False)
- `EMAIL_HOST`: SMTP server host
- `EMAIL_HOST_USER`: SMTP username
- `EMAIL_HOST_PASSWORD`: SMTP password
- Database configuration variables

## 📚 API Endpoints

### Authentication
- `POST /register/` - User registration
- `POST /login/` - User login
- `POST /logout/` - User logout
- `POST /forget-password/` - Password reset request
- `GET /auth/me/` - Get authenticated user info

### Store Management
- `GET|POST /stores/` - List/Create stores
- `GET|PUT|DELETE /stores/<id>/` - Store details/update/delete
- `POST /stores/<id>/add-users/` - Add authorized users
- `POST /stores/<id>/remove-users/` - Remove authorized users

### Goods Management
- `GET|POST /goods/` - List/Create goods
- `GET|PUT|DELETE /goods/<id>/` - Goods details/update/delete
- `GET /search-goods/` - Search and filter goods

### Category Management
- `GET|POST /categories/` - List/Create categories
- `GET|PUT|DELETE /categories/<id>/` - Category details/update/delete

### Receipt Management
- `GET|POST /receipts/` - List/Create receipts
- `GET /receipts/<id>/` - Receipt details
- `GET /receipts/<id>/preview/` - Receipt preview
- `POST /receipts/<id>/email/` - Email receipt

### Invoice Management
- `GET|POST /invoices/` - List/Create invoices
- `GET /invoices/<id>/` - Invoice details
- `GET /invoices/<id>/preview/` - Invoice preview
- `POST /invoices/<id>/email/` - Email invoice

### Public API
- `GET /public/goods/` - Public goods listing
- `GET /public/goods/<id>/` - Public goods details
- `GET /public/categories/` - Public categories

### Statistics API
- `GET /api/stores/count/` - Store count
- `GET /api/goods/count/` - Goods count
- `GET /api/categories/count/` - Categories count
- `GET /api/receipts/count/` - Monthly receipts count
- `GET /api/invoices/count/` - Monthly invoices count
