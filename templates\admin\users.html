{% extends 'base.html' %}

{% block title %}Admin - User Management{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.admin-nav {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.admin-nav .nav-link {
    color: #374151;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.admin-nav .nav-link:hover,
.admin-nav .nav-link.active {
    background: #dc2626;
    color: white;
}

.user-table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.user-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

.search-filters {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <h1 class="mb-2">
            <i class="fas fa-users me-3"></i>
            User Management
        </h1>
        <p class="mb-0 opacity-75">Manage all users, permissions, and access</p>
    </div>
</div>

<div class="container">
    <!-- Admin Navigation -->
    <div class="admin-nav">
        <ul class="nav nav-pills">
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_dashboard' %}">
                    <i class="fas fa-home me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'admin_users' %}">
                    <i class="fas fa-users me-2"></i>Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_goods' %}">
                    <i class="fas fa-box me-2"></i>Products
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_support_tickets' %}">
                    <i class="fas fa-ticket-alt me-2"></i>Support
                </a>
            </li>
        </ul>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="GET" class="row g-3">
            <div class="col-md-6">
                <label for="search" class="form-label">Search Users</label>
                <input type="text" class="form-control" id="search" name="q" 
                       value="{{ search_query }}" placeholder="Username, email, or name">
            </div>
            <div class="col-md-3">
                <label for="status" class="form-label">Status</label>
                <select class="form-select" id="status" name="status">
                    <option value="">All Users</option>
                    <option value="active" {% if status_filter == 'active' %}selected{% endif %}>Active</option>
                    <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>Inactive</option>
                    <option value="staff" {% if status_filter == 'staff' %}selected{% endif %}>Staff</option>
                </select>
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search me-1"></i>Search
                </button>
                <a href="{% url 'admin_users' %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times me-1"></i>Clear
                </a>
            </div>
        </form>
    </div>

    <!-- Users Table -->
    <div class="user-table">
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-dark">
                    <tr>
                        <th>User</th>
                        <th>Email</th>
                        <th>Status</th>
                        <th>Permissions</th>
                        <th>Joined</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr id="user-{{ user.id }}">
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar me-3">
                                    <i class="fas fa-user-circle fa-2x text-muted"></i>
                                </div>
                                <div>
                                    <strong>{{ user.username }}</strong>
                                    {% if user.first_name or user.last_name %}
                                        <small class="d-block text-muted">{{ user.get_full_name }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td>{{ user.email }}</td>
                        <td>
                            {% if user.is_active %}
                                <span class="badge bg-success status-badge">Active</span>
                            {% else %}
                                <span class="badge bg-danger status-badge">Banned</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.is_superuser %}
                                <span class="badge bg-danger status-badge">Superuser</span>
                            {% elif user.is_staff %}
                                <span class="badge bg-warning status-badge">Staff</span>
                            {% else %}
                                <span class="badge bg-secondary status-badge">User</span>
                            {% endif %}
                        </td>
                        <td>
                            <small>{{ user.date_joined|date:"M d, Y" }}</small>
                        </td>
                        <td>
                            <div class="user-actions">
                                {% if user.is_active %}
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="performUserAction('ban', {{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-ban"></i> Ban
                                    </button>
                                {% else %}
                                    <button class="btn btn-outline-success btn-sm" 
                                            onclick="performUserAction('unban', {{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-check"></i> Unban
                                    </button>
                                {% endif %}

                                {% if not user.is_staff %}
                                    <button class="btn btn-outline-warning btn-sm" 
                                            onclick="performUserAction('make_staff', {{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-user-tie"></i> Make Staff
                                    </button>
                                {% else %}
                                    <button class="btn btn-outline-secondary btn-sm" 
                                            onclick="performUserAction('remove_staff', {{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-user-minus"></i> Remove Staff
                                    </button>
                                {% endif %}

                                {% if not user.is_superuser %}
                                    <button class="btn btn-outline-info btn-sm" 
                                            onclick="performUserAction('make_superuser', {{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-crown"></i> Make Super
                                    </button>
                                {% elif user != request.user %}
                                    <button class="btn btn-outline-dark btn-sm" 
                                            onclick="performUserAction('remove_superuser', {{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-crown"></i> Remove Super
                                    </button>
                                {% endif %}

                                {% if not user.is_superuser %}
                                    <button class="btn btn-outline-danger btn-sm" 
                                            onclick="confirmDeleteUser({{ user.id }}, '{{ user.username }}')">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No users found</p>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if users.has_other_pages %}
        <div class="p-3 border-top">
            <nav aria-label="Users pagination">
                <ul class="pagination pagination-sm justify-content-center mb-0">
                    {% if users.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ users.previous_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Previous</a>
                        </li>
                    {% endif %}
                    
                    <li class="page-item active">
                        <span class="page-link">{{ users.number }} of {{ users.paginator.num_pages }}</span>
                    </li>
                    
                    {% if users.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ users.next_page_number }}{% if search_query %}&q={{ search_query }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">Next</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<script>
function performUserAction(action, userId, username) {
    const actionNames = {
        'ban': 'ban',
        'unban': 'unban',
        'make_staff': 'promote to staff',
        'remove_staff': 'remove staff privileges from',
        'make_superuser': 'promote to superuser',
        'remove_superuser': 'remove superuser privileges from',
        'delete_user': 'delete'
    };

    if (confirm(`Are you sure you want to ${actionNames[action]} user "${username}"?`)) {
        fetch('{% url "admin_users" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: `action=${action}&user_id=${userId}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            alert('An error occurred: ' + error);
        });
    }
}

function confirmDeleteUser(userId, username) {
    if (confirm(`⚠️ WARNING: This will permanently delete user "${username}" and all their data!\n\nThis action cannot be undone. Are you absolutely sure?`)) {
        performUserAction('delete_user', userId, username);
    }
}
</script>
{% endblock %}
