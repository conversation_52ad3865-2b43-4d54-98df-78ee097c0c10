{% extends "base.html" %}

{% block title %}My Receipts{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Your Receipts</h2>
        <a href="{% url 'create_receipt' %}" class="btn btn-primary">Create New Receipt</a>
    </div>

    <!-- Date filter -->
    <form method="get" class="d-flex mb-3">
        <input type="date" name="created_at" class="form-control" value="{{ created_at_filter }}" placeholder="Filter by date">
        <button type="submit" class="btn btn-outline-secondary ms-2">Filter by Date</button>
        {% if created_at_filter %}
            <a href="{% url 'list_user_receipts' %}" class="btn btn-outline-danger ms-2">Clear Filter</a>
        {% endif %}
    </form>
    
    {% if created_at_filter %}
        <div class="alert alert-info">
            <i class="fas fa-filter me-2"></i>
            Showing receipts from {{ created_at_filter }}
            <span class="badge bg-primary ms-2">{{ total_receipts }} receipt{{ total_receipts|pluralize }}</span>
        </div>
    {% endif %}

    <table class="table table-striped mt-3">
        <thead>
            <tr>
                <th>ID</th>
                <th>Template</th>
                <th>Total Price</th>
                <th>Created At</th>
                <th>Updated At</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="receiptList">
            <!-- Receipts will load here -->
            {% for receipt in page_obj %}
            <tr>
                <td>{{ receipt.id }}</td>
                <td>{{ receipt.template.name }}</td>
                <td>{{ receipt.currency_symbol }}{{ receipt.total_price|floatformat:2 }}</td>
                <td>{{ receipt.created_at|date:"M d, Y H:i" }}</td>
                <td>{{ receipt.updated_at|date:"M d, Y H:i" }}</td>
                <td>
                    <a href="/receipt-templates/{{ receipt.id }}/?receipt=1" target="_blank" class="btn btn-primary btn-sm me-2">Preview</a>
                    <a href="/receipts/{{ receipt.id }}/download/" class="btn btn-primary btn-sm me-2">Download PDF</a>
                    <button class="btn btn-outline-secondary btn-sm" onclick="openEmailModal({{ receipt.id }})">Send Email</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Pagination controls -->
    {% if page_obj.paginator.num_pages > 1 %}
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <span>Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} receipts</span>
        </div>
        <div class="d-flex align-items-center">
            <!-- Page navigation -->
            <nav aria-label="Receipt pagination">
                <ul class="pagination mb-0 me-3">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}
                    
                    <!-- Show page numbers -->
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            
            <!-- Direct page input -->
            <div class="input-group" style="width: 120px;">
                <input type="number" class="form-control form-control-sm" id="pageInput" 
                       placeholder="Page" min="1" max="{{ page_obj.paginator.num_pages }}" 
                       value="{{ page_obj.number }}">
                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="goToPage()">Go</button>
            </div>
        </div>
    </div>
    {% endif %}

    <p id="noReceiptsMsg" style="display:none;">No receipts found. <a href="{% url 'create_receipt' %}">Create your first receipt</a>.</p>
</div>

<!-- Send Receipt Email Modal -->
<div class="modal fade" id="sendReceiptEmailModal" tabindex="-1" aria-labelledby="sendReceiptEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendReceiptEmailModalLabel">Send Receipt via Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="sendReceiptEmailForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="recipientEmail" class="form-label">Recipient's Email *</label>
                        <input type="email" class="form-control" id="recipientEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="customMessage" class="form-label">Custom Message (optional)</label>
                        <textarea class="form-control" id="customMessage" name="message" rows="3" placeholder="Enter a custom message to include with the receipt..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        The receipt will be sent as a PDF attachment.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>Send Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Alert Container for notifications -->
<div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>

<script>
async function loadReceipts() {
    try {
        // Get current URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const currentPage = urlParams.get('page') || '1';
        const createdAtFilter = urlParams.get('created_at') || '';
        
        // Build the API URL with current parameters
        let apiUrl = "{% url 'list_user_receipts' %}?format=json";
        if (currentPage !== '1') {
            apiUrl += `&page=${currentPage}`;
        }
        if (createdAtFilter) {
            apiUrl += `&created_at=${createdAtFilter}`;
        }
        
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error('Network response was not ok');
        const data = await response.json();

        const receiptList = document.getElementById("receiptList");
        const noReceiptsMsg = document.getElementById("noReceiptsMsg");
        
        // Clear existing content
        receiptList.innerHTML = "";

        // Check if we have receipts data
        const receipts = data.receipts || data; // Handle both new and old response formats
        
        if (receipts.length === 0) {
            noReceiptsMsg.style.display = "block";
            return;
        }
        noReceiptsMsg.style.display = "none";

        receipts.forEach(receipt => {
            const row = document.createElement("tr");
            row.innerHTML = `
                <td>${receipt.id}</td>
                <td>${receipt.template_name}</td>
                <td>${receipt.currency_symbol}${receipt.total_price.toFixed(2)}</td>
                <td>${new Date(receipt.created_at).toLocaleDateString()}</td>
                <td>${new Date(receipt.updated_at).toLocaleString()}</td>
                <td>
                    <a href="/receipt-templates/${receipt.id}/?receipt=1" target="_blank" class="btn btn-primary btn-sm me-2">Preview</a>
                    <a href="/receipts/${receipt.id}/download/" class="btn btn-primary btn-sm me-2">Download PDF</a>
                    <button class="btn btn-outline-white btn-primary btn-sm me-2" onclick="openEmailModal(${receipt.id})">Send Email</button>
                </td>
            `;
            receiptList.appendChild(row);
        });
    } catch (error) {
        console.error("Error loading receipts:", error);
        document.getElementById("receiptList").innerHTML = `<tr><td colspan="6">Failed to load receipts.</td></tr>`;
    }
}

function goToPage() {
    const pageInput = document.getElementById('pageInput');
    const pageNumber = parseInt(pageInput.value);
    const maxPages = {{ page_obj.paginator.num_pages }};
    
    if (pageNumber >= 1 && pageNumber <= maxPages) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('page', pageNumber);
        window.location.search = urlParams.toString();
    } else {
        alert(`Please enter a valid page number between 1 and ${maxPages}`);
        pageInput.value = {{ page_obj.number }};
    }
}

function openEmailModal(receiptId) {
    // Store the receipt ID for later use
    document.getElementById('sendReceiptEmailForm').dataset.receiptId = receiptId;
    
    // Clear previous form data
    document.getElementById('recipientEmail').value = '';
    document.getElementById('customMessage').value = '';
    
    // Open the modal
    const modal = new bootstrap.Modal(document.getElementById('sendReceiptEmailModal'));
    modal.show();
}

function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-remove alert after 5 seconds
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.getElementById('sendReceiptEmailForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const receiptId = this.dataset.receiptId;
    const email = document.getElementById('recipientEmail').value;
    const message = document.getElementById('customMessage').value;
    const submitButton = this.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;

    // Disable button and show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';

    try {
        const response = await fetch(`/receipts/${receiptId}/email/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify({ 
                email: email, 
                message: message 
            })
        });
        
        const result = await response.json();

        if (response.ok) {
            showAlert('Receipt sent successfully!', 'success');
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('sendReceiptEmailModal'));
            modal.hide();
            
            // Reset form
            this.reset();
        } else {
            showAlert('Error sending receipt: ' + (result.error || 'Unknown error'), 'danger');
        }
    } catch (err) {
        console.error('Error sending receipt:', err);
        showAlert('Error sending the receipt. Please try again.', 'danger');
    } finally {
        // Re-enable button and restore original text
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    }
});

document.addEventListener("DOMContentLoaded", loadReceipts);
</script>

{% endblock %}