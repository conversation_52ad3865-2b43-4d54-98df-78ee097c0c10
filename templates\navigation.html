<!-- Navigation Bar for Goods Management -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark mb-4">
    <div class="container-fluid">
        <a class="navbar-brand" href="{% url 'goods-management' %}">
            <i class="fas fa-boxes"></i> Goods Manager
        </a>
        
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {% if request.resolver_match.url_name == 'goods-management' %}active{% endif %}" 
                       href="{% url 'goods-management' %}">
                        <i class="fas fa-home"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'goods-management' %}" 
                       onclick="document.getElementById('addGoodBtn')?.click(); return false;">
                        <i class="fas fa-plus"></i> Add Good
                    </a>
                </li>
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" 
                       data-bs-toggle="dropdown">
                        <i class="fas fa-cog"></i> Actions
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'goods-management' %}">
                            <i class="fas fa-list"></i> View All Goods
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="showLowStockItems()">
                            <i class="fas fa-exclamation-triangle text-warning"></i> Low Stock Items
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="showOutOfStockItems()">
                            <i class="fas fa-times-circle text-danger"></i> Out of Stock Items
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" onclick="openQuickEmailShare()">
                            <i class="fas fa-envelope text-success"></i> Email Share Store
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="exportData()">
                            <i class="fas fa-download"></i> Export Data
                        </a></li>
                    </ul>
                </li>
            </ul>
            
            <ul class="navbar-nav">
                <li class="nav-item">
                    <span class="navbar-text me-3">
                        <i class="fas fa-dollar-sign"></i> 
                        Total Value: <span id="navTotalValue">${{ total_value|floatformat:2|default:"0.00" }}</span>
                    </span>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#" onclick="refreshTotalValue()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </a>
                </li>
                <li class="nav-item">
                    <form method="post" action="{% url 'logout' %}" style="display: inline;">
                        {% csrf_token %}
                        <button type="submit" class="nav-link border-0 bg-transparent text-light">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </button>
                    </form>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Quick Action Floating Buttons -->
<div class="floating-actions">
    <div class="fab-container">
        <button class="fab fab-main" type="button" data-bs-toggle="collapse" data-bs-target="#fabMenu">
            <i class="fas fa-plus"></i>
        </button>
        <div class="collapse" id="fabMenu">
            <div class="fab-menu">
                <a href="{% url 'goods-management' %}" class="fab fab-item" title="Dashboard">
                    <i class="fas fa-home"></i>
                </a>
                <button class="fab fab-item" onclick="triggerAddGood()" title="Add Good">
                    <i class="fas fa-plus"></i>
                </button>
                <button class="fab fab-item" onclick="showQuickStats()" title="Quick Stats">
                    <i class="fas fa-chart-bar"></i>
                </button>
                <button class="fab fab-item" onclick="showLowStockItems()" title="Low Stock Alert">
                    <i class="fas fa-exclamation-triangle"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Breadcrumb Navigation -->
<nav aria-label="breadcrumb" class="mb-3">
    <ol class="breadcrumb">
        <li class="breadcrumb-item">
            <a href="{% url 'goods-management' %}">
                <i class="fas fa-home"></i> Home
            </a>
        </li>
        {% if request.resolver_match.url_name == 'single-good' %}
            <li class="breadcrumb-item">
                <a href="{% url 'goods-management' %}">Goods Management</a>
            </li>
            <li class="breadcrumb-item active">{{ good.name|default:"Good Details" }}</li>
        {% elif request.resolver_match.url_name == 'goods-management' %}
            <li class="breadcrumb-item active">Goods Management</li>
        {% endif %}
    </ol>
</nav>

<!-- Quick Stats Modal -->
<div class="modal fade" id="quickStatsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Quick Statistics</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h4 id="totalGoods">0</h4>
                                <p>Total Goods</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h4 id="inStockGoods">0</h4>
                                <p>In Stock</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h4 id="lowStockGoods">0</h4>
                                <p>Low Stock</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-danger text-white">
                            <div class="card-body">
                                <h4 id="outOfStockGoods">0</h4>
                                <p>Out of Stock</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body">
                                <h6>Total Inventory Value</h6>
                                <h3 class="text-success" id="modalTotalValue">$0.00</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Floating Action Button Styles */
.floating-actions {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.fab-container {
    position: relative;
}

.fab {
    width: 56px;
    height: 56px;
    border-radius: 50%;
    border: none;
    color: white;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
}

.fab-main {
    background: #007bff;
    position: relative;
    z-index: 1001;
}

.fab-main:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.fab-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.fab-item {
    background: #28a745;
    opacity: 0;
    transform: translateY(20px);
    animation: fabSlideIn 0.3s ease forwards;
}

.fab-item:nth-child(1) { animation-delay: 0.1s; }
.fab-item:nth-child(2) { animation-delay: 0.2s; }
.fab-item:nth-child(3) { animation-delay: 0.3s; }
.fab-item:nth-child(4) { animation-delay: 0.4s; }

.fab-item:hover {
    background: #1e7e34;
    transform: scale(1.1);
}

@keyframes fabSlideIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .floating-actions {
        bottom: 20px;
        right: 20px;
    }
    
    .fab {
        width: 48px;
        height: 48px;
        font-size: 16px;
    }
}

/* Active navigation link */
.nav-link.active {
    background-color: rgba(255,255,255,0.1);
    border-radius: 0.25rem;
}

/* Breadcrumb styling */
.breadcrumb {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
}
</style>

<script>
// Navigation JavaScript Functions
function triggerAddGood() {
    // Try to trigger the add good modal
    const addButton = document.querySelector('[data-bs-target="#addGoodModal"]');
    if (addButton) {
        addButton.click();
    } else {
        // If not on the main page, redirect there
        window.location.href = '{% url "goods-management" %}';
    }
}

function showLowStockItems() {
    // Filter table to show only low stock items
    const table = document.querySelector('table tbody');
    if (table) {
        const rows = table.querySelectorAll('tr');
        let lowStockCount = 0;
        
        rows.forEach(row => {
            const quantityBadge = row.querySelector('.badge');
            if (quantityBadge && quantityBadge.classList.contains('bg-warning')) {
                row.style.display = '';
                lowStockCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        if (lowStockCount === 0) {
            alert('No low stock items found!');
            rows.forEach(row => row.style.display = '');
        }
    }
}

function showOutOfStockItems() {
    // Filter table to show only out of stock items
    const table = document.querySelector('table tbody');
    if (table) {
        const rows = table.querySelectorAll('tr');
        let outOfStockCount = 0;
        
        rows.forEach(row => {
            const quantityBadge = row.querySelector('.badge');
            if (quantityBadge && quantityBadge.classList.contains('bg-danger')) {
                row.style.display = '';
                outOfStockCount++;
            } else {
                row.style.display = 'none';
            }
        });
        
        if (outOfStockCount === 0) {
            alert('No out of stock items found!');
            rows.forEach(row => row.style.display = '');
        }
    }
}

function showQuickStats() {
    // Calculate and show quick statistics
    const table = document.querySelector('table tbody');
    if (table) {
        const rows = table.querySelectorAll('tr');
        let totalGoods = 0;
        let inStock = 0;
        let lowStock = 0;
        let outOfStock = 0;
        
        rows.forEach(row => {
            if (row.style.display !== 'none') {
                totalGoods++;
                const badge = row.querySelector('.badge');
                if (badge) {
                    if (badge.classList.contains('bg-danger')) outOfStock++;
                    else if (badge.classList.contains('bg-warning')) lowStock++;
                    else if (badge.classList.contains('bg-success')) inStock++;
                }
            }
        });
        
        // Update modal content
        document.getElementById('totalGoods').textContent = totalGoods;
        document.getElementById('inStockGoods').textContent = inStock;
        document.getElementById('lowStockGoods').textContent = lowStock;
        document.getElementById('outOfStockGoods').textContent = outOfStock;
        
        // Get total value from the page
        const totalValueElement = document.querySelector('.card-text h3');
        if (totalValueElement) {
            document.getElementById('modalTotalValue').textContent = totalValueElement.textContent;
        }
        
        // Show modal
        new bootstrap.Modal(document.getElementById('quickStatsModal')).show();
    }
}

function refreshTotalValue() {
    // Refresh the total value by making an AJAX call
    fetch('{% url "net-total-value" %}')
        .then(response => response.json())
        .then(data => {
            const navTotalValue = document.getElementById('navTotalValue');
            if (navTotalValue) {
                navTotalValue.textContent = '$' + data.net_total_value.toFixed(2);
            }
            
            // Also update the main total value if present
            const mainTotalValue = document.querySelector('.card-text h3');
            if (mainTotalValue) {
                mainTotalValue.textContent = '$' + data.net_total_value.toFixed(2);
            }
            
            // Show success message
            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; width: 300px;';
            alertDiv.innerHTML = `
                Total value refreshed successfully!
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // Auto remove after 3 seconds
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 3000);
        })
        .catch(error => {
            console.error('Error refreshing total value:', error);
        });
}

function exportData() {
    // Simple CSV export functionality
    const table = document.querySelector('table');
    if (!table) {
        alert('No data to export!');
        return;
    }
    
    let csv = 'Name,Description,Price,Quantity,Units Sold,Category,Total Value\n';
    
    const rows = table.querySelectorAll('tbody tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 7) {
            const rowData = [
                cells[0].textContent.trim(),
                cells[1].textContent.trim(),
                cells[2].textContent.trim(),
                cells[3].textContent.trim(),
                cells[4].textContent.trim(),
                cells[5].textContent.trim(),
                cells[6].textContent.trim()
            ];
            csv += rowData.map(field => `"${field}"`).join(',') + '\n';
        }
    });
    
    // Create and download CSV file
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'goods_inventory_' + new Date().toISOString().split('T')[0] + '.csv';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Reset table filters
function resetFilters() {
    const table = document.querySelector('table tbody');
    if (table) {
        const rows = table.querySelectorAll('tr');
        rows.forEach(row => row.style.display = '');
    }
}

// Add event listener for escape key to reset filters
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        resetFilters();
    }
});

// Quick Email Share from Navigation
function openQuickEmailShare() {
    // Check if we're on a page that has the dashboard email share function
    if (typeof openEmailShareModal === 'function') {
        openEmailShareModal();
    } else {
        // Redirect to dashboard with email share trigger
        window.location.href = '/dashboard/?action=email_share';
    }
}
</script>