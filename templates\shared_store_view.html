{% extends 'base.html' %}

{% block title %}{{ seller.first_name }} {{ seller.last_name }}'s Store{% endblock %}

{% block extra_css %}
<style>
/* Compact Collapsible Sidebar Design */
.filter-sidebar {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    position: sticky;
    top: 10px;
    height: fit-content;
    transition: all 0.3s ease;
}

.sidebar-toggle {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    margin-bottom: 1rem;
    width: 100%;
    transition: all 0.2s ease;
}

.sidebar-toggle:hover {
    background: #5a6fd8;
    color: white;
}

.sidebar-content {
    transition: all 0.3s ease;
}

.sidebar-content.collapsed {
    display: none;
}

.filter-section {
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.filter-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.filter-title {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.price-range-inputs {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 0.5rem;
    align-items: center;
}

.price-range-inputs input {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

.filter-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.4rem;
}

.filter-checkbox input[type="checkbox"],
.filter-checkbox input[type="radio"] {
    margin: 0;
    transform: scale(0.9);
}

.filter-checkbox label {
    margin: 0;
    font-size: 0.8rem;
    color: #495057;
    cursor: pointer;
}

.form-control, .form-select {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 6px;
}

.results-header {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
}

.seller-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    text-align: center;
}

.seller-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 1rem;
    background: rgba(255,255,255,0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.seller-name {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.seller-bio {
    opacity: 0.9;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.store-stats {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 1.2rem;
    font-weight: 700;
    display: block;
}

.stat-label {
    font-size: 0.7rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.shared-badge {
    background: rgba(255,255,255,0.2);
    padding: 0.4rem 0.8rem;
    border-radius: 15px;
    font-size: 0.7rem;
    margin-top: 0.75rem;
    display: inline-block;
}

.product-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.08);
    transition: all 0.2s ease;
    overflow: hidden;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.product-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.product-image-container {
    position: relative;
    height: 140px;
    overflow: hidden;
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-placeholder {
    width: 100%;
    height: 100%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.product-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 0.85rem;
    line-height: 1.3;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.price-current {
    font-size: 1rem;
    font-weight: 700;
    color: #27ae60;
}

.product-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0.5rem 0;
    flex-wrap: wrap;
    gap: 0.4rem;
}

.condition-badge {
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 500;
}

.condition-new {
    background: #d4edda;
    color: #155724;
}

.condition-used {
    background: #fff3cd;
    color: #856404;
}

.delivery-badge {
    background: #cce5ff;
    color: #004085;
    padding: 0.2rem 0.4rem;
    border-radius: 8px;
    font-size: 0.6rem;
    font-weight: 500;
}

.product-actions {
    margin-top: auto;
    padding: 0.75rem;
    display: flex;
    gap: 0.4rem;
}

.btn-action {
    flex: 1;
    padding: 0.4rem;
    border-radius: 5px;
    font-size: 0.7rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1rem;
}

@media (max-width: 768px) {
    .products-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
        gap: 0.75rem;
    }
    
    .store-stats {
        gap: 1rem;
    }
    
    .seller-name {
        font-size: 1.3rem;
    }
    
    .filter-sidebar {
        margin-bottom: 1rem;
    }
}

@media (max-width: 576px) {
    .products-grid {
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }
    
    .product-title {
        font-size: 0.8rem;
    }
    
    .price-current {
        font-size: 0.9rem;
    }
    
    .btn-action {
        font-size: 0.65rem;
        padding: 0.3rem;
    }
}

/* Scroll to Top Button */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #2563eb;
    color: white;
    border: none;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.scroll-to-top.show {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background: #1e40af;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
}

@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-3">
    <!-- Compact Seller Information Header -->
    <div class="seller-info">
        <div class="seller-avatar">
            <i class="fas fa-user"></i>
        </div>
        <h1 class="seller-name">{{ seller.first_name }} {{ seller.last_name }}</h1>
        {% if seller.userprofile.bio %}
        <p class="seller-bio">{{ seller.userprofile.bio }}</p>
        {% endif %}
        
        <div class="store-stats">
            <div class="stat-item">
                <span class="stat-number">{{ total_goods }}</span>
                <span class="stat-label">Products</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ total_stores }}</span>
                <span class="stat-label">Stores</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ store_share.view_count }}</span>
                <span class="stat-label">Views</span>
            </div>
        </div>
        
        <div class="shared-badge">
            <i class="fas fa-share-alt me-1"></i>
            Shared Store
        </div>
        
        <!-- Compact Share Store Button -->
        <div class="mt-2">
            <button class="btn btn-light btn-sm" onclick="shareStore()" style="font-size: 0.8rem; padding: 0.4rem 0.8rem;">
                <i class="fas fa-share me-1"></i>Share Store
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Collapsible Filter Sidebar -->
        <div class="col-lg-3 mb-3">
            <div class="filter-sidebar">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-filter me-1"></i>
                    <span id="toggleText">Hide Filters</span>
                </button>
                
                <div class="sidebar-content" id="sidebarContent">
                    <form method="GET" id="filterForm">
                        <!-- Search -->
                        <div class="filter-section">
                            <div class="filter-title">🔍 Search</div>
                            <input type="text" name="q" class="form-control" placeholder="Search products..." value="{{ filters.search_query }}">
                        </div>

                        <!-- Categories -->
                        {% if categories %}
                        <div class="filter-section">
                            <div class="filter-title">📂 Categories</div>
                            <select name="category" class="form-select">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if filters.category == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}

                        <!-- Price Range -->
                        <div class="filter-section">
                            <div class="filter-title">💰 Price Range</div>
                            <div class="price-range-inputs">
                                <input type="number" name="min_price" class="form-control" placeholder="Min" value="{{ filters.min_price }}" step="0.01">
                                <span class="text-muted" style="font-size: 0.7rem;">to</span>
                                <input type="number" name="max_price" class="form-control" placeholder="Max" value="{{ filters.max_price }}" step="0.01">
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Products Grid -->
        <div class="col-lg-9">
            <!-- Compact Results Header -->
            <div class="results-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0" style="font-size: 1rem;">Products ({{ total_goods }})</h5>
                    <div class="d-flex gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="contactSeller()" style="font-size: 0.8rem; padding: 0.4rem 0.8rem;">
                            <i class="fas fa-envelope me-1"></i>Contact
                        </button>
                    </div>
                </div>
            </div>

            <!-- Compact Products Grid -->
            {% if goods %}
            <div class="products-grid">
                {% for product in goods %}
                <div class="product-card">
                    <div class="product-image-container">
                        {% if product.first_image_url %}
                            <img src="/media/{{ product.first_image_url }}" class="product-image" alt="{{ product.name }}"
                                 onerror="this.parentElement.innerHTML='<div class=&quot;product-placeholder&quot;><i class=&quot;fas fa-image fa-2x text-muted&quot;></i></div>'">
                        {% else %}
                            <div class="product-placeholder">
                                <i class="fas fa-image fa-2x text-muted"></i>
                            </div>
                        {% endif %}
                    </div>

                    <div class="p-2 flex-grow-1 d-flex flex-column">
                        <h6 class="product-title">{{ product.name }}</h6>
                        <div class="price-current mb-2">{{ product.currency_symbol }}{{ product.price|floatformat:2 }}</div>

                        <div class="product-meta">
                            <span class="condition-badge {% if product.is_used %}condition-used{% else %}condition-new{% endif %}">
                                {% if product.is_used %}Used{% else %}New{% endif %}
                            </span>
                            {% if product.available_for_delivery %}
                            <span class="delivery-badge">Delivery</span>
                            {% endif %}
                        </div>

                        {% if product.description %}
                        <p class="text-muted small mb-2" style="display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; font-size: 0.7rem;">
                            {{ product.description }}
                        </p>
                        {% endif %}

                        <div class="product-actions">
                            <button class="btn btn-primary btn-action" onclick="inquireAboutProduct('{{ product.id }}', '{{ product.name }}', '{{ seller.username }}')">
                                <i class="fas fa-envelope"></i>
                            </button>
                            <button class="btn btn-outline-secondary btn-action" onclick="viewProductDetails('{{ product.id }}')">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="btn btn-outline-info btn-action" onclick="shareProduct('{{ product.id }}', '{{ product.name }}')">
                                <i class="fas fa-share"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-box-open fa-2x text-muted mb-2"></i>
                <h6>No Products Found</h6>
                <p class="text-muted small">This seller hasn't added any products yet or no products match your filters.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Scroll to Top Button -->
<button id="scrollToTop" class="scroll-to-top" title="Scroll to top">
    <i class="fas fa-chevron-up"></i>
</button>

{% endblock %}

{% block extra_js %}
<script>
// Sidebar toggle functionality
function toggleSidebar() {
    const content = document.getElementById('sidebarContent');
    const toggleText = document.getElementById('toggleText');
    const isCollapsed = content.classList.contains('collapsed');

    if (isCollapsed) {
        content.classList.remove('collapsed');
        toggleText.textContent = 'Hide Filters';
    } else {
        content.classList.add('collapsed');
        toggleText.textContent = 'Show Filters';
    }
}

// Compact inquire functionality
function inquireAboutProduct(productId, productName, sellerUsername) {
    const modalHTML = `
        <div class="modal fade" id="inquiryModal" tabindex="-1">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header py-2">
                        <h6 class="modal-title">Inquire: ${productName}</h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body py-2">
                        <form id="inquiryForm">
                            <div class="mb-2">
                                <label for="inquirerName" class="form-label small">Your Name</label>
                                <input type="text" class="form-control form-control-sm" id="inquirerName" required>
                            </div>
                            <div class="mb-2">
                                <label for="inquirerEmail" class="form-label small">Your Email</label>
                                <input type="email" class="form-control form-control-sm" id="inquirerEmail" required>
                            </div>
                            <div class="mb-2">
                                <label for="inquiryMessage" class="form-label small">Message</label>
                                <textarea class="form-control form-control-sm" id="inquiryMessage" rows="3"
                                          placeholder="I'm interested in this product...">${'I am interested in "' + productName + '". Please provide more details.'}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer py-2">
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary btn-sm" onclick="submitInquiry('${productId}', '${productName}', '${sellerUsername}')">
                            Send
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    const existingModal = document.getElementById('inquiryModal');
    if (existingModal) existingModal.remove();

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('inquiryModal'));
    modal.show();
}

function submitInquiry(productId, productName, sellerUsername) {
    const inquiryData = {
        product_id: productId,
        product_name: productName,
        seller_username: sellerUsername,
        inquirer_name: document.getElementById('inquirerName').value,
        inquirer_email: document.getElementById('inquirerEmail').value,
        message: document.getElementById('inquiryMessage').value
    };

    if (!inquiryData.inquirer_name || !inquiryData.inquirer_email || !inquiryData.message) {
        showAlert('danger', 'Please fill in all required fields.');
        return;
    }

    const submitBtn = document.querySelector('#inquiryModal .btn-primary');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = 'Sending...';
    submitBtn.disabled = true;

    setTimeout(() => {
        showAlert('success', `Inquiry sent to ${sellerUsername}!`);
        const modal = bootstrap.Modal.getInstance(document.getElementById('inquiryModal'));
        modal.hide();
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1000);
}

function shareProduct(productId, productName) {
    const productURL = `${window.location.origin}/public/goods/detail/${productId}/`;
    showShareModal(productURL, productName, 'product');
}

function shareStore() {
    const storeURL = window.location.href;
    const storeName = `{{ seller.first_name }} {{ seller.last_name }}'s Store`;
    showShareModal(storeURL, storeName, 'store');
}

function showShareModal(url, name, type) {
    const modalHTML = `
        <div class="modal fade" id="shareModal" tabindex="-1">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header py-2">
                        <h6 class="modal-title">Share ${type}</h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body py-2">
                        <div class="mb-2">
                            <label class="form-label small">Share URL</label>
                            <div class="input-group input-group-sm">
                                <input type="text" class="form-control" id="shareUrl" value="${url}" readonly>
                                <button class="btn btn-outline-secondary" onclick="copyToClipboard('shareUrl')">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                        </div>
                        <div class="d-grid gap-1">
                            <button class="btn btn-primary btn-sm" onclick="shareViaEmail('${url}', '${name}', '${type}')">
                                <i class="fas fa-envelope me-1"></i>Email
                            </button>
                            <div class="d-flex gap-1">
                                <button class="btn btn-outline-primary btn-sm flex-fill" onclick="shareOnFacebook('${url}', '${name}')">
                                    <i class="fab fa-facebook-f"></i>
                                </button>
                                <button class="btn btn-outline-info btn-sm flex-fill" onclick="shareOnTwitter('${url}', '${name}')">
                                    <i class="fab fa-twitter"></i>
                                </button>
                                <button class="btn btn-outline-success btn-sm flex-fill" onclick="shareOnWhatsApp('${url}', '${name}')">
                                    <i class="fab fa-whatsapp"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    const existingModal = document.getElementById('shareModal');
    if (existingModal) existingModal.remove();

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    try {
        document.execCommand('copy');
        showAlert('success', 'URL copied!');
    } catch (err) {
        navigator.clipboard.writeText(element.value).then(() => {
            showAlert('success', 'URL copied!');
        });
    }
}

function shareViaEmail(url, name, type) {
    const subject = encodeURIComponent(`Check out: ${name}`);
    const body = encodeURIComponent(`Hi, check out this ${type}: ${name}\n\n${url}`);
    window.open(`mailto:?subject=${subject}&body=${body}`);
}

function shareOnFacebook(url, name) {
    window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank', 'width=600,height=400');
}

function shareOnTwitter(url, name) {
    window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(name)}`, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, name) {
    window.open(`https://wa.me/?text=${encodeURIComponent(`${name} - ${url}`)}`, '_blank');
}

function contactSeller() {
    const sellerEmail = '{{ seller.email|default:"" }}';
    const sellerName = '{{ seller.first_name }} {{ seller.last_name }}';

    if (sellerEmail) {
        const subject = encodeURIComponent(`Store Inquiry`);
        const body = encodeURIComponent(`Hi ${sellerName}, I'm interested in your products.`);
        window.open(`mailto:${sellerEmail}?subject=${subject}&body=${body}`);
    } else {
        showAlert('info', 'Contact info not available.');
    }
}

function viewProductDetails(productId) {
    window.location.href = `/public/goods/detail/${productId}/`;
}

function showAlert(type, message) {
    const alertHTML = `
        <div class="alert alert-${type} alert-dismissible fade show position-fixed"
             style="top: 20px; right: 20px; z-index: 9999; font-size: 0.8rem;" role="alert">
            ${message}
            <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', alertHTML);
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        if (alerts.length > 0) alerts[alerts.length - 1].remove();
    }, 3000);
}

// Auto-submit filters
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.getElementById('filterForm');
    if (filterForm) {
        const autoSubmitElements = filterForm.querySelectorAll('select, input[type="checkbox"], input[type="radio"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', () => {
                setTimeout(() => filterForm.submit(), 200);
            });
        });

        const textInputs = filterForm.querySelectorAll('input[type="text"], input[type="number"]');
        textInputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    filterForm.submit();
                }
            });
        });
    }

    // Scroll to top functionality
    const scrollToTopBtn = document.getElementById('scrollToTop');

    // Show/hide scroll to top button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.classList.add('show');
        } else {
            scrollToTopBtn.classList.remove('show');
        }
    });

    // Scroll to top when button is clicked
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});
</script>
{% endblock %}
