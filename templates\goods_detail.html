{% extends 'base.html' %}

{% block title %}{{ goods.name }} - Goods Details{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'stores_list_create' %}">Stores</a></li>
            <li class="breadcrumb-item"><a href="{% url 'goods_list_create' %}">Goods</a></li>
            <li class="breadcrumb-item active">{{ goods.name }}</li>
        </ol>
    </nav>

    <!-- Display Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Display Errors -->
    {% if error %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endif %}

    

    <div class="row">
        <!-- Main Product Card -->
        <div class="col-lg-8">
            <div class="card">
                <div class="row g-0">
 <div class="col-md-5">
    {% if goods.images %}
        <!-- Main Image Display -->
        <div id="imageCarousel" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-inner">
                {% for image in goods.images %}
                    <div class="carousel-item {% if forloop.first %}active{% endif %}">
                        <!-- FIXED: Added proper media URL handling -->
                        <img src="/media/{{ image }}" 
                             class="d-block w-100 rounded-start" 
                             style="height: 300px; object-fit: cover;" 
                             alt="{{ goods.name }}"
                             onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                    </div>
                {% endfor %}
            </div>
             <!-- Carousel Controls (only show if more than 1 image) -->
            {% if goods.images|length > 1 %}
                <button class="carousel-control-prev" type="button" data-bs-target="#imageCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#imageCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            {% endif %}
        </div>
        <!-- Thumbnail Navigation (only show if more than 1 image) -->
        {% if goods.images|length > 1 %}
            <div class="row mt-2 g-1">
                {% for image in goods.images %}
                    <div class="col-3">
                        <!-- FIXED: Added proper media URL handling -->
                        <img src="/media/{{ image }}" 
                             class="img-thumbnail thumbnail-nav {% if forloop.first %}active{% endif %}" 
                             style="height: 60px; object-fit: cover; cursor: pointer;" 
                             alt="Thumbnail {{ forloop.counter }}"
                             onclick="goToSlide({{ forloop.counter0 }})"
                             onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% else %}
        <!-- No Image Placeholder -->
        <div class="d-flex align-items-center justify-content-center bg-light rounded-start" style="height: 300px;">
            <div class="text-center">
                <i class="fas fa-image fa-4x text-muted mb-2"></i>
                <p class="text-muted">No images available</p>
            </div>
        </div>
    {% endif %}
</div>
                    
                    <!-- Product Details Section -->
                    <div class="col-md-7">
                        <div class="card-body h-100 d-flex flex-column">
                            <div class="flex-grow-1">
                                <h3 class="card-title">{{ goods.name }}</h3>
                                <h4 class="text-success mb-3">{{ currency_symbol }}{{ goods.price|floatformat:2 }}</h4>

                                
                                
                                <!-- Product Status Badges -->
                                <div class="mb-3">
                                    {% if goods.is_used %}
                                        <span class="badge bg-warning me-2">Used</span>
                                    {% else %}
                                        <span class="badge bg-success me-2">New</span>
                                    {% endif %}
                                    
                                    {% if goods.available_for_delivery %}
                                        <span class="badge bg-info me-2">{{ goods.delivery_type|default:"Delivery Available" }}</span>
                                    {% else %}
                                        <span class="badge bg-secondary me-2">No Delivery</span>
                                    {% endif %}
                                    
                                    {% if goods.available_for_bulk_sales %}
                                        <span class="badge bg-primary">Bulk Sales Available</span>
                                    {% endif %}
                                </div>
                                
                                <!-- Category -->
                                {% if goods.category %}
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-tag"></i> Category: <strong>{{ goods.category }}</strong>
                                        </small>
                                    </div>
                                {% endif %}
                                
                                <!-- Description -->
                                {% if goods.description %}
                                    <p class="card-text">{{ goods.description }}</p>
                                {% else %}
                                    <p class="text-muted fst-italic">No description provided</p>
                                {% endif %}
                                
                                <!-- Store Information -->
                                <div class="mt-3">
                                    {% if goods.store_name %}
                                        <p class="mb-2">
                                            <strong>Store:</strong> 
                                            <span class="text-primary">{{ goods.store_name }}</span>
                                        </p>
                                    {% endif %}
                                    
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-plus"></i> Created: {{ goods.created_at|date:"M d, Y H:i" }}<br>
                                        <i class="fas fa-calendar-alt"></i> Last updated: {{ goods.updated_at|date:"M d, Y H:i" }}
                                    </small>
                                </div>
                            </div>
                            
                            <!-- Action Buttons -->
                            <div class="mt-3">
                                <div class="btn-group w-100" role="group">
                                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editModal">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <a href="{% url 'public_goods_detail' goods.id %}" class="btn btn-outline-info" target="_blank">
                                        <i class="fas fa-external-link-alt"></i> Public View
                                    </a>
                                    <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
<!-- Fix for the additional images section -->
{% if goods.images|length > 4 %}
    <div class="card mt-3">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-images"></i> All Images ({{ goods.images|length }})
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-2">
                {% for image in goods.images %}
                    <div class="col-md-3 col-sm-4 col-6">
                        <div class="position-relative">
                            <!-- FIXED: Added proper media URL handling -->
                            <img src="/media/{{ image }}" 
                                 class="img-thumbnail w-100" 
                                 style="height: 120px; object-fit: cover; cursor: pointer;" 
                                 alt="Product image {{ forloop.counter }}"
                                 data-bs-toggle="modal" 
                                 data-bs-target="#imageModal"
                                 onclick="showFullImage('/media/{{ image }}', '{{ forloop.counter }}')"
                                 onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                            <div class="position-absolute top-0 end-0 m-1">
                                <span class="badge bg-dark">{{ forloop.counter }}</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
{% endif %}
        </div>
        
        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Contact Information Card -->
            {% if contact_info %}
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-store"></i> Store Information
                    </h5>
                </div>
                <div class="card-body">
                    <h6>{{ contact_info.store_name }}</h6>
                    {% if contact_info.store_description %}
                        <p class="text-muted">{{ contact_info.store_description }}</p>
                    {% endif %}
                    
                    <div class="mt-3">
                        {% if contact_info.email %}
                            <p class="mb-2">
                                <i class="fas fa-envelope text-primary"></i>
                                <a href="mailto:{{ contact_info.email }}">{{ contact_info.email }}</a>
                            </p>
                        {% endif %}
                        
                        {% if contact_info.phone_number %}
                            <p class="mb-2">
                                <i class="fas fa-phone text-success"></i>
                                <a href="tel:{{ contact_info.phone_number }}">{{ contact_info.phone_number }}</a>
                            </p>
                        {% endif %}
                    </div>
                    
                    <div class="mt-3">
                        <a href="{% url 'store_detail' contact_info.store_id %}" class="btn btn-outline-primary btn-sm w-100">
                            <i class="fas fa-store"></i> View Store
                        </a>
                    </div>
                </div>
            </div>
            {% endif %}
            
            <!-- Product Statistics Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> Product Stats
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-images fa-2x text-primary mb-2"></i>
                                <h6 class="mb-0">{{ goods.images|length }}</h6>
                                <small class="text-muted">Images</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <i class="fas fa-eye fa-2x text-info mb-2"></i>
                                <h6 class="mb-0">-</h6>
                                <small class="text-muted">Views</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Actions Card -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-tools"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-primary" onclick="copyPublicLink()">
                            <i class="fas fa-link"></i> Copy Public Link
                        </button>
                        <button class="btn btn-outline-secondary" onclick="shareGoods()">
                            <i class="fas fa-share"></i> Share Product
                        </button>
                        <a href="{% url 'goods_list_create' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Back to Goods List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Product
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editGoodsForm" method="post" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editGoodsName" class="form-label">Product Name *</label>
                                <input type="text" class="form-control" id="editGoodsName" name="name" value="{{ goods.name }}" required>
                            </div>
                            <div class="mb-3">
                                <label for="editGoodsPrice" class="form-label">Price *</label>
                                <div class="input-group">
                                    <span class="input-group-text">$</span>
                                    <input type="number" class="form-control" id="editGoodsPrice" name="price" step="0.01" min="0" value="{{ goods.price }}" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="editCategorySelect" class="form-label">Category</label>
                                <select class="form-select" id="editCategorySelect" name="category_id">
                                    <option value="">Choose Category (Optional)...</option>
                                    {% for category in categories %}
                                        <option value="{{ category.id }}" {% if goods.category and goods.category == category.name %}selected{% endif %}>
                                            {{ category.name }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editIsUsed" name="is_used" {% if goods.is_used %}checked{% endif %}>
                                    <label class="form-check-label" for="editIsUsed">
                                        Used item
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editGoodsDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="editGoodsDescription" name="description" rows="4">{{ goods.description }}</textarea>
                            </div>
                            <div class="mb-3">
                                <label for="editGoodsImages" class="form-label">Images</label>
                                <input type="file" class="form-control" id="editGoodsImages" name="images" accept="image/*" multiple>
                                <div class="form-text">Upload new images (optional). Multiple files allowed.</div>
!-- Fix for edit form current images display -->
{% if goods.images %}
    <div class="mt-2">
        <small class="text-muted">Current images ({{ goods.images|length }}):</small>
        <div class="d-flex flex-wrap gap-1 mt-1">
            {% for image in goods.images|slice:":4" %}
                <!-- FIXED: Added proper media URL handling -->
                <img src="/media/{{ image }}" 
                     alt="Current image" 
                     style="width: 40px; height: 40px; object-fit: cover;" 
                     class="rounded"
                     onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
            {% endfor %}
            {% if goods.images|length > 4 %}
                <div class="d-flex align-items-center justify-content-center bg-light rounded" style="width: 40px; height: 40px;">
                    <small class="text-muted">+{{ goods.images|length|add:"-4" }}</small>
                </div>
            {% endif %}
        </div>
    </div>
{% endif %}
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editAvailableForDelivery" name="available_for_delivery" {% if goods.available_for_delivery %}checked{% endif %} onchange="toggleEditDeliveryType()">
                                    <label class="form-check-label" for="editAvailableForDelivery">
                                        Available for delivery
                                    </label>
                                </div>
                            </div>
                            <div class="mb-3" id="editDeliveryTypeContainer" style="{% if not goods.available_for_delivery %}display: none;{% endif %}">
                                <label for="editDeliveryType" class="form-label">Delivery Type</label>
                                <select class="form-select" id="editDeliveryType" name="delivery_type">
                                    <option value="">Select delivery type</option>
                                    <option value="within_state" {% if goods.delivery_type == 'within_state' %}selected{% endif %}>Within State</option>
                                    <option value="within_country" {% if goods.delivery_type == 'within_country' %}selected{% endif %}>Within Country</option>
                                    <option value="outside_country" {% if goods.delivery_type == 'outside_country' %}selected{% endif %}>Outside Country</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="editAvailableForBulkSales" name="available_for_bulk_sales" {% if goods.available_for_bulk_sales %}checked{% endif %}>
                                    <label class="form-check-label" for="editAvailableForBulkSales">
                                        Available for bulk sales
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Update Product
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle"></i> Delete Product
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                    <p>Are you sure you want to delete <strong>{{ goods.name }}</strong>?</p>
                    <p class="text-danger"><small><i class="fas fa-exclamation-circle"></i> This action cannot be undone.</small></p>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                    <i class="fas fa-trash"></i> Delete Product
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Full Size Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="imageModalTitle">Product Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="fullSizeImage" src="" class="img-fluid" alt="Full size product image">
            </div>
        </div>
    </div>
</div>

<script>
// Carousel and thumbnail functionality
function goToSlide(slideIndex) {
    const carousel = new bootstrap.Carousel(document.getElementById('imageCarousel'));
    carousel.to(slideIndex);
    
    // Update active thumbnail
    document.querySelectorAll('.thumbnail-nav').forEach(thumb => thumb.classList.remove('active'));
    document.querySelectorAll('.thumbnail-nav')[slideIndex].classList.add('active');
}

// Full size image modal
function showFullImage(imageSrc, imageNumber) {
    document.getElementById('fullSizeImage').src = imageSrc;
    document.getElementById('imageModalTitle').textContent = `Product Image ${imageNumber}`;
}

// Delivery type toggle for edit form
function toggleEditDeliveryType() {
    const deliveryCheckbox = document.getElementById('editAvailableForDelivery');
    const deliveryTypeContainer = document.getElementById('editDeliveryTypeContainer');
    
    if (deliveryCheckbox && deliveryCheckbox.checked) {
        deliveryTypeContainer.style.display = 'block';
    } else if (deliveryTypeContainer) {
        deliveryTypeContainer.style.display = 'none';
        const deliveryTypeSelect = document.getElementById('editDeliveryType');
        if (deliveryTypeSelect) deliveryTypeSelect.value = '';
    }
}

// Handle edit form submission
document.getElementById('editGoodsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    const formData = new FormData(this);

    fetch('{% url "goods_detail" goods.id %}', {
        method: 'POST',
        headers: {
            'X-CSRFToken': formData.get('csrfmiddlewaretoken')
        },
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            // Close modal and show success message
            const modal = bootstrap.Modal.getInstance(document.getElementById('editModal'));
            modal.hide();
            
            // Show success alert
            showAlert(data.message, 'success');
            
            // Reload page to show updated data
            setTimeout(() => {
                location.reload();
            }, 1500);
        } else if (data.error) {
            showAlert(data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to update product.', 'danger');
    });
});

// Delete confirmation
function confirmDelete() {
    fetch('{% url "goods_detail" goods.id %}', {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': '{{ csrf_token }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            setTimeout(() => {
                window.location.href = "{% url 'goods_list_create' %}";
            }, 1500);
        } else if (data.error) {
            showAlert(data.error, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Failed to delete product.', 'danger');
    });
}

// Copy public link function
function copyPublicLink() {
    const publicUrl = '{{ request.build_absolute_uri }}{% url "public_goods_detail" goods.id %}';
    navigator.clipboard.writeText(publicUrl).then(function() {
        showAlert('Public link copied to clipboard!', 'success');
    }, function() {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = publicUrl;
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();
        try {
            document.execCommand('copy');
            showAlert('Public link copied to clipboard!', 'success');
        } catch (err) {
            console.error('Fallback: Oops, unable to copy', err);
            showAlert('Unable to copy link. Please copy manually: ' + publicUrl, 'warning');
        }
        document.body.removeChild(textArea);
    });
}

// Share goods function
function shareGoods() {
    const publicUrl = '{{ request.build_absolute_uri }}{% url "public_goods_detail" goods.id %}';
    const shareData = {
        title: '{{ goods.name|escapejs }}',
        text: 'Check out this item: {{ goods.name|escapejs }} - ${{ goods.price }}',
        url: publicUrl
    };

    if (navigator.share) {
        navigator.share(shareData)
            .then(() => console.log('Successful share'))
            .catch((error) => console.log('Error sharing', error));
    } else {
        // Fallback for browsers that don't support Web Share API
        copyPublicLink();
    }
}

// Helper function to show alerts
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.setAttribute('role', 'alert');
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.row'));
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// Initialize carousel event listeners
document.addEventListener('DOMContentLoaded', function() {
    const carousel = document.getElementById('imageCarousel');
    if (carousel) {
        carousel.addEventListener('slide.bs.carousel', function(event) {
            // Update active thumbnail when carousel slides
            document.querySelectorAll('.thumbnail-nav').forEach(thumb => thumb.classList.remove('active'));
            const activeIndex = event.to;
            const thumbnails = document.querySelectorAll('.thumbnail-nav');
            if (thumbnails[activeIndex]) {
                thumbnails[activeIndex].classList.add('active');
            }
        });
    }
});
</script>

<style>
.thumbnail-nav {
    transition: all 0.3s ease;
    opacity: 0.7;
}

.thumbnail-nav:hover,
.thumbnail-nav.active {
    opacity: 1;
    border-color: #0d6efd !important;
    border-width: 2px !important;
}

.carousel-control-prev,
.carousel-control-next {
    width: 5%;
}

.carousel-control-prev-icon,
.carousel-control-next-icon {
    background-color: rgba(0,0,0,0.5);
    border-radius: 50%;
    padding: 15px;
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.badge {
    font-size: 0.8em;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

#fullSizeImage {
    max-height: 70vh;
    max-width: 100%;
    object-fit: contain;
}

.img-thumbnail {
    transition: transform 0.2s ease;
}

.img-thumbnail:hover {
    transform: scale(1.05);
}
</style>
{% endblock %}