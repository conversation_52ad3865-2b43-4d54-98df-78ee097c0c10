{% extends 'base.html' %}

{% block title %}Dashboard - Inventory Management System{% endblock %}

{% block extra_css %}
<style>
    .dashboard-welcome {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        color: white;
        padding: 3rem 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }

    .dashboard-welcome::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
        0%, 100% { transform: translate(-20px, -20px) rotate(0deg); }
        50% { transform: translate(20px, 20px) rotate(180deg); }
    }

    .welcome-content {
        position: relative;
        z-index: 2;
    }

    .welcome-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }

    .welcome-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
        margin-bottom: 0;
    }

    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-card {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        border-left: 5px solid;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .stat-card::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 100px;
        height: 100px;
        opacity: 0.1;
        transform: translate(25%, -25%);
    }

    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    }

    .stat-card.stores {
        border-left-color: #3498db;
    }
    .stat-card.stores::before {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%233498db' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3 7h7l-5.5 4 2 7-6.5-5-6.5 5 2-7-5.5-4h7z'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
    }

    .stat-card.categories {
        border-left-color: #2ecc71;
    }
    .stat-card.categories::before {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%232ecc71' viewBox='0 0 24 24'%3E%3Cpath d='M4 4h16v16H4z'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
    }

    .stat-card.goods {
        border-left-color: #f39c12;
    }
    .stat-card.goods::before {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23f39c12' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3 7h7l-5.5 4 2 7-6.5-5-6.5 5 2-7-5.5-4h7z'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
    }

    .stat-card.receipts {
        border-left-color: #e74c3c;
    }
    .stat-card.receipts::before {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23e74c3c' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
    }
    .stat-card.invoices {
        border-left-color:rgba(63, 60, 231, 0.75);
    }
    .stat-card.invoices::before {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23e74c3c' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
    }

    .stat-card.properties {
        border-left-color: #e67e22;
    }
    .stat-card.properties::before {
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23e67e22' viewBox='0 0 24 24'%3E%3Cpath d='M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z'/%3E%3C/svg%3E") no-repeat;
        background-size: contain;
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        border-radius: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
        margin-bottom: 1rem;
    }

    .stat-icon.stores { background: linear-gradient(135deg, #3498db, #5dade2); }
    .stat-icon.categories { background: linear-gradient(135deg, #2ecc71, #58d68d); }
    .stat-icon.goods { background: linear-gradient(135deg, #f39c12, #f7dc6f); }
    .stat-icon.receipts { background: linear-gradient(135deg, #e74c3c, #ec7063); }
    .stat-icon.invoices { background: linear-gradient(135deg,rgb(77, 60, 231),rgb(72, 70, 243)); }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        line-height: 1;
    }

    .stat-label {
        font-size: 1.1rem;
        color: #7f8c8d;
        font-weight: 500;
        margin-bottom: 1rem;
    }

    .stat-change {
        display: flex;
        align-items: center;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .stat-change.positive {
        color: #27ae60;
    }

    .stat-change.negative {
        color: #e74c3c;
    }

    .quick-actions {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 2rem;
    }

    .quick-actions h3 {
        color: #2c3e50;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .action-buttons {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        padding: 1rem 1.5rem;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        text-decoration: none;
    }

    .action-btn i {
        margin-right: 0.75rem;
        font-size: 1.2rem;
    }

    .action-btn.primary {
        background: linear-gradient(135deg, #3498db, #5dade2);
        color: white;
    }

    .action-btn.success {
        background: linear-gradient(135deg, #2ecc71, #58d68d);
        color: white;
    }

    .action-btn.warning {
        background: linear-gradient(135deg, #f39c12, #f7dc6f);
        color: white;
    }

    .action-btn.info {
        background: linear-gradient(135deg, #17a2b8, #5bc0de);
        color: white;
    }

    .recent-activity {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }

    .recent-activity h3 {
        color: #2c3e50;
        margin-bottom: 1.5rem;
        font-weight: 600;
    }

    .activity-item {
        display: flex;
        align-items: center;
        padding: 1rem 0;
        border-bottom: 1px solid #ecf0f1;
    }

    .activity-item:last-child {
        border-bottom: none;
    }

    .activity-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1rem;
    }

    .activity-content {
        flex: 1;
    }

    .activity-title {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.25rem;
    }

    .activity-time {
        font-size: 0.85rem;
        color: #95a5a6;
    }

    .no-data {
        text-align: center;
        padding: 3rem 2rem;
        color: #7f8c8d;
    }

    .no-data i {
        font-size: 3rem;
        margin-bottom: 1rem;
        opacity: 0.5;
    }

    @media (max-width: 768px) {
        .welcome-title {
            font-size: 2rem;
        }

        .stats-grid {
            grid-template-columns: 1fr;
        }

        .action-buttons {
            grid-template-columns: 1fr;
        }

        .stat-number {
            font-size: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="dashboard-welcome">
        <div class="welcome-content">
            <h1 class="welcome-title">Welcome back, <span id="dashboardUsername">{{ username }}</span>!</h1>
            <p class="welcome-subtitle">Here's what's happening with your inventory today</p>
        </div>
    </div>

    <!-- Statistics Grid -->
    <div class="stats-grid">
        <div class="stat-card stores">
            <div class="stat-icon stores">
                <i class="fas fa-store"></i>
            </div>
            <div class="stat-number" id="storeCount">{{ store_count }}</div>
            <div class="stat-label">Total Stores</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up me-1"></i>
                Active stores
            </div>
        </div>

<!-- Total Categories Card -->
<div class="stat-card categories">
    <div class="stat-icon categories">
        <i class="fas fa-warehouse"></i>
    </div>
    <div class="stat-number" id="categoriesCount">{{ category_count }}</div> <!-- Changed to categories -->
    <div class="stat-label">Total Categories</div>
    <div class="stat-change positive">
        <i class="fas fa-arrow-up me-1"></i> All categories
    </div>
</div>



        <div class="stat-card goods">
            <div class="stat-icon goods">
                <i class="fas fa-box-open"></i>
            </div>
            <div class="stat-number" id="goodsCount">--</div>
            <div class="stat-label">Total Goods</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up me-1"></i>
                In stock
            </div>
        </div>

        <div class="stat-card receipts">
            <div class="stat-icon receipts">
                <i class="fas fa-receipt"></i>
            </div>
            <div class="stat-number" id="receiptsCount">--</div>
            <div class="stat-label">Total Receipts</div>
            <div class="stat-change positive">
                <i class="fas fa-arrow-up me-1"></i>
                This month
            </div>
        </div>

<!-- Total Invoices Card -->
<div class="stat-card invoices">
    <div class="stat-icon invoices">
        <i class="fas fa-receipt"></i>
    </div>
    <div class="stat-number" id="invoicesCount">--</div>  <!-- Set default to "--" -->
    <div class="stat-label">Total Invoices</div>
    <div class="stat-change positive">
        <i class="fas fa-arrow-up me-1"></i> This month
    </div>
</div>

<!-- Total Properties Card -->
<div class="stat-card properties">
    <div class="stat-icon properties">
        <i class="fas fa-home"></i>
    </div>
    <div class="stat-number" id="propertiesCount">--</div>
    <div class="stat-label">Total Properties</div>
    <div class="stat-change positive">
        <i class="fas fa-arrow-up me-1"></i> Personal assets
    </div>
</div>

    </div>

    <div class="row">
        <!-- Quick Actions -->
        <div class="col-lg-8">
            <div class="quick-actions">
                <h3><i class="fas fa-rocket me-2"></i>Quick Actions</h3>
                <div class="action-buttons">
                    <a href="/stores/" class="action-btn primary">
                        <i class="fas fa-store"></i>
                        Manage Stores
                    </a>
                    <a href="/categories/" class="action-btn success">
                        <i class="fas fa-warehouse"></i>
                        View categories
                    </a>
                    <a href="/goods/" class="action-btn warning">
                        <i class="fas fa-box-open"></i>
                        Manage Goods
                    </a>


                    <!-- <a href="{% url 'private_seller_store' %}" class="action-btn secondary">
                        <i class="fas fa-store-alt"></i>
                        My Private Store
                    </a> -->
                    <!-- <a href="{% url 'manage_goods_inventory' %}" class="action-btn primary">
                        <i class="fas fa-warehouse"></i>
                        Inventory Management
                    </a> -->
                    <a href="{% url 'goods_list_create' %}" class="action-btn success">
                        <i class="fas fa-boxes"></i>
                        Goods Management
                    </a>
                    <a href="/receipts/" class="action-btn info">
                        <i class="fas fa-receipt"></i>
                        View Receipts
                    </a>
                    <a href="/receipts/" class="action-btn info">
                        <i class="fas fa-receipt"></i>
                        View invoices
                    </a>
                    <a href="{% url 'private_seller_store' %}" class="action-btn primary">
                        <i class="fas fa-store"></i>
                        My Private Store
                    </a>
                    <a href="/seller/{{ user.id }}/store/" class="action-btn info" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        View Public Store
                    </a>
                    <!-- <a href="{% url 'manage_goods_inventory' %}" class="action-btn warning">
                        <i class="fas fa-boxes"></i>
                        Inventory Management
                    </a> -->
                    <!-- Enhanced Marketplace Commented Out -->
                    <!-- <a href="/enhanced-goods/" class="action-btn success">
                        <i class="fas fa-search"></i>
                        Browse Enhanced Marketplace
                    </a> -->
                    <a href="{% url 'property_management' %}" class="action-btn info">
                        <i class="fas fa-home"></i>
                        Property Management
                    </a>
                    <button class="action-btn success" onclick="openEmailShareModal()">
                        <i class="fas fa-envelope"></i>
                        Email Share Store
                    </button>

                </div>
            </div>
        </div>

<!-- Authorization Section -->
<div class="col-lg-4">
    <div class="recent-activity">
        <h3 class="text-center"><i class="fas fa-user-shield me-2"></i>Authorization</h3>
        <div id="authorizationSection" class="text-center">
            <a href="{% url 'list_authorized_stores' %}" class="action-btn info mb-3 d-flex align-items-center justify-content-center">
                <i class="fas fa-store me-2"></i>
                Authorized Stores
            </a>
            {% comment %} <a href="{% url 'user_stores_list' %}" class="action-btn info d-flex align-items-center justify-content-center">
                <i class="fas fa-store-alt me-2"></i>
                My Stores
            </a> {% endcomment %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Dashboard data and functionality
    let dashboardData = {
        stores: {{ store_count }},
        goods: 0,
        receipts: 0,
        invoices: 0,
        properties: 0,
        lowStock: 0,
        recentSales: 0
    };

    // Load dashboard data on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadDashboardData();
        loadRecentActivity();
        
        // Refresh data every 5 minutes
        setInterval(loadDashboardData, 300000);
    });

    async function loadDashboardData() {
        try {
            // You can create API endpoints for these or load them from your views
            await Promise.all([
                loadCategoriesCount(),
                loadGoodsCount(),
                loadReceiptsCount(),
                loadInvoicesCount(),
                loadPropertiesCount(),
                loadQuickStats()
            ]);
            
            updateStatCards();
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        }
    }

    async function loadCategoriesCount() {
    try {
        const response = await fetch('/api/categories/count/');
        if (response.ok) {
            const data = await response.json();
            dashboardData.categories = data.count || 0; // Update categories count
        }
    } catch (error) {
        console.log('Categories count not available');
        dashboardData.categories = 0;
    }
}


    async function loadGoodsCount() {
        try {
            // Replace with your actual API endpoint
            const response = await fetch('/api/goods/count/');
            if (response.ok) {
                const data = await response.json();
                dashboardData.goods = data.count || 0;
            }
        } catch (error) {
            console.log('Goods count not available');
            dashboardData.goods = 0;
        }
    }

    async function loadReceiptsCount() {
        try {
            // Replace with your actual API endpoint
            const response = await fetch('/api/receipts/count/');
            if (response.ok) {
                const data = await response.json();
                dashboardData.receipts = data.count || 0;
            }
        } catch (error) {
            console.log('Receipts count not available');
            dashboardData.receipts = 0;
        }
    }
async function loadInvoicesCount() {
    try {
        const response = await fetch('/api/invoices/count/');
        if (response.ok) {
            const data = await response.json();
            dashboardData.invoices = data.count || 0;  // Update the invoices count
        }
    } catch (error) {
        console.log('Invoices count not available');
        dashboardData.invoices = 0;
    }
}

async function loadPropertiesCount() {
    try {
        const response = await fetch('/api/properties/count/');
        if (response.ok) {
            const data = await response.json();
            dashboardData.properties = data.count || 0;
        }
    } catch (error) {
        console.log('Properties count not available');
        dashboardData.properties = 0;
    }
}

    async function loadQuickStats() {
        try {
            // Replace with your actual API endpoint
            const response = await fetch('/api/dashboard/stats/');
            if (response.ok) {
                const data = await response.json();
                dashboardData.lowStock = data.low_stock || 0;
                dashboardData.recentSales = data.recent_sales || 0;
            }
        } catch (error) {
            console.log('Quick stats not available');
        }
    }

    function updateStatCards() {
        // Animate numbers
        animateNumber('categoriesCount', dashboardData.categories);
        animateNumber('goodsCount', dashboardData.goods);
        animateNumber('receiptsCount', dashboardData.receipts);
        animateNumber('invoicesCount', dashboardData.invoices);
        animateNumber('propertiesCount', dashboardData.properties);
        animateNumber('lowStockItems', dashboardData.lowStock);
        animateNumber('recentSales', dashboardData.recentSales);
    }

    function animateNumber(elementId, targetNumber) {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const startNumber = 0;
        const duration = 1000;
        const startTime = Date.now();
        
        function updateNumber() {
            const currentTime = Date.now();
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            const current = Math.floor(startNumber + (targetNumber - startNumber) * progress);
            element.textContent = current.toLocaleString();
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            }
        }
        
        updateNumber();
    }

    async function loadRecentActivity() {
        try {
            // Replace with your actual API endpoint
            const response = await fetch('/api/dashboard/activity/');
            if (response.ok) {
                const data = await response.json();
                displayRecentActivity(data.activities || []);
            }
        } catch (error) {
            console.log('Recent activity not available');
        }
    }

    function displayRecentActivity(activities) {
        const container = document.getElementById('recentActivity');
        
        if (!activities || activities.length === 0) {
            container.innerHTML = `
                <div class="no-data">
                    <i class="fas fa-history"></i>
                    <p>No recent activity</p>
                    <small>Your recent actions will appear here</small>
                </div>
            `;
            return;
        }

        const activitiesHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon" style="background: ${getActivityColor(activity.type)};">
                    <i class="fas fa-${getActivityIcon(activity.type)}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-time">${formatTime(activity.timestamp)}</div>
                </div>
            </div>
        `).join('');

        container.innerHTML = activitiesHTML;
    }

    function getActivityColor(type) {
        const colors = {
            'store': '#3498db',
            'inventory': '#2ecc71',
            'goods': '#f39c12',
            'receipt': '#e74c3c'
        };
        return colors[type] || '#95a5a6';
    }

    function getActivityIcon(type) {
        const icons = {
            'store': 'store',
            'inventory': 'warehouse',
            'goods': 'box',
            'receipt': 'receipt'
        };
        return icons[type] || 'circle';
    }

    function formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);
        
        if (minutes < 60) {
            return `${minutes} minutes ago`;
        } else if (hours < 24) {
            return `${hours} hours ago`;
        } else {
            return `${days} days ago`;
        }
    }

    // Update username if available from global context
    if (typeof currentUser !== 'undefined' && currentUser) {
        document.getElementById('dashboardUsername').textContent = currentUser.username;
    }

    // Check for URL parameters to trigger actions
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('action') === 'email_share') {
        // Trigger email share modal after page loads
        setTimeout(() => {
            openEmailShareModal();
        }, 1000);
    }

    // Email Share Functionality
    function openEmailShareModal() {
        // First, get user's stores
        fetch('/api/stores/count/')
        .then(response => response.json())
        .then(data => {
            if (data.count === 0) {
                showAlert('You need to create a store first before sharing!', 'warning');
                return;
            }

            // Load stores for selection
            loadStoresForEmailShare();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error checking stores', 'danger');
        });
    }

    function loadStoresForEmailShare() {
        // Create and show the email share modal
        const modalHtml = `
            <div class="modal fade" id="emailShareModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-envelope"></i> Email Share Store
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div id="storeSelectionStep">
                                <h6 class="mb-3">Select Store to Share</h6>
                                <div id="storesListContainer">
                                    <div class="text-center">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading stores...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="emailFormStep" style="display: none;">
                                <div class="d-flex align-items-center mb-3">
                                    <button class="btn btn-sm btn-outline-secondary me-3" onclick="backToStoreSelection()">
                                        <i class="fas fa-arrow-left"></i> Back
                                    </button>
                                    <h6 class="mb-0">Share: <span id="selectedStoreName"></span></h6>
                                </div>

                                <form id="dashboardEmailShareForm">
                                    <div class="mb-3">
                                        <label for="dashboardRecipientEmails" class="form-label">Recipient Emails *</label>
                                        <textarea class="form-control" id="dashboardRecipientEmails" rows="3"
                                                  placeholder="Enter email addresses separated by commas&#10;<EMAIL>, <EMAIL>" required></textarea>
                                        <small class="form-text text-muted">Separate multiple emails with commas</small>
                                    </div>

                                    <div class="mb-3">
                                        <label for="dashboardEmailMessage" class="form-label">Custom Message (Optional)</label>
                                        <textarea class="form-control" id="dashboardEmailMessage" rows="4"
                                                  placeholder="Add a personal message to include with the store share..."></textarea>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="dashboardPasswordProtected">
                                            <label class="form-check-label" for="dashboardPasswordProtected">
                                                Password protect this share
                                            </label>
                                        </div>
                                    </div>

                                    <div id="dashboardPasswordSection" style="display: none;">
                                        <div class="mb-3">
                                            <label for="dashboardSharePassword" class="form-label">Access Password</label>
                                            <input type="password" class="form-control" id="dashboardSharePassword" placeholder="Enter password for access">
                                        </div>
                                    </div>

                                    <input type="hidden" id="selectedStoreId">
                                </form>

                                <div id="dashboardEmailResults" style="display: none;" class="mt-4">
                                    <div class="alert alert-info">
                                        <h6><i class="fas fa-check-circle"></i> Email Results</h6>
                                        <div id="dashboardEmailResultsContent"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <button type="button" class="btn btn-info" id="advancedShareBtn" onclick="goToAdvancedSharing()" style="display: none;">
                                <i class="fas fa-cog"></i> Advanced Sharing
                            </button>
                            <button type="button" class="btn btn-success" id="sendEmailShareBtn" onclick="sendDashboardEmailShare()" style="display: none;">
                                <i class="fas fa-paper-plane"></i> Send Email Invitations
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if any
        const existingModal = document.getElementById('emailShareModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('emailShareModal'));
        modal.show();

        // Load stores
        loadUserStores();

        // Setup password toggle
        document.getElementById('dashboardPasswordProtected').addEventListener('change', function() {
            const passwordSection = document.getElementById('dashboardPasswordSection');
            passwordSection.style.display = this.checked ? 'block' : 'none';
        });

        // Remove modal from DOM when hidden
        document.getElementById('emailShareModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    }

    function loadUserStores() {
        fetch('/api/user/stores/')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.stores.length > 0) {
                let storesHtml = '<div class="row">';
                data.stores.forEach(store => {
                    storesHtml += `
                        <div class="col-md-6 mb-3">
                            <div class="card store-select-card" onclick="selectStoreForEmail('${store.id}', '${store.name.replace(/'/g, "\\'")}')">
                                <div class="card-body">
                                    <h6 class="card-title">${store.name}</h6>
                                    <p class="card-text text-muted small">${store.description || 'No description'}</p>
                                    <small class="text-muted">Created: ${new Date(store.created_at).toLocaleDateString()}</small>
                                    <div class="mt-2">
                                        <button class="btn btn-sm btn-primary">
                                            <i class="fas fa-envelope"></i> Select for Email Share
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });
                storesHtml += '</div>';

                document.getElementById('storesListContainer').innerHTML = storesHtml;
            } else {
                document.getElementById('storesListContainer').innerHTML = `
                    <div class="text-center py-4">
                        <i class="fas fa-store fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Stores Found</h5>
                        <p class="text-muted">Create a store first to share it via email.</p>
                        <a href="/stores/" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Store
                        </a>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading stores:', error);
            document.getElementById('storesListContainer').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading stores. Please try again.
                </div>
            `;
        });
    }

    function selectStoreForEmail(storeId, storeName) {
        document.getElementById('selectedStoreId').value = storeId;
        document.getElementById('selectedStoreName').textContent = storeName;

        // Hide store selection, show email form
        document.getElementById('storeSelectionStep').style.display = 'none';
        document.getElementById('emailFormStep').style.display = 'block';
        document.getElementById('sendEmailShareBtn').style.display = 'inline-block';
        document.getElementById('advancedShareBtn').style.display = 'inline-block';
    }

    function backToStoreSelection() {
        document.getElementById('emailFormStep').style.display = 'none';
        document.getElementById('storeSelectionStep').style.display = 'block';
        document.getElementById('sendEmailShareBtn').style.display = 'none';
        document.getElementById('advancedShareBtn').style.display = 'none';

        // Clear form
        document.getElementById('dashboardEmailShareForm').reset();
        document.getElementById('dashboardEmailResults').style.display = 'none';
    }

    function goToAdvancedSharing() {
        const storeId = document.getElementById('selectedStoreId').value;
        if (storeId) {
            window.location.href = `/stores/${storeId}/`;
        }
    }

    function sendDashboardEmailShare() {
        const storeId = document.getElementById('selectedStoreId').value;
        const emails = document.getElementById('dashboardRecipientEmails').value.split(',').map(email => email.trim()).filter(email => email);
        const message = document.getElementById('dashboardEmailMessage').value;
        const passwordProtected = document.getElementById('dashboardPasswordProtected').checked;
        const password = document.getElementById('dashboardSharePassword').value;

        if (emails.length === 0) {
            showAlert('Please enter at least one email address', 'warning');
            return;
        }

        const button = document.getElementById('sendEmailShareBtn');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
        button.disabled = true;

        // First create a share link
        const shareData = {
            title: `Shared Store: ${document.getElementById('selectedStoreName').textContent}`,
            description: message,
            password_protected: passwordProtected,
            access_password: passwordProtected ? password : ''
        };

        fetch(`/stores/${storeId}/share/create/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(shareData)
        })
        .then(response => response.json())
        .then(shareResult => {
            if (shareResult.success) {
                // Now send emails
                const emailData = {
                    emails: emails,
                    message: message,
                    share_token: shareResult.share_token
                };

                return fetch(`/stores/${storeId}/share/email/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(emailData)
                });
            } else {
                throw new Error(shareResult.error || 'Failed to create share link');
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const resultsHtml = `
                    <p><strong>Successfully sent:</strong> ${data.total_sent} email(s)</p>
                    ${data.successful_emails.length > 0 ? `<p class="text-success">✓ ${data.successful_emails.join(', ')}</p>` : ''}
                    ${data.failed_emails.length > 0 ? `<p class="text-danger">✗ Failed: ${data.failed_emails.join(', ')}</p>` : ''}
                    <p><strong>Share URL:</strong> <a href="${data.share_url}" target="_blank">${data.share_url}</a></p>
                `;
                document.getElementById('dashboardEmailResultsContent').innerHTML = resultsHtml;
                document.getElementById('dashboardEmailResults').style.display = 'block';

                showAlert(`Emails sent successfully! ${data.total_sent} sent, ${data.total_failed} failed.`, 'success');
            } else {
                showAlert('Error: ' + (data.error || 'Failed to send emails'), 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('An error occurred: ' + error.message, 'danger');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    // Utility function to show alerts
    function showAlert(message, type) {
        // Create alert element
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Add to top of page
        document.body.insertAdjacentHTML('afterbegin', alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = document.querySelector('.alert');
            if (alert) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        }, 5000);
    }

    // Get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>

<!-- Email Share Modal Styles -->
<style>
.store-select-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.store-select-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    border-color: #007bff;
}

.store-select-card .btn {
    pointer-events: none;
}

#emailShareModal .modal-dialog {
    max-width: 600px;
}

#emailShareModal .form-control:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}
</style>

{% endblock %}