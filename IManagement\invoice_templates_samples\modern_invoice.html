<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Modern Invoice</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.3;
      color: #2c3e50;
      background: #f8f9fa;
      padding: 8px;
      font-size: 12px;
    }

    .invoice-container {
      max-width: 650px;
      margin: 0 auto;
      background: #ffffff;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      border: 1px solid #e1e8ed;
    }

    .invoice-header {
      background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);
      color: white;
      padding: 18px;
      position: relative;
    }

    .invoice-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    }

    .header-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .logo-section {
      flex-shrink: 0;
    }

    .logo-image {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid white;
      background: white;
    }

    .title-section {
      flex-grow: 1;
      text-align: center;
    }

    h1 {
      font-size: 1.8em;
      font-weight: 300;
      letter-spacing: 1px;
      margin-bottom: 3px;
      text-transform: uppercase;
    }

    .invoice-subtitle {
      font-size: 0.85em;
      opacity: 0.9;
      font-weight: 300;
    }

    .company-info {
      text-align: center;
      margin-bottom: 18px;
      padding: 12px;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 3px solid #1abc9c;
    }

    .company-name {
      font-size: 1.2em;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 6px;
    }

    .company-contact {
      margin-top: 6px;
      font-size: 0.85em;
      color: #555;
    }

    .company-contact div {
      margin-bottom: 2px;
    }

    .invoice-body {
      padding: 20px;
    }

    .invoice-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 18px;
      margin-bottom: 18px;
    }

    .bill-to, .invoice-info {
      background: #ffffff;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e1e8ed;
    }

    .section-title {
      font-size: 1em;
      font-weight: 600;
      color: #1abc9c;
      margin-bottom: 10px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #1abc9c;
      padding-bottom: 4px;
    }

    .detail-item {
      margin-bottom: 6px;
      display: flex;
      justify-content: space-between;
    }

    .detail-label {
      font-weight: 500;
      color: #7f8c8d;
      min-width: 90px;
    }

    .detail-value {
      font-weight: 600;
      color: #2c3e50;
      text-align: right;
    }

    .client-info {
      color: #2c3e50;
      line-height: 1.4;
    }

    .client-info div {
      margin-bottom: 4px;
    }

    table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin-bottom: 15px;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      font-size: 0.85em;
    }

    th {
      background: linear-gradient(135deg, #1abc9c 0%, #16a085 100%);
      color: white;
      padding: 12px 10px;
      text-align: left;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-size: 0.8em;
    }

    th:last-child,
    td:last-child {
      text-align: right;
    }

    td {
      border-bottom: 1px solid #e1e8ed;
      padding: 10px;
      vertical-align: middle;
      transition: background-color 0.2s ease;
    }

    tr:nth-child(even) {
      background-color: #f8f9fa;
    }

    tr:hover {
      background-color: #f8f9fa;
    }

    tbody tr:last-child td {
      border-bottom: none;
    }

    .item-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .item-sku {
      font-size: 0.8em;
      color: #7f8c8d;
      font-family: 'Courier New', monospace;
    }

    .totals-section {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e1e8ed;
      margin-bottom: 18px;
    }

    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      border-bottom: 1px solid #dee2e6;
    }

    .total-row:last-child {
      border-bottom: none;
      font-size: 1.1em;
      font-weight: 700;
      color: #1abc9c;
      padding-top: 10px;
      border-top: 2px solid #1abc9c;
      margin-top: 10px;
    }

    .total-label {
      font-weight: 500;
    }

    .total-value {
      font-family: 'Courier New', monospace;
      font-weight: 600;
    }

    .payment-terms, .notes-section {
      background: #ffffff;
      padding: 12px;
      border-radius: 6px;
      border: 1px solid #e1e8ed;
      margin-bottom: 15px;
    }

    .payment-terms h6, .notes-section h6 {
      color: #1abc9c;
      font-weight: 600;
      margin-bottom: 6px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .footer {
      margin-top: 18px;
      text-align: center;
      background: #1abc9c;
      color: white;
      padding: 12px;
      font-size: 0.75em;
      opacity: 0.95;
    }

    .signatures-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 15px;
    }

    .signature-block {
      text-align: center;
      padding: 12px;
      background: #ffffff;
      border: 2px dashed #dee2e6;
      border-radius: 6px;
      height: 100px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }

    .signature-label {
      font-weight: 600;
      color: #1abc9c;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-size: 0.8em;
      flex-shrink: 0;
    }

    .signature-area {
      flex-grow: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 55px;
      padding-bottom: 8px;
      position: relative;
    }

    .signature-area img {
      max-width: 90%;
      max-height: 50px;
      object-fit: contain;
      border: none;
      border-radius: 0;
      padding: 0;
      background: transparent;
    }

    .signature-line {
      width: 100%;
      height: 1px;
      background: #dee2e6;
      margin-top: 6px;
    }

    .footer-text {
      font-size: 0.75em;
      color: rgba(255, 255, 255, 0.9);
    }

    .status-badge {
      display: inline-block;
      padding: 4px 12px;
      border-radius: 20px;
      font-size: 0.75em;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-left: 10px;
    }

    .status-paid { background: #2ecc71; color: white; }
    .status-pending { background: #f39c12; color: white; }
    .status-overdue { background: #e74c3c; color: white; }

    @media (max-width: 768px) {
      .invoice-container {
        margin: 10px;
        border-radius: 0;
      }
      
      .invoice-body {
        padding: 15px;
      }
      
      .invoice-details,
      .signatures-section {
        grid-template-columns: 1fr;
        gap: 12px;
      }
      
      .header-content {
        flex-direction: column;
        text-align: center;
        gap: 12px;
      }
      
      th, td {
        padding: 6px 4px;
        font-size: 0.75em;
      }
      
      h1 {
        font-size: 1.4em;
      }
    }

    @media print {
      body {
        background: white;
        padding: 0;
        font-size: 11px;
      }
      
      .invoice-container {
        box-shadow: none;
        border: 1px solid #ccc;
        margin: 0;
        border-radius: 0;
        max-width: none;
        width: 100%;
      }
      
      .invoice-header {
        padding: 15px;
      }
      
      .invoice-body {
        padding: 15px;
      }
      
      .invoice-details {
        margin-bottom: 15px;
        gap: 15px;
      }
      
      .totals-section {
        margin-bottom: 15px;
        padding: 12px;
      }
      
      .signatures-section {
        margin-top: 15px;
        gap: 12px;
      }
      
      .signature-block {
        height: 80px;
        padding: 10px;
      }
      
      .footer {
        padding: 10px;
      }
    }
  </style>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Generate random invoice number if not provided
      const invoiceNumberElements = document.querySelectorAll('[data-auto="invoice-number"]');
      invoiceNumberElements.forEach(element => {
        if (!element.textContent.trim()) {
          const randomNumber = Math.floor(Math.random() * 900000) + 100000;
          element.textContent = `INV-${randomNumber}`;
        }
      });
      
      // Generate current date if not provided
      const dateElements = document.querySelectorAll('[data-auto="date"]');
      dateElements.forEach(element => {
        if (!element.textContent.trim()) {
          const now = new Date();
          const day = String(now.getDate()).padStart(2, '0');
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const year = now.getFullYear();
          
          element.textContent = `${day}/${month}/${year}`;
        }
      });
    });
  </script>
</head>
<body>
  <div class="invoice-container">
    <header class="invoice-header">
      <div class="header-content">
        <div class="logo-section">
          {% if avatar_url %}
            <img src="{{ avatar_url }}" alt="Company Logo" class="logo-image" />
          {% else %}
            <div style="width: 60px; height: 60px; background: #fff; border-radius: 50%; border: 2px solid #fff;"></div>
          {% endif %}
        </div>

        <div class="title-section">
          <h1>Invoice</h1>
          <p class="invoice-subtitle">Professional Invoice Document</p>
        </div>

        <div style="width: 60px;"></div>
      </div>
      
      <div class="company-info">
        <div class="company-name">{{ company_name|default:"Your Company Name" }}</div>
        <div class="company-contact">
          {% if phone_number %}
          <div>Phone: {{ phone_number }}</div>
          {% endif %}
          {% if secondary_email %}
          <div>Email: {{ secondary_email }}</div>
          {% endif %}
          {% if company_address %}
          <div>{{ company_address }}</div>
          {% endif %}
        </div>
      </div>
    </header>

    <main class="invoice-body">
      <section class="invoice-details">
        <div class="bill-to">
          <h6 class="section-title">Bill To</h6>
          <div class="client-info">
            <div><strong>{{ client_name|default:"Client Name" }}</strong></div>
            {% if client_email %}
            <div>{{ client_email }}</div>
            {% endif %}
            {% if client_phone %}
            <div>{{ client_phone }}</div>
            {% endif %}
            {% if client_address %}
            <div>{{ client_address }}</div>
            {% endif %}
          </div>
        </div>

        <div class="invoice-info">
          <h6 class="section-title">Invoice Details</h6>
          <div class="detail-item">
            <span class="detail-label">Invoice #:</span>
            <span class="detail-value">
              {% if invoice_number %}
                {{ invoice_number }}
              {% else %}
                <span data-auto="invoice-number"></span>
              {% endif %}
            </span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Date:</span>
            <span class="detail-value">
              {% if invoice_date %}
                {{ invoice_date }}
              {% else %}
                <span data-auto="date"></span>
              {% endif %}
            </span>
          </div>
          {% if due_date %}
          <div class="detail-item">
            <span class="detail-label">Due Date:</span>
            <span class="detail-value">{{ due_date }}</span>
          </div>
          {% endif %}
          {% if currency %}
          <div class="detail-item">
            <span class="detail-label">Currency:</span>
            <span class="detail-value">{{ currency }}</span>
          </div>
          {% endif %}
        </div>
      </section>

      <!-- Items Table -->
      <table>
        <thead>
          <tr>
            <th>SKU</th>
            <th>Description</th>
            <th>Quantity</th>
            <th>Unit Price</th>
            <th>Line Total</th>
          </tr>
        </thead>
        <tbody>
          {% for item in items %}
          <tr>
            <td>
              {% if item.sku %}
              <span class="item-sku">{{ item.sku }}</span>
              {% else %}
              <span class="item-sku">N/A</span>
              {% endif %}
            </td>
            <td>
              <div class="item-name">{{ item.name }}</div>
            </td>
            <td>{{ item.quantity }}</td>
            <td>{{ currency_symbol|default:"$" }}{{ item.unit_price|default:item.unit_price|floatformat:2 }}</td>
            <td>{{ currency_symbol|default:"$" }}{{ item.total_price|default:item.total|floatformat:2 }}</td>
          </tr>
          {% empty %}
          <!-- Sample items for preview -->
          <tr>
            <td><span class="item-sku">SKU001</span></td>
            <td><div class="item-name">Sample Product 1</div></td>
            <td>2</td>
            <td>$50.00</td>
            <td>$100.00</td>
          </tr>
          <tr>
            <td><span class="item-sku">SRV001</span></td>
            <td><div class="item-name">Sample Service</div></td>
            <td>1</td>
            <td>$75.00</td>
            <td>$75.00</td>
          </tr>
          {% endfor %}
        </tbody>
      </table>

      <!-- Totals Section -->
      <section class="totals-section">
        <div class="total-row">
          <span class="total-label">Subtotal:</span>
          <span class="total-value">{{ currency_symbol|default:"$" }}{{ subtotal|default:"175.00"|floatformat:2 }}</span>
        </div>
        {% if tax_amount %}
        <div class="total-row">
          <span class="total-label">Tax:</span>
          <span class="total-value">{{ currency_symbol|default:"$" }}{{ tax_amount }}</span>
        </div>
        {% endif %}
        {% if discount_amount %}
        <div class="total-row">
          <span class="total-label">Discount:</span>
          <span class="total-value">-{{ currency_symbol|default:"$" }}{{ discount_amount }}</span>
        </div>
        {% endif %}
        <div class="total-row">
          <span class="total-label">Total Amount Due:</span>
          <span class="total-value">{{ currency_symbol|default:"$" }}{{ total_price|default:grand_total|default:"175.00"|floatformat:2 }}</span>
        </div>
      </section>

      <!-- Payment Terms -->
      {% if payment_terms %}
      <section class="payment-terms">
        <h6>Payment Terms</h6>
        <p>{{ payment_terms }}</p>
      </section>
      {% endif %}

      <!-- Notes -->
      {% if notes %}
      <section class="notes-section">
        <h6>Additional Notes</h6>
        <p>{{ notes }}</p>
      </section>
      {% endif %}
    </main>

    <!-- Footer with Signatures -->
    <div class="footer">
      <div class="signatures-section">
        <div class="signature-block">
          <div class="signature-label">Authorized Signature</div>
          <div class="signature-area">
            {% if signature_url %}
              <img src="{{ signature_url }}" alt="Signature" />
            {% endif %}
          </div>
          <div class="signature-line"></div>
        </div>

        <div class="signature-block">
          <div class="signature-label">Company Stamp</div>
          <div class="signature-area">
            {% if stamp_url %}
              <img src="{{ stamp_url }}" alt="Official Stamp" />
            {% endif %}
          </div>
          <div class="signature-line"></div>
        </div>
      </div>
      
      <div class="footer-text">
        <p>Thank you for your business!</p>
      </div>
    </div>
  </div>
</body>
</html>