{% extends 'base.html' %}

{% block title %}My Stores{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Stores You Own or Are Authorized For</h2>
    </div>

    {% if stores %}
        <div class="row">
            {% for store in stores %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <h5 class="card-title">{{ store.name }}</h5>
                                {% if store.is_owner %}
                                    <span class="badge bg-success">Owner</span>
                                {% else %}
                                    <span class="badge bg-info">Authorized</span>
                                {% endif %}
                            </div>
                            <p class="card-text">{{ store.description|default:"No description" }}</p>
                            <small class="text-muted">Created: {{ store.created_at|date:"M d, Y" }}</small>
                            
                            <hr class="my-3">
                            
                            <h6 class="mb-2">
                                <i class="fas fa-users"></i> Authorized Users 
                                <span class="badge bg-secondary">{{ store.authorized_users.all|length }}</span>
                            </h6>
                            
                            {% if store.authorized_users.all %}
                                <div class="authorized-users-list" style="max-height: 120px; overflow-y: auto;">
                                    {% for user in store.authorized_users.all %}
                                        <div class="d-flex align-items-center mb-1">
                                            <i class="fas fa-user-circle text-muted me-2"></i>
                                            <small>
                                                <strong>{{ user.username }}</strong>
                                                <span class="text-muted">({{ user.email }})</span>
                                            </small>
                                        </div>
                                    {% endfor %}
                                </div>
                            {% else %}
                                <small class="text-warning">
                                    <i class="fas fa-exclamation-triangle"></i> No authorized users
                                </small>
                            {% endif %}
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="{% url 'store_detail' store.id %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                {% if store.is_owner %}
                                    <a href="{% url 'store_manage' store.id %}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-cog"></i> Manage
                                    </a>
                                {% endif %}
                                <a href="{% url 'inventories_list_create' %}?store_id={{ store.id }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-boxes"></i> Inventory
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-store fa-3x text-muted mb-3"></i>
            <h4>No stores found</h4>
            <p class="text-muted">You are not the owner or authorized for any stores yet</p>
        </div>
    {% endif %}
</div>
{% endblock %}