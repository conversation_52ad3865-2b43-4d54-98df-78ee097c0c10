<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - Inventory Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .verification-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .verification-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 100%;
            text-align: center;
        }

        .verification-header {
            margin-bottom: 30px;
        }

        .verification-header h1 {
            color: #333;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .verification-header p {
            color: #666;
            font-size: 1.1rem;
        }

        .email-display {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .verification-code-input {
            font-size: 1.5rem;
            text-align: center;
            letter-spacing: 0.5rem;
            font-weight: bold;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            margin: 20px 0;
        }

        .verification-code-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .btn-verify {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            padding: 12px 30px;
            border-radius: 10px;
            color: white;
            font-weight: 600;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            width: 100%;
            margin: 10px 0;
        }

        .btn-verify:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-resend {
            background: transparent;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 10px 25px;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
            width: 100%;
            margin: 10px 0;
        }

        .btn-resend:hover {
            background: #667eea;
            color: white;
        }

        .loading-spinner {
            display: none;
        }

        .alert {
            border-radius: 10px;
            border: none;
            margin: 20px 0;
        }

        .back-to-login {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .back-to-login a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }

        .back-to-login a:hover {
            color: #764ba2;
        }

        .verification-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="verification-container">
        <div class="verification-card">
            <div class="verification-header">
                <i class="fas fa-envelope-open verification-icon"></i>
                <h1>Verify Your Email</h1>
                <p>We've sent a verification code to your email address</p>
            </div>

            <div class="email-display">
                <i class="fas fa-envelope me-2"></i>
                <strong>{{ email }}</strong>
            </div>

            <div id="alertContainer"></div>

            <form id="verificationForm">
                {% csrf_token %}
                <input type="hidden" id="userId" value="{{ user_id }}">
                
                <div class="mb-3">
                    <label for="verificationCode" class="form-label">Enter 6-digit verification code</label>
                    <input type="text" 
                           class="form-control verification-code-input" 
                           id="verificationCode" 
                           name="verification_code"
                           maxlength="6" 
                           pattern="[0-9]{6}"
                           placeholder="000000"
                           required>
                </div>

                <button type="submit" class="btn btn-verify" id="verifyBtn">
                    <span class="verify-text">
                        <i class="fas fa-check-circle me-2"></i>Verify Email
                    </span>
                    <span class="loading-spinner">
                        <i class="fas fa-spinner fa-spin me-2"></i>Verifying...
                    </span>
                </button>
            </form>

            <button type="button" class="btn btn-resend" id="resendBtn" onclick="resendVerification()">
                <span class="resend-text">
                    <i class="fas fa-paper-plane me-2"></i>Resend Code
                </span>
                <span class="loading-spinner">
                    <i class="fas fa-spinner fa-spin me-2"></i>Sending...
                </span>
            </button>

            <div class="back-to-login">
                <p class="mb-0">
                    <a href="/login/">
                        <i class="fas fa-arrow-left me-1"></i>Back to Login
                    </a>
                </p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();
            
            const alertHTML = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHTML);
            
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        function getAlertIcon(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function setButtonLoading(buttonId, isLoading) {
            const button = document.getElementById(buttonId);
            const textSpan = button.querySelector('.verify-text, .resend-text');
            const loadingSpan = button.querySelector('.loading-spinner');
            
            if (isLoading) {
                textSpan.style.display = 'none';
                loadingSpan.style.display = 'inline';
                button.disabled = true;
            } else {
                textSpan.style.display = 'inline';
                loadingSpan.style.display = 'none';
                button.disabled = false;
            }
        }

        // Auto-format verification code input
        document.getElementById('verificationCode').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, ''); // Remove non-digits
            if (value.length > 6) value = value.slice(0, 6); // Limit to 6 digits
            e.target.value = value;
        });

        // Form submission
        document.getElementById('verificationForm').addEventListener('submit', function(e) {
            e.preventDefault();
            setButtonLoading('verifyBtn', true);

            const userId = document.getElementById('userId').value;
            const verificationCode = document.getElementById('verificationCode').value;

            if (verificationCode.length !== 6) {
                showAlert('Please enter a 6-digit verification code', 'warning');
                setButtonLoading('verifyBtn', false);
                return;
            }

            fetch('/verify-email/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name="csrfmiddlewaretoken"]').value,
                },
                body: JSON.stringify({
                    user_id: userId,
                    verification_code: verificationCode
                }),
            })
            .then(response => response.json())
            .then(data => {
                setButtonLoading('verifyBtn', false);
                if (data.success && data.verified) {
                    showAlert(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = data.redirect_url || '/dashboard/';
                    }, 2000);
                } else {
                    showAlert(data.error || 'Verification failed', 'danger');
                }
            })
            .catch(error => {
                setButtonLoading('verifyBtn', false);
                showAlert('An error occurred during verification', 'danger');
            });
        });

        // Resend verification code
        function resendVerification() {
            setButtonLoading('resendBtn', true);
            
            const userId = document.getElementById('userId').value;
            
            fetch('/resend-verification/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name="csrfmiddlewaretoken"]').value,
                },
                body: JSON.stringify({
                    user_id: userId
                }),
            })
            .then(response => response.json())
            .then(data => {
                setButtonLoading('resendBtn', false);
                if (data.success) {
                    showAlert(data.message, 'success');
                } else {
                    showAlert(data.error || 'Failed to resend verification code', 'danger');
                }
            })
            .catch(error => {
                setButtonLoading('resendBtn', false);
                showAlert('An error occurred while resending verification code', 'danger');
            });
        }

        // Auto-focus on verification code input
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('verificationCode').focus();
        });
    </script>
</body>
</html>
