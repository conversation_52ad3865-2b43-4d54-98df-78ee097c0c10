<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - {{ site_name }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .email-container {
            background: white;
            border-radius: 10px;
            padding: 40px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #667eea;
        }
        
        .logo {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .title {
            color: #333;
            font-size: 1.5rem;
            margin-bottom: 10px;
        }
        
        .subtitle {
            color: #666;
            font-size: 1rem;
        }
        
        .content {
            margin: 30px 0;
        }
        
        .greeting {
            font-size: 1.1rem;
            margin-bottom: 20px;
        }
        
        .verification-code-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin: 30px 0;
        }
        
        .verification-code-label {
            font-size: 1rem;
            margin-bottom: 15px;
            opacity: 0.9;
        }
        
        .verification-code {
            font-size: 2.5rem;
            font-weight: bold;
            letter-spacing: 0.5rem;
            background: rgba(255, 255, 255, 0.2);
            padding: 15px 30px;
            border-radius: 8px;
            display: inline-block;
            margin: 10px 0;
        }
        
        .code-instructions {
            font-size: 0.9rem;
            margin-top: 15px;
            opacity: 0.9;
        }
        
        .instructions {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            margin: 20px 0;
        }
        
        .instructions h3 {
            color: #667eea;
            margin-top: 0;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .security-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .security-note strong {
            color: #856404;
        }
        
        .footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            text-align: center;
            color: #666;
            font-size: 0.9rem;
        }
        
        .footer-links {
            margin: 15px 0;
        }
        
        .footer-links a {
            color: #667eea;
            text-decoration: none;
            margin: 0 10px;
        }
        
        .footer-links a:hover {
            text-decoration: underline;
        }
        
        .social-links {
            margin: 20px 0;
        }
        
        .social-links a {
            display: inline-block;
            margin: 0 5px;
            color: #667eea;
            text-decoration: none;
        }
        
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            
            .email-container {
                padding: 20px;
            }
            
            .verification-code {
                font-size: 2rem;
                letter-spacing: 0.3rem;
                padding: 10px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <div class="header">
            <div class="logo">📦 {{ site_name }}</div>
            <h1 class="title">
                {% if is_resend %}
                    New Verification Code
                {% else %}
                    Welcome! Verify Your Email
                {% endif %}
            </h1>
            <p class="subtitle">
                {% if is_resend %}
                    Your new verification code is ready
                {% else %}
                    Complete your registration to get started
                {% endif %}
            </p>
        </div>
        
        <div class="content">
            <p class="greeting">
                Hello <strong>{{ user.first_name }} {{ user.last_name }}</strong>,
            </p>
            
            {% if is_resend %}
            <p>
                You requested a new verification code for your {{ site_name }} account. 
                Here's your new code:
            </p>
            {% else %}
            <p>
                Thank you for registering with {{ site_name }}! We're excited to have you on board.
            </p>
            
            <p>
                To complete your registration and secure your account, please verify your email address 
                using the verification code below:
            </p>
            {% endif %}
            
            <div class="verification-code-section">
                <div class="verification-code-label">Your 6-Digit Verification Code</div>
                <div class="verification-code">{{ verification_code }}</div>
                <div class="code-instructions">
                    Enter this code on the verification page to activate your account
                </div>
            </div>
            
            <div class="instructions">
                <h3>How to verify your email:</h3>
                <ol>
                    <li>Go back to the verification page in your browser</li>
                    <li>Enter the 6-digit code: <strong>{{ verification_code }}</strong></li>
                    <li>Click "Verify Email" to complete the process</li>
                    <li>You'll be automatically logged in and redirected to your dashboard</li>
                </ol>
            </div>
            
            <div class="security-note">
                <strong>🔒 Security Note:</strong> This verification code will expire in 24 hours for your security. 
                If you didn't create this account or request this code, please ignore this email.
            </div>
            
            {% if not is_resend %}
            <p>
                Once verified, you'll have access to all the powerful features of {{ site_name }}:
            </p>
            <ul>
                <li>📊 Professional inventory management</li>
                <li>🏪 Private seller store</li>
                <li>🌍 Public marketplace access</li>
                <li>📱 Mobile-responsive dashboard</li>
                <li>📈 Sales analytics and reporting</li>
            </ul>
            {% endif %}
        </div>
        
        <div class="footer">
            <p>
                <strong>{{ site_name }}</strong><br>
                Professional Inventory Management Solution
            </p>
            
            <div class="footer-links">
                <a href="#">Help Center</a> |
                <a href="#">Contact Support</a> |
                <a href="#">Privacy Policy</a>
            </div>
            
            <p>
                If you're having trouble with verification, please contact our support team.
            </p>
            
            <p style="font-size: 0.8rem; color: #999;">
                This email was sent to {{ user.email }}. If you didn't request this verification, 
                please ignore this email.
            </p>
        </div>
    </div>
</body>
</html>
