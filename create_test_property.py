#!/usr/bin/env python
import os
import sys
import django
from decimal import Decimal

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'InventoryM.settings')
django.setup()

from django.contrib.auth.models import User
from IManagement.models import Property

def create_test_property():
    # Get the first user (or create one)
    try:
        user = User.objects.first()
        if not user:
            user = User.objects.create_user(
                username='testuser',
                email='<EMAIL>',
                password='testpass123'
            )
            print(f"Created test user: {user.username}")
        else:
            print(f"Using existing user: {user.username}")
        
        # Create a test property for sale
        property_obj = Property.objects.create(
            owner=user,
            name="Beautiful Test House",
            description="A lovely test property with great features",
            property_type="house",
            condition="excellent",
            location="Test City, Test State",
            purchase_price=Decimal("250000.00"),
            current_value=Decimal("300000.00"),
            for_sale=True,
            sale_price=Decimal("295000.00"),
            is_negotiable=True,
            sale_description="Perfect family home in great neighborhood. Recently renovated!",
            contact_phone="************",
            contact_email=user.email,
            notes="Test property for demonstration"
        )
        
        print(f"Created test property: {property_obj.name}")
        print(f"Property ID: {property_obj.id}")
        print(f"For Sale: {property_obj.for_sale}")
        print(f"Sale Price: ${property_obj.sale_price}")
        
        return property_obj
        
    except Exception as e:
        print(f"Error creating test property: {e}")
        return None

if __name__ == "__main__":
    property_obj = create_test_property()
    if property_obj:
        print("\n✅ Test property created successfully!")
        print(f"Visit: http://127.0.0.1:8001/seller/{property_obj.owner.id}/store/ to see it in the store")
        print(f"Visit: http://127.0.0.1:8001/properties-for-sale/ to see it in the properties for sale page")
    else:
        print("\n❌ Failed to create test property")
