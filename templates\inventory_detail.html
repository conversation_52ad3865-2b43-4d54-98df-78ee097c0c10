{% extends 'base.html' %}

{% block title %}{{ inventory.name }} - Inventory Details{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'stores_list_create' %}">Stores</a></li>
            <li class="breadcrumb-item"><a href="{% url 'inventories_list_create' %}">Inventories</a></li>
            <li class="breadcrumb-item active">{{ inventory.name }}</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>{{ inventory.name }}</h2>
            <p class="text-muted mb-0">
                <strong>Store:</strong> 
                {% if inventory.store %}
                    <a href="{% url 'store_detail' inventory.store.id %}">{{ inventory.store.name }}</a>
                {% else %}
                    No store assigned
                {% endif %}
            </p>
            <p class="text-muted mb-0"><strong>Quantity:</strong> {{ inventory.quantity }}</p>
            <small class="text-muted">Created: {{ inventory.created_at|date:"M d, Y H:i" }}</small>
        </div>
        <div class="btn-group">
            <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editInventoryModal">
                <i class="fas fa-edit"></i> Edit
            </button>
            <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteInventoryModal">
                <i class="fas fa-trash"></i> Delete
            </button>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Goods in this Inventory</h5>
                    <a href="{% url 'goods_list_create' %}?inventory_id={{ inventory.id }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus"></i> Add Goods
                    </a>
                </div>
                <div class="card-body">
                    {% if inventory.goods.all %}
                        <div class="row">
                            {% for good in inventory.goods.all %}
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card">
                                        {% if good.image %}
                                            <img src="{{ good.image.url }}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="{{ good.name }}">
                                        {% else %}
                                            <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 150px;">
                                                <i class="fas fa-image fa-2x text-muted"></i>
                                            </div>
                                        {% endif %}
                                        <div class="card-body p-3">
                                            <h6 class="card-title mb-2">{{ good.name }}</h6>
                                            <p class="card-text mb-2">
                                                <strong class="text-success">${{ good.price }}</strong>
                                            </p>
                                            <p class="card-text text-muted small">{{ good.description|truncatewords:10 }}</p>
                                            <div class="btn-group w-100 btn-group-sm">
                                                <a href="{% url 'goods_detail' good.id %}" class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <a href="{% url 'public_goods_detail' good.id %}" class="btn btn-outline-info" target="_blank">
                                                    <i class="fas fa-external-link-alt"></i> Public
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-2x text-muted mb-3"></i>
                            <h6>No goods yet</h6>
                            <p class="text-muted">Add your first goods to this inventory</p>
                            <a href="{% url 'goods_list_create' %}?inventory_id={{ inventory.id }}" class="btn btn-primary">
                                Add Goods
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">Inventory Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">{{ inventory.goods.count }}</h4>
                                <small class="text-muted">Goods</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-success">{{ inventory.quantity }}</h4>
                            <small class="text-muted">Quantity</small>
                        </div>
                    </div>
                </div>
            </div>
            
            {% if inventory.store %}
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">Store Information</h5>
                </div>
                <div class="card-body">
                    <h6>{{ inventory.store.name }}</h6>
                    <p class="text-muted">{{ inventory.store.description|default:"No description" }}</p>
                    <a href="{% url 'store_detail' inventory.store.id %}" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-store"></i> View Store
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Edit Inventory Modal -->
<div class="modal fade" id="editInventoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Inventory</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editInventoryForm" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editInventoryName" class="form-label">Inventory Name *</label>
                        <input type="text" class="form-control" id="editInventoryName" name="name" value="{{ inventory.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="editInventoryQuantity" class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="editInventoryQuantity" name="quantity" value="{{ inventory.quantity }}" min="0" required>
                    </div>
                    <div class="mb-3">
                        <label for="editInventoryStore" class="form-label">Store</label>
                        <select class="form-select" id="editInventoryStore" name="store_id">
                            <option value="">No Store</option>
                            {% for store in stores %}
                                <option value="{{ store.id }}" {% if store.id == inventory.store.id %}selected{% endif %}>{{ store.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Inventory</button>
                </div>
            </form>
        </div>
    </div>
</div>


<!-- Delete Inventory Modal -->
<div class="modal fade" id="deleteInventoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Inventory</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete this inventory item?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" onclick="deleteInventory()">Delete Inventory</button>
            </div>
        </div>
    </div>
</div>


<script>
// Load stores when edit modal opens
document.getElementById('editInventoryModal').addEventListener('show.bs.modal', function() {
    loadStoresForEdit();
});

function loadStoresForEdit() {
    fetch('{% url "stores_list_create" %}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.text())
    .then(html => {
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const storeCards = doc.querySelectorAll('.card');
        const storeSelect = document.getElementById('editStoreSelect');
        
        // Clear existing options except the first one
        storeSelect.innerHTML = '<option value="">No store</option>';
        
        // Extract store information from cards
        storeCards.forEach(card => {
            const titleElement = card.querySelector('.card-title');
            const viewLink = card.querySelector('a[href*="store_detail"]');
            
            if (titleElement && viewLink) {
                const storeName = titleElement.textContent.trim();
                const href = viewLink.getAttribute('href');
                const storeId = href.split('/').filter(part => part).pop();
                
                if (storeName && storeId) {
                    const option = document.createElement('option');
                    option.value = storeId;
                    option.textContent = storeName;
                    storeSelect.appendChild(option);
                }
            }
        });
        
        // Set current store as selected
        {% if inventory.store %}
            storeSelect.value = '{{ inventory.store.id }}';
        {% endif %}
    })
    .catch(error => {
        console.error('Error loading stores:', error);
    });
}

document.getElementById('editInventoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('editInventoryName').value,
        quantity: document.getElementById('editInventoryQuantity').value,
        store_id: document.getElementById('editInventoryStore').value
    };

    fetch('{% url "inventory_detail" inventory.id %}', {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            alert(data.message);
            window.location.reload();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating inventory');
    });
});

function deleteInventory() {
    fetch('{% url "inventory_detail" inventory.id %}', {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            alert(data.message);
            window.location.href = '{% url "inventories_list_create" %}';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error deleting inventory');
    });
}
</script>
{% endblock %}