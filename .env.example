# Django Settings
DJANGO_SECRET_KEY=your-secret-key-here
DJANGO_DEBUG=True
ALLOWED_HOSTS=127.0.0.1,localhost,example.com

# Gmail Configuration (Recommended)
# 1. Enable 2-Factor Authentication on your Gmail account
# 2. Generate an App Password: Google Account > Security > App passwords
# 3. Use the 16-character app password below (not your regular password)
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-16-character-app-password-here
DEFAULT_FROM_EMAIL=<EMAIL>

# Alternative Email Providers:
# For Outlook/Hotmail:
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-app-password
# DEFAULT_FROM_EMAIL=<EMAIL>

# For Yahoo:
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-app-password
# DEFAULT_FROM_EMAIL=<EMAIL>

# Database Configuration (Optional - defaults to SQLite)
DJ<PERSON>GO_DB_ENGINE=django.db.backends.sqlite3
DJANGO_DB_NAME=db.sqlite3
DJANGO_DB_USER=
DJANGO_DB_PASSWORD=
DJANGO_DB_HOST=
DJANGO_DB_PORT=
