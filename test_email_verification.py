#!/usr/bin/env python
"""
Test script to verify email verification system is working
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'InventoryM.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings
from django.template.loader import render_to_string
from django.contrib.auth.models import User

def test_email_sending():
    """Test basic email sending functionality"""
    print("🧪 Testing email sending...")
    
    try:
        # Check email settings
        print(f"📧 Email Host User: {settings.EMAIL_HOST_USER}")
        print(f"📧 Default From Email: {settings.DEFAULT_FROM_EMAIL}")
        
        if not settings.EMAIL_HOST_USER or not settings.EMAIL_HOST_PASSWORD:
            print("❌ Email credentials not configured properly")
            return False
        
        # Test sending a simple email
        print("\n📤 Sending test email...")
        
        send_mail(
            subject='✅ Email Configuration Test - Inventory Management System',
            message='Congratulations! Your email configuration is working correctly. The email verification system is ready to use.',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.EMAIL_HOST_USER],
            fail_silently=False,
        )
        
        print("✅ Test email sent successfully!")
        print(f"📧 Check your inbox: {settings.EMAIL_HOST_USER}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error sending email: {str(e)}")
        return False

def test_verification_email_template():
    """Test the verification email template"""
    print("\n🧪 Testing verification email template...")
    
    try:
        # Create a mock user for testing
        mock_user = User(
            first_name='Test',
            last_name='User',
            email=settings.EMAIL_HOST_USER,
            username='testuser'
        )
        
        verification_code = '123456'
        
        # Test HTML email template
        html_message = render_to_string('emails/verification_email.html', {
            'user': mock_user,
            'verification_code': verification_code,
            'site_name': 'Inventory Management System'
        })
        
        print("✅ Email template rendered successfully")
        
        # Send the verification email
        print("📤 Sending verification email template test...")
        
        plain_message = f"""
Hello {mock_user.first_name} {mock_user.last_name},

This is a test of the verification email template.

Your verification code is: {verification_code}

Best regards,
Inventory Management System Team
        """.strip()
        
        send_mail(
            subject='🔐 Email Verification Test - Inventory Management System',
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.EMAIL_HOST_USER],
            html_message=html_message,
            fail_silently=False,
        )
        
        print("✅ Verification email template sent successfully!")
        print("📧 Check your inbox for the beautiful verification email")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing verification email: {str(e)}")
        return False

def test_registration_flow():
    """Test the complete registration flow"""
    print("\n🧪 Testing registration flow...")
    
    try:
        from django.test import Client
        
        client = Client()
        
        # Test data
        test_data = {
            'first_name': 'Email',
            'last_name': 'Test',
            'username': 'emailtest',
            'email': settings.EMAIL_HOST_USER,
            'password1': 'testpass123',
            'password2': 'testpass123'
        }
        
        # Clean up any existing test user
        try:
            existing_user = User.objects.get(username=test_data['username'])
            existing_user.delete()
            print("🧹 Cleaned up existing test user")
        except User.DoesNotExist:
            pass
        
        print("📝 Testing registration with email sending...")
        
        # Test registration (this should send an email)
        response = client.post('/register/', 
                             data=test_data, 
                             content_type='application/json')
        
        print(f"✅ Registration response - Status: {response.status_code}")
        
        if response.status_code == 201:
            response_data = response.json()
            print(f"📧 Email should be sent to: {response_data.get('email')}")
            print("📧 Check your inbox for the verification email!")
            
            # Clean up test user
            try:
                test_user = User.objects.get(username=test_data['username'])
                test_user.delete()
                print("🧹 Cleaned up test user")
            except User.DoesNotExist:
                pass
            
            return True
        else:
            print(f"❌ Registration failed with status: {response.status_code}")
            if hasattr(response, 'json'):
                print(f"Error: {response.json()}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing registration flow: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎯 Testing Email Verification System")
    print("=" * 50)
    
    # Test 1: Basic email sending
    test1 = test_email_sending()
    
    # Test 2: Verification email template
    test2 = test_verification_email_template()
    
    # Test 3: Complete registration flow
    test3 = test_registration_flow()
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"✅ Basic Email Sending: {'PASS' if test1 else 'FAIL'}")
    print(f"✅ Verification Template: {'PASS' if test2 else 'FAIL'}")
    print(f"✅ Registration Flow: {'PASS' if test3 else 'FAIL'}")
    
    if test1 and test2 and test3:
        print("\n🎉 All tests passed! Your email verification system is working perfectly!")
        print("📧 Check your email inbox to see the test emails")
        print("🚀 Your system is ready for production use!")
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
    
    sys.exit(0 if (test1 and test2 and test3) else 1)
