from django import forms
from .models import Store, Country, State, Category
from django.contrib.auth.forms import UserCreationForm, PasswordChangeForm
from django.contrib.auth.models import User
from django_countries.fields import CountryField

# Custom User Creation Form (with email)
class CustomUserCreationForm(UserCreationForm):
    email = forms.EmailField(
        required=True, 
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )

    class Meta:
        model = User
        fields = ("username", "email", "password1", "password2")

    def clean_email(self):
        email = self.cleaned_data.get("email")
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError("A user with this email already exists.")
        return email

# Custom User Change Form for updating profile
class CustomUserChangeForm(forms.ModelForm):
    class Meta:
        model = User
        fields = ('username', 'email', 'first_name', 'last_name')

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in ['username', 'email', 'first_name', 'last_name']:
            self.fields[field].widget.attrs.update({'class': 'form-control'})

# Custom User Creation Form (with country selection)
class CustomUserCreationFormWithCountry(UserCreationForm):
    email = forms.EmailField(
        required=True, 
        widget=forms.EmailInput(attrs={'class': 'form-control'})
    )
    country = CountryField().formfield()  # This will use django-countries to populate the country field
    state = forms.CharField(max_length=100, required=False, widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Optional: Enter state'}))

    class Meta:
        model = User
        fields = ("username", "email", "password1", "password2", "country", "state")

    def clean_email(self):
        email = self.cleaned_data.get("email")
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError("A user with this email already exists.")
        return email


# Enhanced User Creation Form with Email Verification
class EnhancedUserCreationForm(UserCreationForm):
    first_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'First Name'})
    )
    last_name = forms.CharField(
        max_length=30,
        required=True,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'Last Name'})
    )
    email = forms.EmailField(
        required=True,
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'Email Address'})
    )
    country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        required=True,
        empty_label="Select Country",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        required=False,
        empty_label="Select State (Optional)",
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    class Meta:
        model = User
        fields = ("first_name", "last_name", "username", "email", "password1", "password2")

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Add CSS classes to all fields
        for field_name, field in self.fields.items():
            field.widget.attrs.update({'class': 'form-control'})

        # If country is selected, populate states
        if 'country' in self.data:
            try:
                country_id = int(self.data.get('country'))
                self.fields['state'].queryset = State.objects.filter(country_id=country_id).order_by('name')
            except (ValueError, TypeError):
                pass

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError("A user with this email already exists.")
        return email


# Email Verification Form
class EmailVerificationForm(forms.Form):
    verification_code = forms.CharField(
        max_length=6,
        min_length=6,
        required=True,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': '000000',
            'maxlength': '6',
            'style': 'text-align: center; font-size: 2rem; letter-spacing: 0.5em;'
        })
    )
    user_id = forms.IntegerField(widget=forms.HiddenInput())

    def clean_verification_code(self):
        code = self.cleaned_data.get('verification_code')
        if not code.isdigit():
            raise forms.ValidationError("Verification code must contain only numbers.")
        return code


# Custom Password Change Form
class CustomPasswordChangeForm(PasswordChangeForm):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        for field in ['old_password', 'new_password1', 'new_password2']:
            self.fields[field].widget.attrs.update({'class': 'form-control'})

# Store Form
class StoreForm(forms.ModelForm):
    class Meta:
        model = Store
        fields = ['name', 'description', 'location']  # Ensure you have these fields in your model
        widgets = {
            'description': forms.Textarea(attrs={'rows': 4, 'placeholder': 'Enter store description'}),
            'location': forms.TextInput(attrs={'placeholder': 'Enter store location'})
        }


# Enhanced Public Goods Filter Form with Country and Star Rating
class PublicGoodsFilterForm(forms.Form):
    search = forms.CharField(
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Search goods by name, description, SKU...'
        })
    )

    country = forms.ModelChoiceField(
        queryset=Country.objects.all(),
        required=False,
        empty_label="🌍 All Countries",
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    state = forms.ModelChoiceField(
        queryset=State.objects.none(),
        required=False,
        empty_label="📍 All States",
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    star_rating = forms.ChoiceField(
        choices=[('', '⭐ All Ratings')] + [(i, f'{"⭐" * i} {i} Star{"s" if i > 1 else ""}') for i in range(1, 6)],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    min_rating = forms.ChoiceField(
        choices=[('', 'Min Rating')] + [(i, f'{"⭐" * i}+ ({i}+ Stars)') for i in range(1, 6)],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    category = forms.ModelChoiceField(
        queryset=Category.objects.all(),
        required=False,
        empty_label="📂 All Categories",
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    min_price = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Min Price ($)',
            'step': '0.01'
        })
    )

    max_price = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Max Price ($)',
            'step': '0.01'
        })
    )

    condition = forms.ChoiceField(
        choices=[('', 'All Conditions'), ('new', '🆕 New'), ('used', '♻️ Used')],
        required=False,
        widget=forms.Select(attrs={'class': 'form-control'})
    )

    available_for_delivery = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

    in_stock_only = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={'class': 'form-check-input'})
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # If country is selected, populate states
        if 'country' in self.data:
            try:
                country_id = int(self.data.get('country'))
                self.fields['state'].queryset = State.objects.filter(country_id=country_id).order_by('name')
            except (ValueError, TypeError):
                pass


# Private Store Inventory Management Form
class PrivateStoreInventoryForm(forms.Form):
    goods_id = forms.IntegerField(widget=forms.HiddenInput())
    action = forms.ChoiceField(
        choices=[
            ('sell', 'Sell Stock'),
            ('add', 'Add Stock'),
            ('adjust', 'Adjust Stock'),
        ],
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    quantity = forms.IntegerField(
        min_value=1,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Quantity',
            'min': '1'
        })
    )
    unit_price = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Unit Price (optional)',
            'step': '0.01'
        })
    )
    cost_price = forms.DecimalField(
        max_digits=10,
        decimal_places=2,
        required=False,
        widget=forms.NumberInput(attrs={
            'class': 'form-control',
            'placeholder': 'Cost Price (optional)',
            'step': '0.01'
        })
    )
    notes = forms.CharField(
        max_length=500,
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'form-control',
            'placeholder': 'Notes (optional)',
            'rows': 2
        })
    )
    reference_number = forms.CharField(
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'Reference Number (optional)'
        })
    )


# Alias for EmailVerificationForm for backward compatibility
VerificationCodeForm = EmailVerificationForm