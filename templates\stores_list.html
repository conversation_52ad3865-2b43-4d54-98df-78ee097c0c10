{% extends 'base.html' %}

{% block title %}My Stores{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Alert container for dynamic messages -->
    <div id="alert-container"></div>
    
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>My Stores</h2>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createStoreModal">
            <i class="fas fa-plus"></i> Create New Store
        </button>
    </div>

    <!-- Stores List Container -->
    <div id="stores-container">
        {% if stores %}
            <div class="row" id="stores-list">
                {% for store in stores %}
                    <div class="col-md-6 col-lg-4 mb-4" data-store-id="{{ store.id }}">
                        <div class="card h-100 store-card">
                            <div class="card-body">
                                <h5 class="card-title">{{ store.name }}</h5>
                                <p class="card-text">{{ store.description|default:"No description" }}</p>
                                <small class="text-muted">Created: {{ store.created_at|date:"M d, Y" }}</small>
                            </div>
                            <div class="card-footer">
                                <div class="btn-group w-100" role="group">
                                    <a href="{% url 'store_detail' store.id %}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <button class="btn btn-outline-info btn-sm" onclick="quickShare({{ store.id }}, '{{ store.name|escapejs }}')">
                                        <i class="fas fa-share-alt"></i> Share
                                    </button>
                                    <button class="btn btn-outline-warning btn-sm" onclick="editStore({{ store.id }}, '{{ store.name|escapejs }}', '{{ store.description|escapejs }}')">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm" onclick="deleteStore({{ store.id }})">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="text-center py-5" id="no-stores-message">
                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                <h4>No stores yet</h4>
                <p class="text-muted">Create your first store to get started</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createStoreModal">
                    Create Store
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Create Store Modal -->
<div class="modal fade" id="createStoreModal" tabindex="-1" aria-labelledby="createStoreModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createStoreModalLabel">Create New Store</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createStoreForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="storeName" class="form-label">Store Name *</label>
                        <input type="text" class="form-control" id="storeName" name="name" required>
                        <div class="invalid-feedback" id="nameError"></div>
                    </div>
                    <div class="mb-3">
                        <label for="storeDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="storeDescription" name="description" rows="3"></textarea>
                        <div class="invalid-feedback" id="descriptionError"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="createStoreBtn">
                        Create Store
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Store Modal -->
<div class="modal fade" id="editStoreModal" tabindex="-1" aria-labelledby="editStoreModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editStoreModalLabel">Edit Store</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editStoreForm">
                {% csrf_token %}
                <input type="hidden" id="editStoreId">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editStoreName" class="form-label">Store Name *</label>
                        <input type="text" class="form-control" id="editStoreName" name="name" required>
                        <div class="invalid-feedback" id="editNameError"></div>
                    </div>
                    <div class="mb-3">
                        <label for="editStoreDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editStoreDescription" name="description" rows="3"></textarea>
                        <div class="invalid-feedback" id="editDescriptionError"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="editStoreBtn">
                        Update Store
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.store-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-left: 4px solid #007bff;
}

.store-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn-group .btn {
    flex: 1;
}

.fade-out {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.3s ease;
}

.alert {
    margin-bottom: 1rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeForms();
});

function initializeForms() {
    // Create form
    const createForm = document.getElementById('createStoreForm');
    const createModal = document.getElementById('createStoreModal');
    
    createForm.addEventListener('submit', handleCreateSubmit);
    createModal.addEventListener('hidden.bs.modal', () => resetForm('createStoreForm'));
    
    // Edit form
    const editForm = document.getElementById('editStoreForm');
    const editModal = document.getElementById('editStoreModal');
    
    editForm.addEventListener('submit', handleEditSubmit);
    editModal.addEventListener('hidden.bs.modal', () => resetForm('editStoreForm'));
}

function handleCreateSubmit(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('createStoreBtn');
    const nameInput = document.getElementById('storeName');
    const descriptionInput = document.getElementById('storeDescription');
    
    if (submitBtn.disabled) return;
    
    clearFormErrors('createStoreForm');
    
    const formData = {
        name: nameInput.value.trim(),
        description: descriptionInput.value.trim()
    };
    
    if (!formData.name) {
        showFieldError('storeName', 'Store name is required');
        return;
    }
    
    setLoadingState(submitBtn, 'Creating...');
    
    fetch('{% url "stores_list_create" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(formData)
    })
    .then(async response => {
        const data = await response.json().catch(() => ({}));
        
        if (response.ok) {
            hideModal('createStoreModal');
            resetForm('createStoreForm');
            showAlert('success', 'Store created successfully!');
            
            if (data.store) {
                addStoreToList(data.store);
            } else {
                setTimeout(() => window.location.reload(), 1000);
            }
        } else {
            if (data.errors) {
                handleFormErrors(data.errors, 'create');
            } else {
                showAlert('danger', data.error || 'Failed to create store');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Network error occurred');
    })
    .finally(() => {
        resetLoadingState(submitBtn, 'Create Store');
    });
}

function handleEditSubmit(e) {
    e.preventDefault();
    
    const submitBtn = document.getElementById('editStoreBtn');
    const storeId = document.getElementById('editStoreId').value;
    const nameInput = document.getElementById('editStoreName');
    const descriptionInput = document.getElementById('editStoreDescription');
    
    if (submitBtn.disabled) return;
    
    clearFormErrors('editStoreForm');
    
    const formData = {
        name: nameInput.value.trim(),
        description: descriptionInput.value.trim()
    };
    
    if (!formData.name) {
        showFieldError('editStoreName', 'Store name is required');
        return;
    }
    
    setLoadingState(submitBtn, 'Updating...');
    
    fetch(`{% url 'stores_list_create' %}${storeId}/`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(formData)
    })
    .then(async response => {
        const data = await response.json().catch(() => ({}));
        
        if (response.ok) {
            hideModal('editStoreModal');
            resetForm('editStoreForm');
            showAlert('success', 'Store updated successfully!');
            updateStoreInList(storeId, data.store || formData);
        } else {
            if (data.errors) {
                handleFormErrors(data.errors, 'edit');
            } else {
                showAlert('danger', data.error || 'Failed to update store');
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Network error occurred');
    })
    .finally(() => {
        resetLoadingState(submitBtn, 'Update Store');
    });
}

function editStore(storeId, name, description) {
    document.getElementById('editStoreId').value = storeId;
    document.getElementById('editStoreName').value = name;
    document.getElementById('editStoreDescription').value = description;
    
    const modal = new bootstrap.Modal(document.getElementById('editStoreModal'));
    modal.show();
}

function deleteStore(storeId) {
    if (!confirm('Are you sure you want to delete this store? This action cannot be undone.')) {
        return;
    }
    
    const storeCard = document.querySelector(`[data-store-id="${storeId}"]`);
    
    fetch(`{% url 'stores_list_create' %}${storeId}/`, {
        method: 'DELETE',
        headers: {
            'X-CSRFToken': getCookie('csrftoken')
        }
    })
    .then(async response => {
        if (response.ok) {
            showAlert('success', 'Store deleted successfully!');
            
            if (storeCard) {
                storeCard.classList.add('fade-out');
                setTimeout(() => {
                    storeCard.remove();
                    checkIfNoStores();
                }, 300);
            }
        } else {
            const data = await response.json().catch(() => ({}));
            showAlert('danger', data.error || 'Failed to delete store');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('danger', 'Network error occurred');
    });
}

function addStoreToList(store) {
    const storesContainer = document.getElementById('stores-container');
    const noStoresMessage = document.getElementById('no-stores-message');
    
    if (noStoresMessage) {
        noStoresMessage.remove();
    }
    
    let storesList = document.getElementById('stores-list');
    if (!storesList) {
        storesList = document.createElement('div');
        storesList.className = 'row';
        storesList.id = 'stores-list';
        storesContainer.appendChild(storesList);
    }
    
    const storeCard = document.createElement('div');
    storeCard.className = 'col-md-6 col-lg-4 mb-4';
    storeCard.setAttribute('data-store-id', store.id);
    storeCard.innerHTML = `
        <div class="card h-100 store-card">
            <div class="card-body">
                <h5 class="card-title">${escapeHtml(store.name)}</h5>
                <p class="card-text">${escapeHtml(store.description || 'No description')}</p>
                <small class="text-muted">Created: ${formatDate(store.created_at)}</small>
            </div>
            <div class="card-footer">
                <div class="btn-group w-100" role="group">
                    <a href="/stores/${store.id}/" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-eye"></i> View
                    </a>
                    <button class="btn btn-outline-warning btn-sm" onclick="editStore(${store.id}, '${escapeHtml(store.name)}', '${escapeHtml(store.description || '')}')">
                        <i class="fas fa-edit"></i> Edit
                    </button>
                    <button class="btn btn-outline-danger btn-sm" onclick="deleteStore(${store.id})">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
        </div>
    `;
    
    storesList.insertBefore(storeCard, storesList.firstChild);
}

function updateStoreInList(storeId, storeData) {
    const storeCard = document.querySelector(`[data-store-id="${storeId}"]`);
    if (storeCard) {
        const titleElement = storeCard.querySelector('.card-title');
        const textElement = storeCard.querySelector('.card-text');
        const editButton = storeCard.querySelector('.btn-outline-warning');
        
        if (titleElement) titleElement.textContent = storeData.name;
        if (textElement) textElement.textContent = storeData.description || 'No description';
        if (editButton) {
            editButton.setAttribute('onclick', `editStore(${storeId}, '${escapeHtml(storeData.name)}', '${escapeHtml(storeData.description || '')}')`);
        }
    }
}

function checkIfNoStores() {
    const storesList = document.getElementById('stores-list');
    const storesContainer = document.getElementById('stores-container');
    
    if (!storesList || storesList.children.length === 0) {
        storesContainer.innerHTML = `
            <div class="text-center py-5" id="no-stores-message">
                <i class="fas fa-store fa-3x text-muted mb-3"></i>
                <h4>No stores yet</h4>
                <p class="text-muted">Create your first store to get started</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createStoreModal">
                    Create Store
                </button>
            </div>
        `;
    }
}

// Utility functions
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    const modalInstance = bootstrap.Modal.getInstance(modal);
    if (modalInstance) {
        modalInstance.hide();
    }
}

function resetForm(formId) {
    const form = document.getElementById(formId);
    form.reset();
    clearFormErrors(formId);
}

function clearFormErrors(formId) {
    const form = document.getElementById(formId);
    const inputs = form.querySelectorAll('.form-control');
    const errors = form.querySelectorAll('.invalid-feedback');
    
    inputs.forEach(input => input.classList.remove('is-invalid'));
    errors.forEach(error => error.textContent = '');
}

function showFieldError(fieldId, message) {
    const field = document.getElementById(fieldId);
    const errorId = fieldId.includes('edit') ? 
        fieldId.replace('editStore', 'edit').replace('Name', '') + 'Error' :
        fieldId.replace('store', '').toLowerCase() + 'Error';
    const errorElement = document.getElementById(errorId);
    
    if (field && errorElement) {
        field.classList.add('is-invalid');
        errorElement.textContent = message;
    }
}

function handleFormErrors(errors, type) {
    Object.keys(errors).forEach(field => {
        const fieldId = type === 'edit' ? `editStore${field === 'name' ? 'Name' : field}` : `store${field === 'name' ? 'Name' : field}`;
        const message = Array.isArray(errors[field]) ? errors[field][0] : errors[field];
        showFieldError(fieldId, message);
    });
}

function setLoadingState(button, text) {
    button.disabled = true;
    button.innerHTML = `<span class="spinner-border spinner-border-sm me-2" role="status"></span>${text}`;
}

function resetLoadingState(button, text) {
    button.disabled = false;
    button.innerHTML = text;
}

function showAlert(type, message) {
    const alertContainer = document.getElementById('alert-container');
    const alertId = 'alert-' + Date.now();
    
    const alertDiv = document.createElement('div');
    alertDiv.id = alertId;
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${escapeHtml(message)}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    alertContainer.appendChild(alertDiv);
    
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) alert.remove();
    }, 5000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function escapeHtml(text) {
    if (!text) return '';
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Quick Share Function
function quickShare(storeId, storeName) {
    // Create a quick share link and show it in a modal
    const data = {
        title: `Shared Store: ${storeName}`,
        description: '',
        expires_at: '',
        password_protected: false,
        access_password: ''
    };

    fetch(`/stores/${storeId}/share/create/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show the share link in a simple modal
            showQuickShareModal(data.share_url, storeName, storeId);
        } else {
            showAlert('Error: ' + (data.error || 'Failed to create share link'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while creating the share link', 'danger');
    });
}

// Show quick share modal
function showQuickShareModal(shareUrl, storeName, storeId) {
    const modalHtml = `
        <div class="modal fade" id="quickShareModal" tabindex="-1" data-store-id="${storeId}">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Share Store: ${escapeHtml(storeName)}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p>Your store has been shared! Copy the link below:</p>
                        <div class="input-group">
                            <input type="text" class="form-control" value="${shareUrl}" readonly id="quickShareUrl">
                            <button class="btn btn-outline-secondary" onclick="copyQuickShareLink()">
                                <i class="fas fa-copy"></i> Copy
                            </button>
                        </div>
                        <div class="mt-3">
                            <a href="${shareUrl}" target="_blank" class="btn btn-primary">
                                <i class="fas fa-external-link-alt"></i> Open Shared Store
                            </a>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        <a href="/stores/${storeId}/" class="btn btn-info">
                            <i class="fas fa-cog"></i> Advanced Sharing
                        </a>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('quickShareModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('quickShareModal'));
    modal.show();

    // Remove modal from DOM when hidden
    document.getElementById('quickShareModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

// Copy quick share link
function copyQuickShareLink() {
    const linkInput = document.getElementById('quickShareUrl');
    linkInput.select();
    linkInput.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        showAlert('Share link copied to clipboard!', 'success');
    } catch (err) {
        console.error('Failed to copy: ', err);
        showAlert('Failed to copy link. Please copy manually.', 'warning');
    }
}

// Extract store ID from share URL (helper function)
function getStoreIdFromShareUrl(shareUrl) {
    // For the quick share modal, we need to get the store ID from the context
    // Since we're in the stores list, we can get it from the button that was clicked
    const currentStoreId = document.querySelector('.modal')?.getAttribute('data-store-id');
    if (currentStoreId) {
        return currentStoreId;
    }

    // Fallback: try to extract from URL patterns
    const match = shareUrl.match(/stores\/(\d+)/);
    return match ? match[1] : '';
}

// Get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}