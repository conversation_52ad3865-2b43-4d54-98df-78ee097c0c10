{% extends 'base.html' %}
{% load static %}

{% block title %}My Categories{% endblock %}

{% block extra_css %}
<style>
    .category-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        border: none;
        border-radius: 12px;
        overflow: hidden;
    }
    .category-card:hover {
        transform: translateY(-4px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }
    .category-actions {
        opacity: 0;
        transition: opacity 0.2s ease-in-out;
    }
    .category-card:hover .category-actions {
        opacity: 1;
    }
    .btn-create {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
        transition: all 0.3s ease;
        border-radius: 8px;
        font-weight: 500;
        padding: 12px 24px;
    }
    .btn-create:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        color: white;
    }
    .empty-state {
        text-align: center;
        padding: 4rem 2rem;
        color: #6c757d;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 16px;
        border: 2px dashed #dee2e6;
    }
    .empty-state i {
        font-size: 5rem;
        margin-bottom: 1.5rem;
        opacity: 0.4;
        color: #667eea;
    }
    .category-icon {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }
    .category-stats {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 8px;
        padding: 0.75rem;
        margin-top: 1rem;
    }
    .header-section {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem;
        border-radius: 16px;
        margin-bottom: 2rem;
    }
    .dropdown-toggle::after {
        display: none;
    }
    .btn-action {
        padding: 0.375rem 0.75rem;
        border-radius: 6px;
        border: 1px solid #dee2e6;
        background: white;
        transition: all 0.2s ease;
    }
    .btn-action:hover {
        background: #f8f9fa;
        border-color: #adb5bd;
    }
    .modal-content {
        border: none;
        border-radius: 16px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    .modal-header {
        border-bottom: 1px solid #f1f3f4;
        padding: 1.5rem;
    }
    .modal-body {
        padding: 1.5rem;
    }
    .modal-footer {
        border-top: 1px solid #f1f3f4;
        padding: 1.5rem;
    }
    .form-control {
        border-radius: 8px;
        border: 1px solid #dee2e6;
        padding: 0.75rem 1rem;
        transition: all 0.2s ease;
    }
    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    .toast {
        border-radius: 12px;
        border: none;
        backdrop-filter: blur(10px);
    }
    .category-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 1.5rem;
    }
    @media (max-width: 768px) {
        .category-grid {
            grid-template-columns: 1fr;
        }
        .header-section {
            text-align: center;
        }
        .header-section .d-flex {
            flex-direction: column;
            gap: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- Header Section -->
    <div class="header-section">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="mb-2 fw-bold">My Categories</h1>
                <p class="mb-0 opacity-75">Organize your goods with custom categories</p>
            </div>
            <button class="btn btn-create" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                <i class="fas fa-plus me-2"></i>Create Category
            </button>
        </div>
    </div>

    <!-- Categories Grid -->
    <div id="categoriesContainer">
        <!-- Server-side rendered categories (fallback) -->
        {% if categories %}
            <div class="category-grid">
                {% for category in categories %}
                <div class="category-card bg-white shadow-sm">
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="category-icon">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div class="category-actions">
                                <div class="dropdown">
                                    <button class="btn btn-action btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end shadow">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="editCategory({{ category.id }}, '{{ category.name|escapejs }}')">
                                                <i class="fas fa-edit me-2 text-warning"></i>Edit Category
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-danger" href="#" onclick="confirmDeleteCategory({{ category.id }}, '{{ category.name|escapejs }}')">
                                                <i class="fas fa-trash me-2"></i>Delete Category
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <h5 class="card-title fw-bold mb-2">{{ category.name }}</h5>
                            <small class="text-muted">
                                <i class="fas fa-calendar-plus me-1"></i>
                                Created: {{ category.created_at|date:"M d, Y" }}
                            </small>
                        </div>

                        <div class="category-stats">
                            <div class="row text-center">
                                <div class="col-6">
                                    <div class="fw-bold text-primary">{{ category.good_count|default:0 }}</div>
                                    <small class="text-muted">Goods</small>
                                </div>
                                <div class="col-6">
                                    <div class="fw-bold text-success">Active</div>
                                    <small class="text-muted">Status</small>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mt-3">
                            <a href="{% url 'list_categories' %}?category={{ category.id }}" class="btn btn-outline-primary">
                                <i class="fas fa-eye me-2"></i>View Goods ({{ category.good_count|default:0 }})
                            </a>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="empty-state">
                <i class="fas fa-folder-open"></i>
                <h3 class="fw-bold mb-3">No Categories Yet</h3>
                <p class="mb-4">Create your first category to organize your goods and make management easier.</p>
                <button class="btn btn-create btn-lg" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                    <i class="fas fa-plus me-2"></i>Create Your First Category
                </button>
            </div>
        {% endif %}
    </div>
</div>

<!-- Create Category Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1" aria-labelledby="createCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold" id="createCategoryModalLabel">
                    <i class="fas fa-plus-circle me-2 text-primary"></i>Create New Category
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createCategoryForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label fw-semibold">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required 
                               placeholder="Enter category name..." maxlength="100">
                        <div class="invalid-feedback"></div>
                        <small class="text-muted">Choose a descriptive name for your category</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-create">
                        <span class="spinner-border spinner-border-sm me-2 d-none" id="createSpinner"></span>
                        <i class="fas fa-check me-2"></i>Create Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="editCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title fw-bold" id="editCategoryModalLabel">
                    <i class="fas fa-edit me-2 text-warning"></i>Edit Category
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editCategoryForm">
                <div class="modal-body">
                    <input type="hidden" id="editCategoryId">
                    <div class="mb-3">
                        <label for="editCategoryName" class="form-label fw-semibold">Category Name</label>
                        <input type="text" class="form-control" id="editCategoryName" name="name" required maxlength="100">
                        <div class="invalid-feedback"></div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" class="btn btn-warning text-white">
                        <span class="spinner-border spinner-border-sm me-2 d-none" id="editSpinner"></span>
                        <i class="fas fa-save me-2"></i>Update Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Category Modal -->
<div class="modal fade" id="deleteCategoryModal" tabindex="-1" aria-labelledby="deleteCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title fw-bold text-danger" id="deleteCategoryModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Delete Category
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <div class="mb-3">
                    <i class="fas fa-trash-alt text-danger mb-3" style="font-size: 3rem; opacity: 0.7;"></i>
                    <h6 class="fw-bold">Are you sure you want to delete this category?</h6>
                    <p class="text-muted mb-3">This action cannot be undone. Goods in this category will be uncategorized.</p>
                    <div class="alert alert-light border">
                        <strong>Category:</strong> <span id="deleteCategoryName" class="text-primary"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <span class="spinner-border spinner-border-sm me-2 d-none" id="deleteSpinner"></span>
                    <i class="fas fa-trash me-2"></i>Delete Category
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    let categories = [
        {% for category in categories %}
        {
            id: {{ category.id }},
            name: "{{ category.name|escapejs }}",
            created_at: "{{ category.created_at|date:'Y-m-d' }}",
            good_count: {{ category.good_count|default:0 }}
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
    ];
    let currentEditId = null;
    let currentDeleteId = null;

    // Initialize on page load
    renderCategories();

    // Create category form handler
    document.getElementById('createCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        createCategory();
    });

    // Edit category form handler
    document.getElementById('editCategoryForm').addEventListener('submit', function(e) {
        e.preventDefault();
        updateCategory();
    });

    // Delete category confirmation
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        deleteCategory();
    });

    function renderCategories() {
        const container = document.getElementById('categoriesContainer');
        
        if (!categories || categories.length === 0) {
            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-folder-open"></i>
                    <h3 class="fw-bold mb-3">No Categories Yet</h3>
                    <p class="mb-4">Create your first category to organize your goods and make management easier.</p>
                    <button class="btn btn-create btn-lg" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                        <i class="fas fa-plus me-2"></i>Create Your First Category
                    </button>
                </div>
            `;
            return;
        }

        const categoriesHtml = categories.map(category => `
            <div class="category-card bg-white shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div class="category-icon">
                            <i class="fas fa-tag"></i>
                        </div>
                        <div class="category-actions">
                            <div class="dropdown">
                                <button class="btn btn-action btn-sm" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-ellipsis-v"></i>
                                </button>
                                <ul class="dropdown-menu dropdown-menu-end shadow">
                                    <li>
                                        <a class="dropdown-item" href="#" onclick="editCategory(${category.id}, '${escapeHtml(category.name)}')">
                                            <i class="fas fa-edit me-2 text-warning"></i>Edit Category
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="#" onclick="confirmDeleteCategory(${category.id}, '${escapeHtml(category.name)}')">
                                            <i class="fas fa-trash me-2"></i>Delete Category
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <h5 class="card-title fw-bold mb-2">${escapeHtml(category.name)}</h5>
                        <small class="text-muted">
                            <i class="fas fa-calendar-plus me-1"></i>
                            Created: ${formatDate(category.created_at || new Date())}
                        </small>
                    </div>

                    <div class="category-stats">
                        <div class="row text-center">
                            <div class="col-6">
                                <div class="fw-bold text-primary" id="goods-count-${category.id}">${category.good_count || 0}</div>
                                <small class="text-muted">Goods</small>
                            </div>
                            <div class="col-6">
                                <div class="fw-bold text-success">Active</div>
                                <small class="text-muted">Status</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-grid gap-2 mt-3">
                        <a href="/goods/?category=${category.id}" class="btn btn-outline-primary">
                            <i class="fas fa-eye me-2"></i>View Goods (${category.good_count || 0})
                        </a>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = `<div class="category-grid">${categoriesHtml}</div>`;
    }

    function createCategory() {
        const form = document.getElementById('createCategoryForm');
        const formData = new FormData(form);
        const spinner = document.getElementById('createSpinner');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        // Show loading state
        spinner.classList.remove('d-none');
        submitBtn.disabled = true;
        clearValidationErrors(form);

        fetch('/categories/create/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showValidationError(form, 'name', data.error);
            } else {
                showToast('Category created successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('createCategoryModal')).hide();
                form.reset();
                // Add new category to the list
                if (data.category) {
                    categories.push(data.category);
                    renderCategories();
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error creating category', 'error');
        })
        .finally(() => {
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    }

    function editCategory(id, name) {
        currentEditId = id;
        document.getElementById('editCategoryId').value = id;
        document.getElementById('editCategoryName').value = name;
        new bootstrap.Modal(document.getElementById('editCategoryModal')).show();
    }

    function updateCategory() {
        const form = document.getElementById('editCategoryForm');
        const formData = new FormData(form);
        const spinner = document.getElementById('editSpinner');
        const submitBtn = form.querySelector('button[type="submit"]');
        
        spinner.classList.remove('d-none');
        submitBtn.disabled = true;
        clearValidationErrors(form);

        fetch(`/categories/${currentEditId}/update/`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showValidationError(form, 'name', data.error);
            } else {
                showToast('Category updated successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('editCategoryModal')).hide();
                
                // Update category in the list
                const categoryIndex = categories.findIndex(cat => cat.id === currentEditId);
                if (categoryIndex !== -1 && data.category) {
                    categories[categoryIndex] = data.category;
                    renderCategories();
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error updating category', 'error');
        })
        .finally(() => {
            spinner.classList.add('d-none');
            submitBtn.disabled = false;
        });
    }

    function confirmDeleteCategory(id, name) {
        currentDeleteId = id;
        document.getElementById('deleteCategoryName').textContent = name;
        new bootstrap.Modal(document.getElementById('deleteCategoryModal')).show();
    }

    function deleteCategory() {
        const spinner = document.getElementById('deleteSpinner');
        const deleteBtn = document.getElementById('confirmDeleteBtn');
        
        spinner.classList.remove('d-none');
        deleteBtn.disabled = true;

        fetch(`/categories/${currentDeleteId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                showToast(data.error, 'error');
            } else {
                showToast('Category deleted successfully!', 'success');
                bootstrap.Modal.getInstance(document.getElementById('deleteCategoryModal')).hide();
                
                // Remove category from the list
                categories = categories.filter(cat => cat.id !== currentDeleteId);
                renderCategories();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Error deleting category', 'error');
        })
        .finally(() => {
            spinner.classList.add('d-none');
            deleteBtn.disabled = false;
        });
    }

    // Utility functions
    function getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
               document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
               '{{ csrf_token }}';
    }

    function escapeHtml(text) {
        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, function(m) { return map[m]; });
    }

    function formatDate(date) {
        return new Date(date).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    function showValidationError(form, fieldName, message) {
        const field = form.querySelector(`[name="${fieldName}"]`);
        if (field) {
            field.classList.add('is-invalid');
            const feedback = field.nextElementSibling;
            if (feedback && feedback.classList.contains('invalid-feedback')) {
                feedback.textContent = message;
            }
        }
    }

    function clearValidationErrors(form) {
        form.querySelectorAll('.is-invalid').forEach(field => {
            field.classList.remove('is-invalid');
        });
    }

    function showToast(message, type = 'info') {
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;
        
        let toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
            toastContainer.style.zIndex = '1055';
            document.body.appendChild(toastContainer);
        }
        
        toastContainer.insertAdjacentHTML('beforeend', toastHtml);
        const toastElement = toastContainer.lastElementChild;
        const toast = new bootstrap.Toast(toastElement, {
            autohide: true,
            delay: 4000
        });
        
        toast.show();
        
        // Remove toast element after it's hidden
        toastElement.addEventListener('hidden.bs.toast', function() {
            toastElement.remove();
        });
    }

    // Make functions globally accessible
    window.editCategory = editCategory;
    window.confirmDeleteCategory = confirmDeleteCategory;
});
</script>
{% endblock %}