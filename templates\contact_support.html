{% extends 'base.html' %}

{% block title %}Contact Support{% endblock %}

{% block extra_css %}
<style>
.support-container {
    max-width: 800px;
    margin: 2rem auto;
    padding: 2rem;
}

.support-header {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.support-header i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.contact-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.contact-method {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    text-align: center;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.contact-method:hover {
    border-color: #667eea;
    transform: translateY(-2px);
}

.contact-method i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.support-form {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-floating {
    margin-bottom: 1rem;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.category-option {
    position: relative;
}

.category-option input[type="radio"] {
    display: none;
}

.category-option label {
    display: block;
    padding: 0.75rem;
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.category-option input[type="radio"]:checked + label {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.category-option label:hover {
    border-color: #667eea;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="support-container">
        <!-- Header -->
        <div class="support-header">
            <i class="fas fa-headset"></i>
            <h1>Contact Support</h1>
            <p class="mb-0">We're here to help! Get in touch with our support team.</p>
        </div>

        <!-- Contact Methods -->
        <div class="contact-methods">
            <div class="contact-method">
                <i class="fas fa-envelope"></i>
                <h5>Email Support</h5>
                <p class="text-muted">Get help via email</p>
                <small>Response within 24 hours</small>
            </div>
            <div class="contact-method">
                <i class="fas fa-comments"></i>
                <h5>Live Chat</h5>
                <p class="text-muted">Chat with our team</p>
                <small>Available 9 AM - 6 PM</small>
            </div>
            <div class="contact-method">
                <i class="fas fa-book"></i>
                <h5>Help Center</h5>
                <p class="text-muted">Browse our guides</p>
                <small>Self-service resources</small>
            </div>
        </div>

        <!-- Success/Error Messages -->
        {% if success %}
        <div class="alert alert-success">
            <i class="fas fa-check-circle me-2"></i>
            {{ success }}
        </div>
        {% endif %}

        {% if error %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
        </div>
        {% endif %}

        <!-- Support Form -->
        <div class="support-form">
            <h3 class="mb-4">
                <i class="fas fa-paper-plane me-2"></i>
                Send us a message
            </h3>

            <form method="post">
                {% csrf_token %}
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="text" 
                                   class="form-control" 
                                   id="name" 
                                   name="name" 
                                   placeholder="Your Name"
                                   value="{% if user.is_authenticated %}{{ user.get_full_name|default:user.username }}{% endif %}{{ form_data.name|default:'' }}"
                                   required>
                            <label for="name">Your Name</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-floating">
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   placeholder="Your Email"
                                   value="{% if user.is_authenticated %}{{ user.email }}{% endif %}{{ form_data.email|default:'' }}"
                                   required>
                            <label for="email">Your Email</label>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Category</label>
                    <div class="category-grid">
                        <div class="category-option">
                            <input type="radio" id="technical" name="category" value="technical" {% if form_data.category == 'technical' %}checked{% endif %}>
                            <label for="technical">
                                <i class="fas fa-cog d-block mb-1"></i>
                                Technical
                            </label>
                        </div>
                        <div class="category-option">
                            <input type="radio" id="account" name="category" value="account" {% if form_data.category == 'account' %}checked{% endif %}>
                            <label for="account">
                                <i class="fas fa-user d-block mb-1"></i>
                                Account
                            </label>
                        </div>
                        <div class="category-option">
                            <input type="radio" id="billing" name="category" value="billing" {% if form_data.category == 'billing' %}checked{% endif %}>
                            <label for="billing">
                                <i class="fas fa-credit-card d-block mb-1"></i>
                                Billing
                            </label>
                        </div>
                        <div class="category-option">
                            <input type="radio" id="feature" name="category" value="feature" {% if form_data.category == 'feature' %}checked{% endif %}>
                            <label for="feature">
                                <i class="fas fa-lightbulb d-block mb-1"></i>
                                Feature
                            </label>
                        </div>
                        <div class="category-option">
                            <input type="radio" id="bug" name="category" value="bug" {% if form_data.category == 'bug' %}checked{% endif %}>
                            <label for="bug">
                                <i class="fas fa-bug d-block mb-1"></i>
                                Bug Report
                            </label>
                        </div>
                        <div class="category-option">
                            <input type="radio" id="other" name="category" value="other" {% if form_data.category == 'other' or not form_data.category %}checked{% endif %}>
                            <label for="other">
                                <i class="fas fa-question d-block mb-1"></i>
                                Other
                            </label>
                        </div>
                    </div>
                </div>

                <div class="form-floating mb-3">
                    <input type="text" 
                           class="form-control" 
                           id="subject" 
                           name="subject" 
                           placeholder="Subject"
                           value="{{ form_data.subject|default:'' }}"
                           required>
                    <label for="subject">Subject</label>
                </div>

                <div class="form-floating mb-3">
                    <textarea class="form-control" 
                              id="message" 
                              name="message" 
                              placeholder="Your message"
                              style="height: 150px"
                              required>{{ form_data.message|default:'' }}</textarea>
                    <label for="message">Your Message</label>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-paper-plane me-2"></i>
                        Send Message
                    </button>
                </div>
            </form>
        </div>

        <!-- FAQ Section -->
        <div class="mt-5">
            <h4 class="mb-3">
                <i class="fas fa-question-circle me-2"></i>
                Frequently Asked Questions
            </h4>
            <div class="accordion" id="faqAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq1">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse1">
                            How do I reset my password?
                        </button>
                    </h2>
                    <div id="collapse1" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            You can reset your password by clicking the "Forgot Password" link on the login page and following the instructions sent to your email.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq2">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse2">
                            How do I add products to my store?
                        </button>
                    </h2>
                    <div id="collapse2" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            Go to your private store, click "Add New Product" and fill in the product details including name, price, description, and images.
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="faq3">
                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse3">
                            How do I share my store?
                        </button>
                    </h2>
                    <div id="collapse3" class="accordion-collapse collapse" data-bs-parent="#faqAccordion">
                        <div class="accordion-body">
                            In your private store, look for the "Share Store" button to generate a public link that you can share with customers.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
