# Generated by Django 5.2.3 on 2025-07-27 08:25

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('IManagement', '0004_remove_receipt_currency_symbol'),
    ]

    operations = [
        migrations.AddField(
            model_name='property',
            name='contact_email',
            field=models.EmailField(blank=True, help_text='Contact email for inquiries', max_length=254),
        ),
        migrations.AddField(
            model_name='property',
            name='contact_phone',
            field=models.CharField(blank=True, help_text='Contact phone for inquiries', max_length=20),
        ),
        migrations.AddField(
            model_name='property',
            name='for_sale',
            field=models.BooleanField(default=False, help_text='Is this property available for sale?'),
        ),
        migrations.AddField(
            model_name='property',
            name='is_negotiable',
            field=models.BooleanField(default=True, help_text='Is the price negotiable?'),
        ),
        migrations.AddField(
            model_name='property',
            name='sale_description',
            field=models.TextField(blank=True, help_text='Additional description for sale listing'),
        ),
        migrations.AddField(
            model_name='property',
            name='sale_price',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Asking price for sale', max_digits=12, null=True),
        ),
    ]
