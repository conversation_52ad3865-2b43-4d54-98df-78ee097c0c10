#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'InventoryM.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from IManagement.models import Property

def test_property_management():
    print("🔍 Testing Property Management Functionality...")
    
    # Create test client
    client = Client()
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username='testuser',
        defaults={
            'email': '<EMAIL>',
            'password': 'testpass123'
        }
    )
    
    if created:
        user.set_password('testpass123')
        user.save()
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Using existing user: {user.username}")
    
    # Login user
    login_success = client.login(username='testuser', password='testpass123')
    print(f"✅ Login successful: {login_success}")
    
    # Test property management page GET
    try:
        response = client.get('/property-management/')
        print(f"✅ Property management GET: {response.status_code}")
        if response.status_code != 200:
            print(f"❌ Error: {response.content}")
    except Exception as e:
        print(f"❌ Property management GET failed: {e}")
    
    # Test property creation POST
    try:
        property_data = {
            'name': 'Test Property via API',
            'description': 'A test property created via API',
            'property_type': 'house',
            'condition': 'excellent',
            'location': 'Test City',
            'purchase_price': '250000.00',
            'current_value': '300000.00',
            'for_sale': 'on',
            'sale_price': '280000.00',
            'is_negotiable': 'on',
            'sale_description': 'Great property for sale!',
            'contact_phone': '************',
            'contact_email': '<EMAIL>'
        }
        
        response = client.post('/property-management/', data=property_data)
        print(f"✅ Property creation POST: {response.status_code}")
        
        if response.status_code == 200:
            import json
            result = json.loads(response.content)
            if result.get('success'):
                print(f"✅ Property created successfully: ID {result.get('property_id')}")
            else:
                print(f"❌ Property creation failed: {result.get('error')}")
        else:
            print(f"❌ Property creation error: {response.content}")
            
    except Exception as e:
        print(f"❌ Property creation POST failed: {e}")
        import traceback
        traceback.print_exc()
    
    # Test property detail view
    try:
        properties = Property.objects.filter(owner=user)
        if properties.exists():
            property_obj = properties.first()
            response = client.get(f'/properties/{property_obj.id}/')
            print(f"✅ Property detail GET: {response.status_code}")
        else:
            print("⚠️ No properties found for detail test")
    except Exception as e:
        print(f"❌ Property detail GET failed: {e}")
    
    # Test property delete
    try:
        properties = Property.objects.filter(owner=user)
        if properties.count() > 1:  # Only delete if we have more than one
            property_obj = properties.last()
            response = client.delete(f'/properties/{property_obj.id}/')
            print(f"✅ Property delete: {response.status_code}")
        else:
            print("⚠️ Skipping delete test (not enough properties)")
    except Exception as e:
        print(f"❌ Property delete failed: {e}")
    
    print("\n📊 Final Property Count:")
    final_count = Property.objects.filter(owner=user).count()
    print(f"   Total properties: {final_count}")
    
    return True

if __name__ == "__main__":
    success = test_property_management()
    if success:
        print("\n✅ Property management tests completed!")
        print("🚀 All core functionality is working")
    else:
        print("\n❌ Some tests failed")
