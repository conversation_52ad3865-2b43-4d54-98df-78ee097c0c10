<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Corporate Receipt Template</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.4;
      color: #2c3e50;
      background: #f8f9fa;
      padding: 10px;
      font-size: 13px;
    }

    .receipt-container {
      max-width: 700px;
      margin: 30px auto;
      background: #f9f9f9;
      border-radius: 8px;
      box-shadow: 0 0 12px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      padding: 30px;
      color: #34495e;
    }

    .receipt-header {
      border-bottom: 3px solid #2980b9;
      padding-bottom: 15px;
      margin-bottom: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .receipt-header::after {
      display: none;
    }

    .logo-section {
      flex-shrink: 0;
    }

    .logo-image {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #2980b9;
      background: white;
    }

    .title-section {
      flex-grow: 1;
      text-align: center;
    }

    .receipt-title {
      font-size: 2.2em;
      font-weight: 700;
      color: #2980b9;
      margin: 0;
    }

    .receipt-subtitle {
      font-size: 1.1em;
      margin-top: 8px;
      color: #7f8c8d;
    }

    .spacer {
      width: 60px;
    }

    .company-info {
      text-align: center;
      margin-bottom: 30px;
      padding: 15px;
      background: #ffffff;
      border-radius: 4px;
      border-left: 3px solid #2980b9;
    }

    .company-name {
      font-size: 1.2em;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .company-contact {
      margin-top: 5px;
      font-size: 0.9em;
      color: #555;
    }

    .company-contact div {
      margin-bottom: 2px;
    }

    .company-message {
      color: #7f8c8d;
      font-size: 0.9em;
      font-style: italic;
      margin-top: 8px;
    }

    .client-info {
      margin-bottom: 30px;
    }

    .details-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 15px;
    }

    .detail-item {
      background: #ffffff;
      padding: 15px;
      border-radius: 4px;
      border: 1px solid #e1e8ed;
    }

    .detail-label {
      font-size: 0.9em;
      color: #7f8c8d;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 8px;
      font-weight: 600;
    }

    .detail-value {
      font-size: 1em;
      font-weight: 500;
      color: #2c3e50;
      line-height: 1.5;
    }

    .items-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 35px;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .items-table thead {
      background-color: #d6eaf8;
    }

    .items-table th {
      padding: 12px 15px;
      text-align: left;
      font-weight: 700;
      color: #2c3e50;
      border-bottom: 1px solid #ccc;
    }

    .items-table tbody tr {
      border-bottom: 1px solid #ccc;
      transition: background-color 0.2s ease;
    }

    .items-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .items-table tbody tr:last-child {
      border-bottom: none;
    }

    .items-table td {
      padding: 12px 15px;
      vertical-align: middle;
    }

    .item-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .item-quantity, .item-price {
      font-family: 'Courier New', monospace;
      text-align: right;
    }

    .totals-section {
      background: #ffffff;
      padding: 20px;
      border-radius: 6px;
      border: 1px solid #e1e8ed;
      margin-bottom: 30px;
    }

    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #dee2e6;
    }

    .total-row:last-child {
      border-bottom: none;
      font-size: 1.4em;
      font-weight: 700;
      color: #27ae60;
      padding-top: 15px;
      border-top: 2px solid #2980b9;
      margin-top: 10px;
    }

    .total-label {
      font-weight: 600;
    }

    .total-value {
      font-family: 'Courier New', monospace;
      font-weight: 700;
    }

    .signatures-section {
      display: flex;
      justify-content: space-between;
      font-size: 0.9em;
      color: #95a5a6;
      margin-top: 30px;
    }

    .signature-block, .stamp-block {
      text-align: center;
      width: 200px;
    }

    .signature-label, .stamp-label {
      font-weight: 600;
      color: #7f8c8d;
      margin-bottom: 10px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-size: 0.85em;
    }

    .signature-area, .stamp-area {
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 5px;
    }

    .signature-area img, .stamp-area img {
      max-width: 90%;
      max-height: 55px;
      object-fit: contain;
    }

    .signature-line, .stamp-line {
      border-top: 1px solid #bdc3c7;
      padding-top: 5px;
      font-weight: 600;
      color: #7f8c8d;
    }

    .receipt-footer {
      background: #34495e;
      color: white;
      text-align: center;
      padding: 15px;
      font-size: 0.8em;
      opacity: 0.9;
      margin: 30px -30px -30px -30px;
      border-radius: 0 0 8px 8px;
    }

    @media (max-width: 768px) {
      .receipt-container {
        margin: 10px;
        padding: 20px;
      }
      
      .receipt-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
      }
      
      .spacer {
        display: none;
      }
      
      .details-grid,
      .signatures-section {
        grid-template-columns: 1fr;
        gap: 15px;
        flex-direction: column;
      }
      
      .items-table th,
      .items-table td {
        padding: 8px 6px;
        font-size: 0.85em;
      }
      
      .receipt-title {
        font-size: 1.8em;
      }
    }

    @media print {
      body {
        background: white;
        padding: 0;
      }
      
      .receipt-container {
        box-shadow: none;
        border: 1px solid #ccc;
        margin: 0;
        background: white;
      }
    }
  </style>
  <script>
    // Auto-generate receipt number and date if not provided
    document.addEventListener('DOMContentLoaded', function() {
      // Generate random receipt number
      const receiptNumberElements = document.querySelectorAll('[data-auto="receipt-number"]');
      receiptNumberElements.forEach(element => {
        if (!element.textContent.trim()) {
          const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number
          element.textContent = `#RCP${randomNumber}`;
        }
      });
      
      // Generate current date and time
      const dateElements = document.querySelectorAll('[data-auto="date"]');
      dateElements.forEach(element => {
        if (!element.textContent.trim()) {
          const now = new Date();
          const day = String(now.getDate()).padStart(2, '0');
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const year = now.getFullYear();
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          
          element.textContent = `${day}/${month}/${year} ${hours}:${minutes}`;
        }
      });
    });
  </script>
</head>
<body>
  <div class="receipt-container">
    <header class="receipt-header">
      <div class="logo-section">
        {% if avatar_url %}
          <img src="{{ avatar_url }}" alt="Company Logo" class="logo-image" />
        {% else %}
          <div style="width: 60px; height: 60px; background: #fff; border-radius: 50%; border: 2px solid #2980b9;"></div>
        {% endif %}
      </div>

      <div class="title-section">
        <h1 class="receipt-title">Receipt</h1>
        <p class="receipt-subtitle">{{ company_name }}</p>
        <p class="company-message">Thank you for your purchase!</p>
      </div>

      <div class="spacer"></div>
    </header>

    <main class="receipt-body">
      <section class="company-info">
        <div class="company-name">{{ company_name }}</div>
        <div class="company-contact">
          {% if phone_number %}
          <div>Phone: {{ phone_number }}</div>
          {% endif %}
          {% if secondary_email %}
          <div>Email: {{ secondary_email }}</div>
          {% endif %}
        </div>
        <div class="company-message">Thank you for your business!</div>
      </section>

      <section class="client-info">
        <div class="details-grid">
          <div class="detail-item">
            <div class="detail-label">Customer Details</div>
            <div class="detail-value">
              {% if client_name %}
                <strong>{{ client_name }}</strong><br>
              {% endif %}
              {% if client_email %}
                {{ client_email }}<br>
              {% endif %}
              {% if client_phone %}
                {{ client_phone }}<br>
              {% endif %}
              {% if client_address %}
                {{ client_address }}
              {% endif %}
            </div>
          </div>
          <div class="detail-item">
            <div class="detail-label">Receipt Details</div>
            <div class="detail-value">
              Receipt No: 
              {% if receipt_number %}
                {{ receipt_number }}
              {% else %}
                <span data-auto="receipt-number"></span>
              {% endif %}
              <br>
              Date: 
              {% if transaction_date %}
                {{ transaction_date }}
              {% else %}
                <span data-auto="date"></span>
              {% endif %}
            </div>
          </div>
        </div>
      </section>

      <section class="items-section">
        <table class="items-table">
          <thead>
            <tr>
              <th>Item</th>
              <th style="text-align: center;">Qty</th>
              <th style="text-align: right;">Unit Price</th>
              <th style="text-align: right;">Price</th>
            </tr>
          </thead>
          <tbody>
            {% for item in items %}
            <tr>
              <td class="item-name">{{ item.name }}</td>
              <td class="item-quantity" style="text-align: center;">{{ item.quantity }}</td>
              <td class="item-price">{{ currency_symbol }}{{ item.unit_price }}</td>
              <td class="item-price">{{ currency_symbol }}{{ item.total_price }}</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </section>

      <section class="totals-section">
        <div class="total-row">
          <span class="total-label">Subtotal:</span>
          <span class="total-value">{{ currency_symbol }} {{ subtotal|default:total_price }}</span>
        </div>
        {% if tax_amount %}
        <div class="total-row">
          <span class="total-label">Tax:</span>
          <span class="total-value">{{ currency_symbol }} {{ tax_amount }}</span>
        </div>
        {% endif %}
        {% if discount_amount %}
        <div class="total-row">
          <span class="total-label">Discount:</span>
          <span class="total-value">-{{ currency_symbol }} {{ discount_amount }}</span>
        </div>
        {% endif %}
        <div class="total-row">
          <span class="total-label">Total:</span>
          <span class="total-value">{{ currency_symbol }} {{ total_price }}</span>
        </div>
      </section>

      <section class="signatures-section">
        <div class="signature-block">
          <div class="signature-label">Signature:</div>
          <div class="signature-area">
            {% if signature_url %}
              <img src="{{ signature_url }}" alt="Signature" />
            {% endif %}
          </div>
          <div class="signature-line">____________________</div>
        </div>

        <div class="stamp-block">
          <div class="stamp-label">Stamp:</div>
          <div class="stamp-area">
            {% if stamp_url %}
              <img src="{{ stamp_url }}" alt="Official Stamp" />
            {% endif %}
          </div>
          <div class="stamp-line">____________________</div>
        </div>
      </section>
    </main>

    <footer class="receipt-footer">
      <p>This receipt serves as proof of transaction • Please retain for your records</p>
    </footer>
  </div>
</body>
</html>