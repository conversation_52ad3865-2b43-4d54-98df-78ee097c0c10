{% extends 'base.html' %}

{% block title %}My Goods{% endblock %}

{% block content %}

<style>
    .dropdown-menu {
    z-index: 1050 !important;
}

/* Ensure the dropdown container has proper stacking context */
.dropdown {
    position: relative;
    z-index: 1000;
}

/* Ensure cards don't interfere with dropdown */
.card {
    position: relative;
    z-index: 1;
}

/* Alternative fix: Set specific z-index for the filter section */
.card.mb-4 {
    position: relative;
    z-index: 100;
}

/* Make sure the dropdown button container has proper positioning */
.d-flex.gap-2.align-items-center {
    position: relative;
    z-index: 10;
</style>
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>My Goods</h2>
    </div>

    <!-- Display Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <!-- Display Errors -->
    {% if error %}
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ error }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    {% endif %}

    <!-- Search and Filter Section -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center flex-wrap gap-3">
                <h6 class="mb-0">
                    <i class="fas fa-boxes text-primary"></i> My Goods
                    <span class="badge bg-primary ms-2">{{ goods|length }}</span>
                </h6>
                <div class="d-flex gap-2 align-items-center">
                    <!-- Search Bar -->
                    <div class="search-container">
                        <div class="input-group">
                            <input type="text" class="form-control" id="searchGoods" placeholder="Search goods...">
                            <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                                    <!-- Category Filter -->
                <div class="category-filter">
                    <select class="form-select" id="categoryFilter" name="category">
                        <option value="">All Categories</option>
                        {% for category in categories %}
                            <option value="{{ category.name|lower }}">{{ category.name }}</option>
                        {% empty %}
                            <option value="">No categories available</option>
                        {% endfor %}
                    </select>
                </div>
                

                    <!-- Filter Dropdown -->
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-filter"></i> Filter
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item filter-option" href="#" data-filter="all">All Items</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="used">Used Items</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="new">New Items</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="delivery">Available for Delivery</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="bulk">Bulk Sales</a></li>
                        </ul>
                    </div>
                                   <!-- Clear All Filters Button -->
                <button class="btn btn-outline-danger" type="button" id="clearAllFilters">
                    <i class="fas fa-times-circle"></i> Clear All
                </button>
                </div>
            </div>
        </div>
    </div>

    {% if goods %}
        <div class="row g-4" id="goods-grid">
            {% for item in goods %}
            <div class="col-md-6 col-lg-4 mb-4 goods-item" 
                 data-goods-id="{{ item.id }}"
                 data-name="{{ item.name|lower }}"
                 data-category="{{ item.category.name|default:'uncategorized'|lower }}"
                 data-condition="{% if item.is_used %}used{% else %}new{% endif %}"
                 data-delivery="{% if item.available_for_delivery %}true{% else %}false{% endif %}"
                 data-bulk="{% if item.available_for_bulk_sales %}true{% else %}false{% endif %}">
                <div class="card h-100">
<!-- Replace the image section in your template with this -->
<div class="position-relative">
    {% if item.first_image_url %}
        <img src="{{ item.first_image_url }}" class="card-img-top" alt="{{ item.name }}" style="height: 200px; object-fit: cover;">
    {% else %}
        <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
            <i class="fas fa-image fa-3x text-muted"></i>
        </div>
    {% endif %}
    <!-- Price Badge -->
    <div class="position-absolute top-0 end-0 m-2">
        <span class="badge bg-success fs-6">{{ item.currency_symbol }}{{ item.price|floatformat:2 }}</span>
    </div>
</div>


                    <div class="card-body">
                        <h5 class="card-title">{{ item.name }}</h5>
                        <p class="card-text">{{ item.description|default:"No description provided"|truncatewords:20 }}</p>
                        
                        <!-- Category Info -->
                        {% if item.category %}
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-tag"></i> {{ item.category.name }}
                                </small>
                            </div>
                        {% endif %}
                        
                        <!-- Status badges -->
                        <div class="mb-2">
                            {% if item.is_used %}
                                <span class="badge bg-warning me-1">Used</span>
                            {% else %}
                                <span class="badge bg-success me-1">New</span>
                            {% endif %}
                            
                            {% if item.available_for_delivery %}
                                <span class="badge bg-info me-1">{{ item.delivery_type|default:"Delivery" }}</span>
                            {% endif %}
                            
                            {% if item.available_for_bulk_sales %}
                                <span class="badge bg-primary me-1">Bulk Sales</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="card-footer bg-transparent">
                        <div class="btn-group w-100" role="group">
                            <a href="{% url 'goods_detail' item.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <button class="btn btn-outline-warning btn-sm edit-goods-btn" 
                                    data-goods-id="{{ item.id }}"
                                    data-goods-name="{{ item.name }}"
                                    data-goods-price="{{ item.price }}"
                                    data-goods-description="{{ item.description }}"
                                    data-category-id="{{ item.category.id|default:'' }}"
                                    data-is-used="{{ item.is_used|yesno:'true,false' }}"
                                    data-available-delivery="{{ item.available_for_delivery|yesno:'true,false' }}"
                                    data-delivery-type="{{ item.delivery_type|default:'' }}"
                                    data-available-bulk="{{ item.available_for_bulk_sales|yesno:'true,false' }}"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#editGoodsModal">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="btn btn-outline-danger btn-sm delete-goods-btn" 
                                    data-goods-id="{{ item.id }}"
                                    data-goods-name="{{ item.name }}"
                                    data-bs-toggle="modal" 
                                    data-bs-target="#deleteGoodsModal">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- No Results Message (Hidden by default) -->
        <div id="no-results-message" class="text-center py-5" style="display: none;">
            <i class="fas fa-search fa-3x text-muted mb-3"></i>
            <h6>No goods found</h6>
            <p class="text-muted">Try adjusting your search or filter criteria</p>
        </div>
        
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-shopping-bag fa-3x text-muted mb-3"></i>
            <h4 class="text-muted">No goods yet</h4>
            <p class="text-muted">You haven't added any goods yet</p>
        </div>
    {% endif %}
</div>

<!-- Edit Goods Modal -->
<div class="modal fade" id="editGoodsModal" tabindex="-1" aria-labelledby="editGoodsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editGoodsModalLabel">Edit Goods</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editGoodsForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <input type="hidden" id="editGoodsId" name="goods_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editGoodsName" class="form-label">Name *</label>
                                <input type="text" class="form-control" id="editGoodsName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editGoodsPrice" class="form-label">Price *</label>
                                <input type="number" class="form-control" id="editGoodsPrice" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editGoodsDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editGoodsDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editGoodsImage" class="form-label">Image</label>
                        <input type="file" class="form-control" id="editGoodsImage" name="image" accept="image/*">
                    </div>
                    
                    <div class="mb-3">
                        <label for="editCategorySelect" class="form-label">Category</label>
                        <select class="form-select" id="editCategorySelect" name="category_id">
                            <option value="">Choose Category (Optional)...</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                            {% empty %}
                                <option disabled>No categories available</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="editIsUsed" name="is_used">
                                <label class="form-check-label" for="editIsUsed">
                                    This is a used item
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="editAvailableForBulkSales" name="available_for_bulk_sales">
                                <label class="form-check-label" for="editAvailableForBulkSales">
                                    Available for bulk sales
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="editAvailableForDelivery" name="available_for_delivery" onchange="toggleEditDeliveryType()">
                            <label class="form-check-label" for="editAvailableForDelivery">
                                Available for delivery
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3" id="editDeliveryTypeContainer" style="display: none;">
                        <label for="editDeliveryType" class="form-label">Delivery Type</label>
                        <select class="form-select" id="editDeliveryType" name="delivery_type">
                            <option value="">Select Delivery Type</option>
                            <option value="standard">Standard Delivery</option>
                            <option value="express">Express Delivery</option>
                            <option value="same_day">Same Day Delivery</option>
                            <option value="pickup">Pickup Available</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Update Goods</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Goods Modal -->
<div class="modal fade" id="deleteGoodsModal" tabindex="-1" aria-labelledby="deleteGoodsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteGoodsModalLabel">Delete Goods</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete "<span id="deleteGoodsName"></span>"?</p>
                <p class="text-danger"><strong>This action cannot be undone.</strong></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteGoods">Delete</button>
            </div>
        </div>
    </div>
</div>

<script>
class GoodsListManager {
    constructor() {
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.categoryFilter = '';
        this.init();
    }

    init() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Search functionality
        const searchInput = document.getElementById('searchGoods');
        searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        
        document.getElementById('clearSearch').addEventListener('click', () => {
            searchInput.value = '';
            this.handleSearch('');
        });

        // Category filter functionality
        const categoryFilter = document.getElementById('categoryFilter');
        categoryFilter.addEventListener('change', (e) => this.handleCategoryFilter(e.target.value));

        // Status filter functionality
        document.querySelectorAll('.filter-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFilter(e.target.dataset.filter);
            });
        });

        // Clear all filters
        document.getElementById('clearAllFilters').addEventListener('click', () => {
            this.clearAllFilters();
        });

        // Edit Goods form submit
        document.getElementById('editGoodsForm').addEventListener('submit', (e) => this.handleEditGoods(e));

        // Edit button populate modal fields
        document.querySelectorAll('.edit-goods-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.getElementById('editGoodsId').value = btn.dataset.goodsId;
                document.getElementById('editGoodsName').value = btn.dataset.goodsName;
                document.getElementById('editGoodsPrice').value = btn.dataset.goodsPrice;
                document.getElementById('editGoodsDescription').value = btn.dataset.goodsDescription;
                document.getElementById('editCategorySelect').value = btn.dataset.categoryId;
                document.getElementById('editIsUsed').checked = btn.dataset.isUsed === 'true';
                document.getElementById('editAvailableForDelivery').checked = btn.dataset.availableDelivery === 'true';
                document.getElementById('editDeliveryType').value = btn.dataset.deliveryType;
                document.getElementById('editAvailableForBulkSales').checked = btn.dataset.availableBulk === 'true';
                
                // Toggle delivery type visibility
                toggleEditDeliveryType();
            });
        });

        // Delete button setup
        document.querySelectorAll('.delete-goods-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                document.getElementById('deleteGoodsName').textContent = btn.dataset.goodsName;
                document.getElementById('confirmDeleteGoods').dataset.goodsId = btn.dataset.goodsId;
            });
        });

        // Confirm delete
        document.getElementById('confirmDeleteGoods').addEventListener('click', () => {
            const goodsId = document.getElementById('confirmDeleteGoods').dataset.goodsId;
            fetch(`/goods/${goodsId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            }).then(response => {
                if (response.ok) {
                    location.reload();
                } else {
                    alert('Failed to delete goods');
                }
            }).catch(err => {
                console.error(err);
                alert('Error deleting goods.');
            });
        });

        // Auto-dismiss alerts
        setTimeout(() => {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    }

    handleSearch(query) {
        this.searchQuery = query.toLowerCase();
        this.filterGoods();
    }

    handleCategoryFilter(category) {
        this.categoryFilter = category.toLowerCase();
        this.filterGoods();
    }

    handleFilter(filter) {
        this.currentFilter = filter;
        this.filterGoods();
        
        // Update filter button text
        const filterBtn = document.querySelector('.dropdown-toggle');
        const filterText = filter === 'all' ? 'Filter' : `Filter: ${filter.replace('-', ' ')}`;
        filterBtn.innerHTML = `<i class="fas fa-filter"></i> ${filterText}`;
    }

    clearAllFilters() {
        // Reset all filter values
        this.searchQuery = '';
        this.categoryFilter = '';
        this.currentFilter = 'all';
        
        // Reset UI elements
        document.getElementById('searchGoods').value = '';
        document.getElementById('categoryFilter').value = '';
        const filterBtn = document.querySelector('.dropdown-toggle');
        filterBtn.innerHTML = '<i class="fas fa-filter"></i> Filter';
        
        // Apply filters (which will show all items)
        this.filterGoods();
    }

    filterGoods() {
        const goodsItems = document.querySelectorAll('.goods-item');
        let visibleCount = 0;

        goodsItems.forEach(item => {
            const name = item.dataset.name;
            const category = item.dataset.category;
            const condition = item.dataset.condition;
            const delivery = item.dataset.delivery === 'true';
            const bulk = item.dataset.bulk === 'true';

            // Search filter
            const matchesSearch = !this.searchQuery || 
                name.includes(this.searchQuery) || 
                category.includes(this.searchQuery);

            // Category filter
            const matchesCategory = !this.categoryFilter || 
                category === this.categoryFilter;

            // Status filter
            let matchesFilter = true;
            switch (this.currentFilter) {
                case 'used':
                    matchesFilter = condition === 'used';
                    break;
                case 'new':
                    matchesFilter = condition === 'new';
                    break;
                case 'delivery':
                    matchesFilter = delivery;
                    break;
                case 'bulk':
                    matchesFilter = bulk;
                    break;
                default:
                    matchesFilter = true;
            }

            const shouldShow = matchesSearch && matchesCategory && matchesFilter;
            item.style.display = shouldShow ? 'block' : 'none';
            
            if (shouldShow) visibleCount++;
        });

        // Show/hide no results message
        const noResultsMsg = document.getElementById('no-results-message');
        const goodsGrid = document.getElementById('goods-grid');
        
        if (visibleCount === 0 && goodsItems.length > 0) {
            goodsGrid.style.display = 'none';
            noResultsMsg.style.display = 'block';
        } else {
            goodsGrid.style.display = 'flex';
            noResultsMsg.style.display = 'none';
        }

        // Update goods count badge
        const countBadge = document.querySelector('.badge.bg-primary');
        if (countBadge) {
            countBadge.textContent = visibleCount;
        }
    }

    async handleEditGoods(event) {
        event.preventDefault();
        const goodsId = document.getElementById('editGoodsId').value;
        const formData = new FormData(event.target);

        try {
            const response = await fetch(`/goods/${goodsId}/`, {
                method: 'PUT',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });
            
            if (response.ok) {
                location.reload();
            } else {
                const data = await response.json();
                alert('Error: ' + (data.error || 'Failed to update goods'));
            }
        } catch (err) {
            console.error(err);
            alert('Error updating goods.');
        }
    }
}

function toggleEditDeliveryType() {
    const deliveryCheckbox = document.getElementById('editAvailableForDelivery');
    const deliveryTypeContainer = document.getElementById('editDeliveryTypeContainer');
    
    if (deliveryCheckbox && deliveryCheckbox.checked) {
        deliveryTypeContainer.style.display = 'block';
    } else if (deliveryTypeContainer) {
        deliveryTypeContainer.style.display = 'none';
        const deliveryTypeSelect = document.getElementById('editDeliveryType');
        if (deliveryTypeSelect) deliveryTypeSelect.value = '';
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            cookie = cookie.trim();
            if (cookie.startsWith(name + '=')) {
                cookieValue = decodeURIComponent(cookie.slice(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// Initialize the manager when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new GoodsListManager();
});
</script>

<!-- Add this CSS for better styling -->
<style>
.category-filter .form-select {
    min-width: 150px;
}

.search-container {
    min-width: 250px;
}

@media (max-width: 768px) {
    .d-flex.gap-2.align-items-center.flex-wrap > * {
        margin-bottom: 10px;
    }
    
    .category-filter .form-select,
    .search-container {
        min-width: 100%;
    }
}

/* Fix dropdown z-index issue */
.dropdown-menu {
    z-index: 1050 !important;
}

.dropdown {
    position: relative;
    z-index: 1000;
}
</style>
{% endblock %}