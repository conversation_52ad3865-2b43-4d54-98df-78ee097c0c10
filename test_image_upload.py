#!/usr/bin/env python
import os
import sys
import django
from PIL import Image
import io

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'InventoryM.settings')
django.setup()

from django.contrib.auth.models import User
from IManagement.models import Property

def create_test_image():
    """Create a simple test image"""
    # Create a simple colored image
    img = Image.new('RGB', (300, 200), color='lightblue')
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return img_bytes.getvalue()

def test_property_with_image():
    try:
        # Get the first user
        user = User.objects.first()
        if not user:
            print("No user found. Please create a user first.")
            return
        
        # Create test image data
        image_data = create_test_image()
        
        # Save test image to media directory
        import uuid
        from django.conf import settings
        
        upload_dir = os.path.join(settings.MEDIA_ROOT, 'property_images')
        os.makedirs(upload_dir, exist_ok=True)
        
        filename = f"test_image_{uuid.uuid4()}.jpg"
        file_path = os.path.join(upload_dir, filename)
        
        with open(file_path, 'wb') as f:
            f.write(image_data)
        
        image_url = f"/media/property_images/{filename}"
        
        # Create property with image
        property_obj = Property.objects.create(
            owner=user,
            name="Test House with Image",
            description="A test property with a beautiful image",
            property_type="house",
            condition="excellent",
            location="Test City with Image",
            for_sale=True,
            sale_price=350000.00,
            is_negotiable=True,
            sale_description="Beautiful house with stunning views!",
            contact_phone="************",
            contact_email=user.email,
            images=[image_url]  # Add the image URL
        )
        
        print(f"✅ Created property with image: {property_obj.name}")
        print(f"Property ID: {property_obj.id}")
        print(f"Image URL: {image_url}")
        print(f"Image file: {file_path}")
        print(f"File exists: {os.path.exists(file_path)}")
        
        return property_obj
        
    except Exception as e:
        print(f"❌ Error creating property with image: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    property_obj = test_property_with_image()
    if property_obj:
        print(f"\n🎉 Test property with image created successfully!")
        print(f"Visit: http://127.0.0.1:8001/properties-for-sale/ to see it")
        print(f"Visit: http://127.0.0.1:8001/property-sale/{property_obj.id}/ for details")
    else:
        print("\n💥 Failed to create test property with image")
