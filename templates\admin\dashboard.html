{% extends 'base.html' %}

{% block title %}Admin Dashboard{% endblock %}

{% block extra_css %}
<style>
.admin-header {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #dc2626;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #dc2626;
    display: block;
}

.stat-label {
    color: #6b7280;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.admin-nav {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.admin-nav .nav-link {
    color: #374151;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.admin-nav .nav-link:hover,
.admin-nav .nav-link.active {
    background: #dc2626;
    color: white;
}

.recent-section {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.recent-item {
    padding: 1rem;
    border-bottom: 1px solid #f3f4f6;
    transition: background 0.3s ease;
}

.recent-item:hover {
    background: #f9fafb;
}

.recent-item:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

{% block content %}
<div class="admin-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-tachometer-alt me-3"></i>
                    Admin Dashboard
                </h1>
                <p class="mb-0 opacity-75">Manage your inventory management system</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="text-white">
                    <small>Last updated: {{ "now"|date:"M d, Y H:i" }}</small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Admin Navigation -->
    <div class="admin-nav">
        <ul class="nav nav-pills">
            <li class="nav-item">
                <a class="nav-link active" href="{% url 'admin_dashboard' %}">
                    <i class="fas fa-home me-2"></i>Dashboard
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_users' %}">
                    <i class="fas fa-users me-2"></i>Users
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_goods' %}">
                    <i class="fas fa-box me-2"></i>Products
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_stores' %}">
                    <i class="fas fa-store me-2"></i>Stores
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_properties' %}">
                    <i class="fas fa-home me-2"></i>Properties
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_support_tickets' %}">
                    <i class="fas fa-ticket-alt me-2"></i>Support
                </a>
            </li>
            <li class="nav-item">
                <a class="nav-link" href="{% url 'admin_system_stats' %}">
                    <i class="fas fa-chart-bar me-2"></i>Statistics
                </a>
            </li>
        </ul>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <span class="stat-number">{{ stats.total_users }}</span>
            <div class="stat-label">Total Users</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ stats.total_goods }}</span>
            <div class="stat-label">Total Products</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ stats.total_stores }}</span>
            <div class="stat-label">Total Stores</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ stats.total_properties }}</span>
            <div class="stat-label">Total Properties</div>
        </div>
        <div class="stat-card">
            <span class="stat-number">{{ stats.open_tickets }}</span>
            <div class="stat-label">Open Tickets</div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Users -->
        <div class="col-lg-4">
            <div class="recent-section">
                <h5 class="mb-3">
                    <i class="fas fa-user-plus me-2 text-primary"></i>
                    Recent Users
                </h5>
                {% for user in recent_users %}
                <div class="recent-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ user.username }}</strong>
                            <small class="d-block text-muted">{{ user.email }}</small>
                        </div>
                        <small class="text-muted">{{ user.date_joined|timesince }} ago</small>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">No recent users</p>
                {% endfor %}
            </div>
        </div>

        <!-- Recent Products -->
        <div class="col-lg-4">
            <div class="recent-section">
                <h5 class="mb-3">
                    <i class="fas fa-box me-2 text-success"></i>
                    Recent Products
                </h5>
                {% for good in recent_goods %}
                <div class="recent-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>{{ good.name }}</strong>
                            <small class="d-block text-muted">by {{ good.owner.username }}</small>
                        </div>
                        <small class="text-muted">{{ good.created_at|timesince }} ago</small>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">No recent products</p>
                {% endfor %}
            </div>
        </div>

        <!-- Recent Support Tickets -->
        <div class="col-lg-4">
            <div class="recent-section">
                <h5 class="mb-3">
                    <i class="fas fa-ticket-alt me-2 text-warning"></i>
                    Recent Tickets
                </h5>
                {% for ticket in recent_tickets %}
                <div class="recent-item">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>#{{ ticket.id }} - {{ ticket.subject|truncatechars:30 }}</strong>
                            <small class="d-block text-muted">
                                <span class="badge bg-{{ ticket.status|yesno:'success,warning,danger' }}">
                                    {{ ticket.get_status_display }}
                                </span>
                            </small>
                        </div>
                        <small class="text-muted">{{ ticket.created_at|timesince }} ago</small>
                    </div>
                </div>
                {% empty %}
                <p class="text-muted">No recent tickets</p>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="recent-section">
        <h5 class="mb-3">
            <i class="fas fa-bolt me-2 text-info"></i>
            Quick Actions
        </h5>
        <div class="row g-3">
            <div class="col-md-3">
                <a href="{% url 'admin_users' %}" class="btn btn-outline-primary w-100">
                    <i class="fas fa-users me-2"></i>
                    Manage Users
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'admin_goods' %}" class="btn btn-outline-success w-100">
                    <i class="fas fa-box me-2"></i>
                    Manage Products
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'admin_support_tickets' %}" class="btn btn-outline-warning w-100">
                    <i class="fas fa-ticket-alt me-2"></i>
                    Support Tickets
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'admin_stores' %}" class="btn btn-outline-info w-100">
                    <i class="fas fa-store me-2"></i>
                    Manage Stores
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'admin_properties' %}" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-home me-2"></i>
                    Manage Properties
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'admin_system_stats' %}" class="btn btn-outline-dark w-100">
                    <i class="fas fa-chart-bar me-2"></i>
                    System Stats
                </a>
            </div>
            <div class="col-md-3">
                <a href="{% url 'admin_bulk_actions' %}" class="btn btn-outline-warning w-100">
                    <i class="fas fa-tools me-2"></i>
                    Bulk Actions
                </a>
            </div>
            <div class="col-md-3">
                <a href="/admin/" class="btn btn-outline-danger w-100">
                    <i class="fas fa-cog me-2"></i>
                    Django Admin
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
