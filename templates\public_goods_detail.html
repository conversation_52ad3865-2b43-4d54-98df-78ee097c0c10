<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Browse Goods - InventoryPro</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --dark-color: #1f2937;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            background: #f8fafc;
        }

        .navbar {
            background: rgba(255,255,255,0.98) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .nav-link {
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .hero-banner {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            padding: 3rem 0 2rem;
            position: relative;
            overflow: hidden;
        }

        .hero-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 500"><polygon fill="rgba(255,255,255,0.1)" points="0,0 1000,300 1000,500 0,200"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
            text-align: center;
            color: white;
        }

        .hero-title {
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .main-content {
            margin-top: -2rem;
            position: relative;
            z-index: 3;
        }

        .filter-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 1.5rem;
        }

        .filter-card .card-body {
            padding: 1.5rem;
        }

        .product-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            border: none;
            overflow: hidden;
            height: 100%;
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .product-image {
            height: 220px;
            object-fit: cover;
            border-radius: 0;
        }

        .product-placeholder {
            height: 220px;
            background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .price-tag {
            font-size: 1.4rem;
            font-weight: 700;
            color: var(--accent-color);
        }

        .store-badge {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .btn-view-details {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border: none;
            border-radius: 12px;
            padding: 12px 20px;
            font-weight: 600;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-view-details:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
            color: white;
        }

        .btn-contact {
            border-radius: 12px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-contact:hover {
            transform: translateY(-1px);
        }

        .filter-btn {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border: none;
            border-radius: 12px;
            padding: 12px 24px;
            font-weight: 600;
            color: white;
        }

        .no-results {
            text-align: center;
            padding: 4rem 2rem;
            background: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .no-results i {
            font-size: 4rem;
            color: #d1d5db;
            margin-bottom: 1.5rem;
        }

        /* Modal Enhancements */
        .modal-content {
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            border: none;
        }

        .modal-header {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            color: white;
            border-radius: 20px 20px 0 0;
            border-bottom: none;
        }

        .modal-header .btn-close {
            filter: brightness(0) invert(1);
        }

        .form-control:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e5e7eb;
            padding: 12px 16px;
        }

        .form-label {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 8px;
        }

        .card-footer {
            background: transparent;
            border-top: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2rem;
            }
            
            .main-content {
                margin-top: -20px;
            }
            
            .filter-card .card-body {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="/">
                <i class="fas fa-boxes"></i> InventoryPro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-bold text-primary" href="/public/goods/">Browse Goods</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/login/">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-primary px-3 ms-2" href="/register/">Sign Up</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Banner -->
    <section class="hero-banner">
        <div class="container">
            <div class="hero-content">
                <h1 class="hero-title">Browse All Products</h1>
                <p class="hero-subtitle">Discover amazing products from verified sellers</p>
            </div>
        </div>
    </section>
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'public_goods_list' %}">Browse Goods</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ goods.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-md-6">
<!-- Image Gallery Section -->
    {% if goods.images %}
        <!-- Main Image Display -->
        <div id="imageCarousel" class="carousel slide" data-bs-ride="carousel">
            <div class="carousel-inner">
                {% for image in goods.images %}
                    <div class="carousel-item {% if forloop.first %}active{% endif %}">
                        <!-- FIXED: Added proper media URL handling -->
                        <img src="/media/{{ image }}" 
                             class="d-block w-100 rounded-start" 
                             style="height: 300px; object-fit: cover;" 
                             alt="{{ goods.name }}"
                             onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                    </div>
                {% endfor %}
        </div>
        
        <!-- Carousel Controls (only show if more than 1 image) -->
           {% if goods.images|length > 1 %}
                <button class="carousel-control-prev" type="button" data-bs-target="#imageCarousel" data-bs-slide="prev">
                    <span class="carousel-control-prev-icon"></span>
                    <span class="visually-hidden">Previous</span>
                </button>
                <button class="carousel-control-next" type="button" data-bs-target="#imageCarousel" data-bs-slide="next">
                    <span class="carousel-control-next-icon"></span>
                    <span class="visually-hidden">Next</span>
                </button>
            {% endif %}
    </div>

    <!-- Thumbnail Navigation (only show if more than 1 image) -->
        {% if goods.images|length > 1 %}
            <div class="row mt-2 g-1">
                {% for image in goods.images %}
                    <div class="col-3">
                        <!-- FIXED: Added proper media URL handling -->
                        <img src="/media/{{ image }}" 
                             class="img-thumbnail thumbnail-nav {% if forloop.first %}active{% endif %}" 
                             style="height: 60px; object-fit: cover; cursor: pointer;" 
                             alt="Thumbnail {{ forloop.counter }}"
                             onclick="goToSlide({{ forloop.counter0 }})"
                             onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                    </div>
                {% endfor %}
            </div>
        {% endif %}
    {% else %}
        <!-- No Image Placeholder -->
        <div class="d-flex align-items-center justify-content-center bg-light rounded-start" style="height: 300px;">
            <div class="text-center">
                <i class="fas fa-image fa-4x text-muted mb-2"></i>
                <p class="text-muted">No images available</p>
            </div>
        </div>
    {% endif %}

    <!-- Fix for the additional images section -->
{% if goods.images|length > 4 %}
    <div class="card mt-3">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-images"></i> All Images ({{ goods.images|length }})
            </h5>
        </div>
        <div class="card-body">
            <div class="row g-2">
                {% for image in goods.images %}
                    <div class="col-md-3 col-sm-4 col-6">
                        <div class="position-relative">
                            <!-- FIXED: Added proper media URL handling -->
                            <img src="/media/{{ image }}" 
                                 class="img-thumbnail w-100" 
                                 style="height: 120px; object-fit: cover; cursor: pointer;" 
                                 alt="Product image {{ forloop.counter }}"
                                 data-bs-toggle="modal" 
                                 data-bs-target="#imageModal"
                                 onclick="showFullImage('/media/{{ image }}', '{{ forloop.counter }}')"
                                 onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                            <div class="position-absolute top-0 end-0 m-1">
                                <span class="badge bg-dark">{{ forloop.counter }}</span>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
{% endif %}

        </div>
        
        <div class="col-md-6">
            <h1 class="mb-3">{{ goods.name }}</h1>
            
            <div class="mb-4">
                <h2 class="text-success">{{ goods.currency_symbol }}{{ goods.price }}</h2>
            </div>
            
            {% if goods.description %}
            <div class="mb-4">
                <h5>Description</h5>
                <p class="text-muted">{{ goods.description }}</p>
            </div>

            <!-- ✅ Product Status Badges -->
<div class="mb-3">
    {% if goods.is_used %}
        <span class="badge bg-warning text-dark me-1">Used</span>
    {% else %}
        <span class="badge bg-success me-1">New</span>
    {% endif %}

    {% if goods.available_for_delivery %}
        <span class="badge bg-info me-1">{{ goods.delivery_type|default:"Delivery" }}</span>
    {% endif %}

    {% if goods.available_for_bulk_sales %}
        <span class="badge bg-primary me-1">Bulk Sales</span>
    {% endif %}
</div>

            {% endif %}
            
            <div class="mb-4">
                <h5>Details</h5>
                <ul class="list-unstyled">
                    <li><strong>Listed:</strong> {{ goods.created_at|date:"M d, Y H:i" }}</li>
                    {% if goods.category %}
                    <li><strong>Category:</strong> {{ goods.category }}</li>
                    {% endif %}
  {% if goods.quantity is not None %}
        <li>
            <strong>Available Quantity:</strong>
            {% if goods.quantity == 0 %}
                <span class="badge bg-danger">Out of Stock</span>
            {% else %}
                <span class="badge bg-success">{{ goods.quantity }} In Stock</span>
            {% endif %}
        </li>
        {% endif %}
            </div>
            
            {% if contact_info %}
            <div class="mb-4">
                <h5>Seller Information</h5>
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-store text-success"></i> {{ contact_info.store_name }}
                        </h6>
                        {% if contact_info.store_description %}
                        <p class="card-text">{{ contact_info.store_description }}</p>
                        {% endif %}
                        <div class="row">
                            <div class="col-12">
                                <p class="mb-2">
                                    <i class="fas fa-envelope text-primary"></i> 
                                    <a href="mailto:{{ contact_info.email }}?subject=Inquiry about {{ goods.name }}" class="text-decoration-none">
                                        {{ contact_info.email }}
                                    </a>
                                </p>
                                {% if contact_info.phone_number %}
                                <p class="mb-2">
                                    <i class="fas fa-phone text-success"></i> 
                                    <a href="tel:{{ contact_info.phone_number }}" class="text-decoration-none">
                                        {{ contact_info.phone_number }}
                                    </a>
                                </p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-grid gap-2">
                <button class="btn btn-success btn-lg" data-bs-toggle="modal" data-bs-target="#contactModal">
                    <i class="fas fa-phone"></i> Contact Seller
                </button>
                <button class="btn btn-primary btn-lg" onclick="sendEmail()">
                    <i class="fas fa-envelope"></i> Send Email Inquiry
                </button>
                {% if contact_info.phone_number %}
                <a href="tel:{{ contact_info.phone_number }}" class="btn btn-outline-success">
                    <i class="fas fa-phone-alt"></i> Call Now
                </a>
                {% endif %}
{% if contact_info.phone_number %}
<button class="btn btn-success" 
        onclick="openWhatsApp('{{ contact_info.phone_number }}', 'Hi, I\'m interested in your {{ goods.name }} listed for {{ goods.currency_symbol }}{{ goods.price }}. Could you please provide more details?')">
   <i class="fab fa-whatsapp"></i> WhatsApp
</button>
{% endif %}
            </div>
            {% else %}
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                Contact information not available for this item.
            </div>
            {% endif %}
        </div>
    </div>
    
    <div class="row mt-5 mb-2">
        <div class="col-12">
            <a href="{% url 'public_goods_list' %}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Back to Browse
            </a>
        </div>
    </div>
    
    {% if goods.inventory and goods.inventory.store %}
    <div class="row mt-4">
        <div class="col-12">
            <h5>More from this Store</h5>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                Browse more items from <strong>{{ contact_info.store_name }}</strong> by contacting them directly.
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="contactModalLabel">Contact Seller</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        {% if goods.image %}
                            <img src="{{ goods.image.url }}" class="img-fluid rounded mb-3" alt="{{ goods.name }}">
                        {% endif %}
                        <h6>{{ goods.name }}</h6>
                        <p class="text-success h5">{{ goods.currency_symbol }}{{ goods.price }}</p>
                    </div>
                    <div class="col-md-8">
                        {% if contact_info %}
                        <h6 class="mb-3">Seller Details</h6>
                        <div class="mb-3">
                            <strong>Store:</strong> {{ contact_info.store_name }}
                        </div>
                        {% if contact_info.store_description %}
                        <div class="mb-3">
                            <strong>About:</strong> {{ contact_info.store_description }}
                        </div>
                        {% endif %}
                        
                        <h6 class="mb-3">Contact Options</h6>
                        <div class="d-grid gap-2">
                            <a href="mailto:{{ contact_info.email }}?subject=Inquiry about {{ goods.name }}&body=Hi, I'm interested in your {{ goods.name }} listed for ${{ goods.price }}. Could you please provide more details?" 
                               class="btn btn-primary">
                                <i class="fas fa-envelope"></i> Send Email
                            </a>
                            {% if contact_info.phone_number %}
                            <a href="tel:{{ contact_info.phone_number }}" class="btn btn-success">
                                <i class="fas fa-phone"></i> Call {{ contact_info.phone_number }}
                            </a>
                            {% endif %}
 <button class="btn btn-success" 
        onclick="openWhatsApp('{{ contact_info.phone_number }}', 'Hi, I\'m interested in your {{ goods.name }} listed for {{ item.currency_symbol }}{{ goods.price }}. Could you please provide more details about:\n- Current condition\n- Availability\n- Pickup/delivery options\n\nThank you!')">
   <i class="fab fa-whatsapp"></i> Message on WhatsApp
</button>

                        </div>
                        
                        <div class="mt-4">
                            <h6>Quick Message Template</h6>
                            <div class="form-floating">
                                <textarea class="form-control" id="messageTemplate" style="height: 100px" readonly>Hi,

I'm interested in your {{ goods.name }} listed for {{ goods.currency_symbol }}{{ goods.price }}.

Could you please provide more details about:
- Current condition
- Availability
- Pickup/delivery options

Thank you!</textarea>
                                <label for="messageTemplate">Copy this message</label>
                            </div>
                            <button class="btn btn-outline-secondary btn-sm mt-2" onclick="copyMessage()">
                                <i class="fas fa-copy"></i> Copy Message
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- Footer -->
<footer class="bg-dark text-light py-5">
    <div class="container">
        <div class="row">
            <!-- Company Info Section -->
            <div class="col-lg-4 mb-4">
                <h5 class="fw-bold mb-3 text-white">Company House</h5>
                <p class="text-light mb-3">
                    We are a registered IT company in the UK with company number <span class="text-warning">14676167</span>
                </p>
            </div>

            <!-- Contact Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Contact</h6>
                <div class="contact-info">
                    <p class="text-light mb-2">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <strong>Address:</strong> 13L Queensway, Ponders End, Enfield, London EN3 4SA
                    </p>
                    <p class="text-light mb-2">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <strong>Email:</strong> <EMAIL>
                    </p>
                    <p class="text-light mb-0">
                        <i class="fas fa-phone me-2 text-primary"></i>
                        <strong>Tel:</strong> 07500503952
                    </p>
                </div>
            </div>

            <!-- Subscribe Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Subscribe Now</h6>
                <p class="text-light mb-3">
                    Don't miss our future updates! Get Subscribed Today!
                </p>
                <div class="mb-3">
            <div class="col-md-6 text-md-start">
                <a href="/register/" class="btn btn-primary">
                    <i class="fas fa-rocket me-2"></i>Register
                </a>
            </div>
                </div>
                <!-- Social Icons -->
                <div class="social-icons">
                    <a href="https://www.facebook.com/amatip.info.tech" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://x.com/amatipIT" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.tiktok.com/@amatip_it?is_from_webapp=1&sender_device=pc" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-tiktok"></i>
                    </a>
                    <a href="https://youtube.com/@amatip_it?si=eQ54UaVIM-DgLOAr" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="https://www.instagram.com/amatip_it/profilecard/?igsh=MWRzbGV5b3h1MTQ2Yw==" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="d-flex flex-wrap gap-4">
                    <a href="#features" class="text-muted text-decoration-none">Features</a>
                    <a href="/public/goods/" class="text-muted text-decoration-none">Browse Goods</a>
                    <a href="/login/" class="text-muted text-decoration-none">Login</a>
                    <a href="/register/" class="text-muted text-decoration-none">Sign Up</a>
                </div>
            </div>

        </div>

        <hr class="my-4 border-secondary">
        
        <!-- Copyright -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-muted">&copy; 2023 Amatip. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <small class="text-muted">Built with Django & Bootstrap</small>
            </div>
        </div>
    </div>
</footer>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

<script>
function sendEmail() {
    {% if contact_info %}
    const subject = encodeURIComponent('Inquiry about {{ goods.name }}');
    const body = encodeURIComponent(`Hi,

I'm interested in your {{ goods.name }} listed for {{ goods.currency_symbol }}{{ goods.price }}.

Could you please provide more details about:
- Current condition
- Availability
- Pickup/delivery options

Thank you!`);
    
    window.location.href = `mailto:{{ contact_info.email }}?subject=${subject}&body=${body}`;
    {% else %}
    alert('Contact information not available.');
    {% endif %}
}

function copyMessage() {
    const messageText = document.getElementById('messageTemplate');
    messageText.select();
    messageText.setSelectionRange(0, 99999); // For mobile devices
    
    try {
        document.execCommand('copy');
        
        // Show success feedback
        const copyBtn = event.target;
        const originalText = copyBtn.innerHTML;
        copyBtn.innerHTML = '<i class="fas fa-check"></i> Copied!';
        copyBtn.classList.add('btn-success');
        copyBtn.classList.remove('btn-outline-secondary');
        
        setTimeout(() => {
            copyBtn.innerHTML = originalText;
            copyBtn.classList.remove('btn-success');
            copyBtn.classList.add('btn-outline-secondary');
        }, 2000);
    } catch (err) {
        console.error('Failed to copy message:', err);
        alert('Failed to copy message. Please select and copy manually.');
    }
}

// Add smooth scroll for back button
document.addEventListener('DOMContentLoaded', function() {
    // Add any additional initialization here
    console.log('Public goods detail page loaded');
});
    </script>

    <script>
function formatWhatsAppNumber(phoneNumber) {
    // Remove all non-digit characters
    let cleanNumber = phoneNumber.replace(/\D/g, '');
    
    // If number starts with 0, replace with country code (adjust 44 for your country)
    if (cleanNumber.startsWith('0')) {
        cleanNumber = '44' + cleanNumber.substring(1); // UK country code
    }
    
    // If number doesn't start with country code, add it
    if (!cleanNumber.startsWith('44') && cleanNumber.length < 12) {
        cleanNumber = '44' + cleanNumber;
    }
    
    return cleanNumber;
}

function openWhatsApp(phoneNumber, message) {
    const cleanNumber = formatWhatsAppNumber(phoneNumber);
    const encodedMessage = encodeURIComponent(message);
    const whatsappUrl = `https://wa.me/${cleanNumber}?text=${encodedMessage}`;
    window.open(whatsappUrl, '_blank');
}
</script>

</body>
</html>