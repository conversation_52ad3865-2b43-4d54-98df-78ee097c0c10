{% extends 'base.html' %}

{% block title %}Seller Categories{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-3xl font-bold text-gray-800">Seller Categories</h2>
            <div class="flex space-x-3">
                <a href="{% url 'create_category' %}" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200">
                    Add Category
                </a>
                <a href="{% url 'list_categories' %}" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition duration-200">
                    Manage Categories
                </a>
            </div>
        </div>
        
        {% if categories %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% for category in categories %}
                <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition duration-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold text-gray-800 mb-2">{{ category.name }}</h3>
                            <p class="text-sm text-gray-500">Category ID: {{ category.id }}</p>
                        </div>
                        <div class="flex flex-col space-y-2">
                            <button class="text-xs bg-blue-100 text-blue-600 px-3 py-1 rounded-full hover:bg-blue-200 transition duration-200">
                                View Products
                            </button>
                            <button class="text-xs bg-gray-100 text-gray-600 px-3 py-1 rounded-full hover:bg-gray-200 transition duration-200">
                                Edit
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% else %}
            <div class="bg-white rounded-lg shadow-md p-8 text-center">
                <div class="text-gray-500 mb-4">
                    <svg class="mx-auto h-16 w-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M8 11v6a2 2 0 002 2h4a2 2 0 002-2v-6M8 11h8" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No categories available</h3>
                <p class="text-gray-500 mb-4">Start by creating categories to organize your products.</p>
                <a href="{% url 'create_category' %}" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 transition duration-200">
                    Create First Category
                </a>
            </div>
        {% endif %}
        
        <div class="mt-8 bg-blue-50 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-800 mb-2">Category Management Tips</h3>
            <ul class="text-sm text-blue-700 space-y-1">
                <li>• Categories help organize your products for better customer experience</li>
                <li>• Use clear, descriptive names for your categories</li>
                <li>• Each category name must be unique within your store</li>
                <li>• Categories can be assigned to products when creating or editing them</li>
            </ul>
        </div>
    </div>
</div>
{% endblock %}