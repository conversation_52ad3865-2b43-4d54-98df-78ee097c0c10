#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'InventoryM.settings')
django.setup()

from django.contrib.auth.models import User
from IManagement.models import TwoFactorAuth
from django.utils import timezone
from datetime import timedelta

def test_login_flow():
    print("🔍 Testing Login Flow Components...")
    
    # Check if user exists
    user = User.objects.first()
    if not user:
        print("❌ No user found. Creating test user...")
        user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        print(f"✅ Created test user: {user.username}")
    else:
        print(f"✅ Found existing user: {user.username}")
    
    # Test 2FA code generation
    try:
        from IManagement.views import generate_2fa_code
        code = generate_2fa_code()
        print(f"✅ 2FA code generation works: {code}")
    except Exception as e:
        print(f"❌ 2FA code generation failed: {e}")
        return False
    
    # Test 2FA model
    try:
        # Clear old codes
        TwoFactorAuth.objects.filter(user=user, is_used=False).delete()
        
        # Create test verification
        verification = TwoFactorAuth.objects.create(
            user=user,
            code=code,
            expires_at=timezone.now() + timedelta(minutes=10),
            ip_address='127.0.0.1',
            user_agent='Test Agent'
        )
        print(f"✅ 2FA model works: Created verification {verification.id}")
        
        # Test validation
        if verification.is_valid():
            print("✅ 2FA validation works")
        else:
            print("❌ 2FA validation failed")
            
    except Exception as e:
        print(f"❌ 2FA model test failed: {e}")
        return False
    
    # Test email settings
    try:
        from django.conf import settings
        print(f"✅ Email backend: {settings.EMAIL_BACKEND}")
        print(f"✅ Default from email: {getattr(settings, 'DEFAULT_FROM_EMAIL', 'Not set')}")
    except Exception as e:
        print(f"❌ Email settings check failed: {e}")
    
    print("\n🎯 Login Flow Test Summary:")
    print("1. ✅ User authentication ready")
    print("2. ✅ 2FA code generation working")
    print("3. ✅ 2FA model and validation working")
    print("4. ✅ Email configuration present")
    
    print(f"\n🚀 Test login with:")
    print(f"   Username: {user.username}")
    print(f"   Password: testpass123" if user.username == 'testuser' else "   Password: (your existing password)")
    print(f"   Email: {user.email}")
    
    return True

if __name__ == "__main__":
    success = test_login_flow()
    if success:
        print("\n✅ Login flow components are working correctly!")
        print("Visit: http://127.0.0.1:8001/login/ to test the full flow")
    else:
        print("\n❌ Login flow has issues that need to be fixed")
