{% extends 'base.html' %}

{% block title %}Authorized Stores{% endblock %}

{% block content %}
<div class="container mt-4">
    <h2 class="mb-4">My Authorized Stores</h2>

    {% if stores %}
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
            {% for store in stores %}
            <div class="col">
                <div class="card h-100 shadow-sm border-0">
                    <div class="card-body">
                        <h5 class="card-title text-primary">
                            <i class="fas fa-store"></i> {{ store.name }}
                        </h5>
                        {% if store.description %}
                        <p class="card-text text-muted">{{ store.description|truncatewords:20 }}</p>
                        {% else %}
                        <p class="text-muted fst-italic">No description available.</p>
                        {% endif %}
                    </div>
                    <div class="card-footer bg-light text-end">
                        <a href="{% url 'store_detail' store.id %}" class="btn btn-outline-primary btn-sm">
                            View Store
                        </a>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle"></i> You do not have access to any authorized stores yet.
        </div>
    {% endif %}
</div>
{% endblock %}
