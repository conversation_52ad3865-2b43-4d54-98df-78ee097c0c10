#!/usr/bin/env python
"""
Quick test to verify email configuration
"""
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'InventoryM.settings')
django.setup()

from django.core.mail import send_mail
from django.conf import settings

def test_email():
    print("🧪 Quick Email Test")
    print("=" * 30)
    
    print(f"📧 Email Host User: '{settings.EMAIL_HOST_USER}'")
    print(f"📧 Email Host Password: {'*' * len(settings.EMAIL_HOST_PASSWORD) if settings.EMAIL_HOST_PASSWORD else 'NOT SET'}")
    print(f"📧 Default From Email: '{settings.DEFAULT_FROM_EMAIL}'")
    
    if not settings.EMAIL_HOST_USER:
        print("❌ EMAIL_HOST_USER is empty!")
        return False
        
    if not settings.EMAIL_HOST_PASSWORD:
        print("❌ EMAIL_HOST_PASSWORD is empty!")
        return False
    
    try:
        print("\n📤 Sending test email...")
        send_mail(
            subject='✅ Email Test - Inventory Management System',
            message='Great! Your email configuration is working perfectly. The verification system is ready!',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[settings.EMAIL_HOST_USER],
            fail_silently=False,
        )
        
        print("✅ Email sent successfully!")
        print(f"📧 Check your inbox: {settings.EMAIL_HOST_USER}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_email()
    if success:
        print("\n🎉 Email configuration is working!")
        print("🚀 Your verification system is ready to use!")
    else:
        print("\n❌ Email configuration needs fixing")
    
    sys.exit(0 if success else 1)
