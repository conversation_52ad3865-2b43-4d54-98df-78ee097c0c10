#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'InventoryM.settings')
django.setup()

from django.contrib.auth.models import User
from IManagement.models import Property
from decimal import Decimal

def create_simple_property():
    try:
        # Get the first user
        user = User.objects.first()
        if not user:
            print("No user found. Please create a user first.")
            return
        
        # Create property with fake image filename (to test display)
        property_obj = Property.objects.create(
            owner=user,
            name="Test Property with Fake Image",
            description="A test property to check image display",
            property_type="house",
            condition="excellent",
            location="Test Location",
            for_sale=True,
            sale_price=Decimal("250000.00"),
            is_negotiable=True,
            sale_description="Great property for testing!",
            contact_phone="************",
            contact_email=user.email,
            images=["property_images/fake_image.jpg"]  # Fake image to test template
        )
        
        print(f"✅ Created test property: {property_obj.name}")
        print(f"Property ID: {property_obj.id}")
        print(f"For Sale: {property_obj.for_sale}")
        print(f"Images: {property_obj.images}")
        
        return property_obj
        
    except Exception as e:
        print(f"❌ Error creating property: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    property_obj = create_simple_property()
    if property_obj:
        print(f"\n🎉 Test property created!")
        print(f"Visit: http://127.0.0.1:8001/properties-for-sale/ to see it")
        print(f"Visit: http://127.0.0.1:8001/seller/{property_obj.owner.id}/store/ to see it in store")
    else:
        print("\n💥 Failed to create test property")
