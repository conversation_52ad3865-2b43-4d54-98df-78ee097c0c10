{% extends 'base.html' %}

{% block title %}{{ property.name }} - Property for Sale{% endblock %}

{% block extra_css %}
<style>
:root {
    --primary-color: #2563eb;
    --secondary-color: #1e40af;
    --accent-color: #f59e0b;
    --success-color: #10b981;
}

.property-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 3rem 0;
    margin-bottom: 2rem;
}

.property-gallery {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.main-image {
    height: 400px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.price-display {
    position: absolute;
    top: 2rem;
    right: 2rem;
    background: var(--accent-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    font-size: 1.5rem;
    font-weight: 700;
}

.negotiable-badge {
    position: absolute;
    top: 2rem;
    left: 2rem;
    background: var(--success-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: 600;
}

.property-info {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
}

.contact-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    position: sticky;
    top: 2rem;
}

.contact-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 10px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
    margin-bottom: 1rem;
}

.contact-btn:hover {
    background: var(--secondary-color);
    color: white;
    transform: translateY(-2px);
}

.property-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin: 2rem 0;
}

.meta-item {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 10px;
    text-align: center;
}

.meta-label {
    font-size: 0.9rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
}

.meta-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
}
</style>
{% endblock %}

{% block content %}
<!-- Property Header -->
<div class="property-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-5 fw-bold mb-3">{{ property.name }}</h1>
                <p class="lead mb-0">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    {{ property.location|default:"Location not specified" }}
                </p>
            </div>
            <div class="col-lg-4 text-lg-end">
                <a href="{% url 'properties_for_sale' %}" class="btn btn-light">
                    <i class="fas fa-arrow-left me-2"></i>Back to Properties
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Property Gallery and Info -->
        <div class="col-lg-8">
            <!-- Main Image -->
            <div class="property-gallery">
                <div class="main-image">
                    {% if property.images and property.images.0 %}
                        <img src="/media/{{ property.images.0 }}" alt="{{ property.name }}" onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                    {% else %}
                        <div class="text-center">
                            <i class="fas fa-home fa-5x text-muted mb-3"></i>
                            <p class="text-muted">No image available</p>
                        </div>
                    {% endif %}
                    
                    <div class="price-display">
                        ${{ property.sale_price|floatformat:0 }}
                    </div>
                    
                    {% if property.is_negotiable %}
                        <div class="negotiable-badge">Negotiable</div>
                    {% endif %}
                </div>
            </div>

            <!-- Property Information -->
            <div class="property-info">
                <h3 class="mb-3">Property Details</h3>
                
                <!-- Property Meta -->
                <div class="property-meta">
                    <div class="meta-item">
                        <div class="meta-label">Property Type</div>
                        <div class="meta-value">{{ property.get_property_type_display|default:"Not specified" }}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Price</div>
                        <div class="meta-value">${{ property.sale_price|floatformat:0 }}</div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Negotiable</div>
                        <div class="meta-value">
                            {% if property.is_negotiable %}
                                <span class="text-success">Yes</span>
                            {% else %}
                                <span class="text-danger">No</span>
                            {% endif %}
                        </div>
                    </div>
                    <div class="meta-item">
                        <div class="meta-label">Listed By</div>
                        <div class="meta-value">{{ property.owner.username }}</div>
                    </div>
                </div>

                <!-- Description -->
                <h4 class="mt-4 mb-3">Description</h4>
                <div class="mb-3">
                    {% if property.sale_description %}
                        <p>{{ property.sale_description|linebreaks }}</p>
                    {% endif %}
                    {% if property.description %}
                        <p>{{ property.description|linebreaks }}</p>
                    {% endif %}
                    {% if not property.sale_description and not property.description %}
                        <p class="text-muted">No description available.</p>
                    {% endif %}
                </div>

                <!-- Additional Images -->
                {% if property.images|length > 1 %}
                <h4 class="mt-4 mb-3">More Images</h4>
                <div class="row">
                    {% for image in property.images|slice:"1:" %}
                    <div class="col-md-4 mb-3">
                        <img src="/media/{{ image }}" alt="{{ property.name }}" class="img-fluid rounded" onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                    </div>
                    {% endfor %}
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Contact Information -->
        <div class="col-lg-4">
            <div class="contact-card">
                <h4 class="mb-3">
                    <i class="fas fa-phone me-2"></i>
                    Contact Seller
                </h4>
                
                <div class="mb-3">
                    <strong>{{ property.owner.get_full_name|default:property.owner.username }}</strong>
                </div>

                {% if property.contact_phone %}
                <a href="tel:{{ property.contact_phone }}" class="contact-btn">
                    <i class="fas fa-phone me-2"></i>
                    Call {{ property.contact_phone }}
                </a>
                {% endif %}

                {% if property.contact_email %}
                <a href="mailto:{{ property.contact_email }}?subject=Inquiry about {{ property.name }}" class="contact-btn">
                    <i class="fas fa-envelope me-2"></i>
                    Email Seller
                </a>
                {% endif %}

                {% if not property.contact_phone and not property.contact_email %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Contact information not provided by seller.
                </div>
                {% endif %}

                <!-- Property Summary -->
                <div class="mt-4 pt-3 border-top">
                    <h6 class="mb-3">Property Summary</h6>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Listed:</span>
                        <span>{{ property.created_at|date:"M d, Y" }}</span>
                    </div>
                    <div class="d-flex justify-content-between mb-2">
                        <span>Price:</span>
                        <span class="fw-bold">${{ property.sale_price|floatformat:0 }}</span>
                    </div>
                    {% if property.is_negotiable %}
                    <div class="d-flex justify-content-between mb-2">
                        <span>Negotiable:</span>
                        <span class="text-success">Yes</span>
                    </div>
                    {% endif %}
                </div>

                <!-- Report Property -->
                <div class="mt-4 pt-3 border-top">
                    <a href="{% url 'contact_support' %}?subject=Report Property: {{ property.name }}" class="btn btn-outline-danger btn-sm w-100">
                        <i class="fas fa-flag me-2"></i>
                        Report this Property
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scroll to Top Button -->
<button id="scrollToTop" class="scroll-to-top" title="Scroll to top" style="position: fixed; bottom: 30px; right: 30px; width: 50px; height: 50px; background: var(--primary-color); color: white; border: none; border-radius: 50%; font-size: 1.2rem; cursor: pointer; opacity: 0; visibility: hidden; transition: all 0.3s ease; z-index: 1000;">
    <i class="fas fa-chevron-up"></i>
</button>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Scroll to top functionality
    const scrollToTopBtn = document.getElementById('scrollToTop');
    
    // Show/hide scroll to top button
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            scrollToTopBtn.style.opacity = '1';
            scrollToTopBtn.style.visibility = 'visible';
        } else {
            scrollToTopBtn.style.opacity = '0';
            scrollToTopBtn.style.visibility = 'hidden';
        }
    });

    // Scroll to top when button is clicked
    scrollToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
});
</script>
{% endblock %}
