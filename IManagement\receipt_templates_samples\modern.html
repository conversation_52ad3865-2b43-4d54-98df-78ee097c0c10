<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Modern Receipt</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
      max-width: 650px;
      margin: 20px auto;
      background: #fafafa;
      color: #2c3e50;
      padding: 25px;
      border-radius: 10px;
      box-shadow: 0 0 15px rgba(0,0,0,0.1);
      line-height: 1.6;
    }

    .receipt-header {
      border-bottom: 2px solid #3498db;
      padding-bottom: 15px;
      margin-bottom: 30px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .logo-section {
      flex-shrink: 0;
    }

    .logo-image {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      object-fit: cover;
      border: 2px solid #3498db;
      background: white;
    }

    .title-section {
      flex-grow: 1;
      text-align: center;
    }

    .receipt-title {
      margin: 0;
      font-weight: 700;
      font-size: 2em;
      color: #2980b9;
    }

    .receipt-subtitle {
      font-weight: 600;
      font-size: 1em;
      color: #7f8c8d;
      margin-top: 5px;
    }

    .company-info {
      background: #ecf0f1;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 25px;
      text-align: center;
    }

    .company-name {
      font-size: 1.3em;
      font-weight: 700;
      color: #2c3e50;
      margin-bottom: 8px;
    }

    .company-contact {
      color: #7f8c8d;
      font-size: 0.95em;
      margin-bottom: 3px;
    }

    .company-message {
      color: #27ae60;
      font-weight: 600;
      font-style: italic;
      margin-top: 10px;
    }

    .client-details {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-bottom: 25px;
    }

    .detail-block {
      background: #ecf0f1;
      padding: 15px;
      border-radius: 8px;
    }

    .detail-label {
      font-weight: 700;
      color: #34495e;
      margin-bottom: 8px;
      text-transform: uppercase;
      font-size: 0.85em;
      letter-spacing: 0.5px;
    }

    .detail-content {
      color: #2c3e50;
      font-size: 0.95em;
    }

    .items-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0 12px;
      margin-bottom: 25px;
    }

    .items-table th {
      text-align: left;
      color: #34495e;
      font-weight: 700;
      padding-bottom: 10px;
      border-bottom: 2px solid #bdc3c7;
      font-size: 0.95em;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .items-table th:nth-child(2),
    .items-table th:nth-child(3),
    .items-table th:nth-child(4) {
      text-align: right;
    }

    .items-table td {
      background: #ecf0f1;
      padding: 12px 15px;
      border-radius: 8px;
      vertical-align: middle;
    }

    .items-table td:nth-child(2),
    .items-table td:nth-child(3),
    .items-table td:nth-child(4) {
      text-align: right;
      font-family: 'Courier New', monospace;
    }

    .item-name {
      font-weight: 600;
      color: #2c3e50;
    }

    .totals-section {
      background: #ecf0f1;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 30px;
    }

    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #bdc3c7;
    }

    .total-row:last-child {
      border-bottom: none;
      font-weight: 700;
      font-size: 1.4em;
      color: #27ae60;
      padding-top: 15px;
      border-top: 2px solid #27ae60;
      margin-top: 10px;
    }

    .total-label {
      font-weight: 600;
      color: #34495e;
    }

    .total-value {
      font-family: 'Courier New', monospace;
      font-weight: 600;
    }

    .signatures-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 30px;
      margin-bottom: 30px;
    }

    .signature-block {
      text-align: center;
    }

    .signature-label {
      font-weight: 600;
      color: #7f8c8d;
      margin-bottom: 15px;
      text-transform: uppercase;
      font-size: 0.9em;
      letter-spacing: 0.5px;
    }

    .signature-area {
      height: 80px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 10px;
    }

    .signature-area img {
      max-width: 100%;
      max-height: 70px;
      object-fit: contain;
    }

    .signature-line {
      border-top: 1px solid #bdc3c7;
      width: 200px;
      margin: 0 auto;
      padding-top: 5px;
      font-size: 0.8em;
      color: #95a5a6;
    }

    .receipt-footer {
      font-size: 0.9em;
      color: #95a5a6;
      text-align: center;
      font-style: italic;
    }

    @media (max-width: 768px) {
      body {
        margin: 10px;
        padding: 15px;
      }
      
      .receipt-header {
        flex-direction: column;
        text-align: center;
        gap: 15px;
      }
      
      .client-details,
      .signatures-section {
        grid-template-columns: 1fr;
        gap: 15px;
      }
      
      .items-table th,
      .items-table td {
        padding: 8px;
        font-size: 0.9em;
      }
      
      .receipt-title {
        font-size: 1.7em;
      }
    }

    @media print {
      body {
        background: white;
        box-shadow: none;
        margin: 0;
        padding: 20px;
      }
    }
  </style>
  <script>
    // Auto-generate receipt number and date if not provided
    document.addEventListener('DOMContentLoaded', function() {
      // Generate random receipt number
      const receiptNumberElements = document.querySelectorAll('[data-auto="receipt-number"]');
      receiptNumberElements.forEach(element => {
        if (!element.textContent.trim()) {
          const randomNumber = Math.floor(Math.random() * 900000) + 100000;
          element.textContent = `#RCP${randomNumber}`;
        }
      });
      
      // Generate current date and time
      const dateElements = document.querySelectorAll('[data-auto="date"]');
      dateElements.forEach(element => {
        if (!element.textContent.trim()) {
          const now = new Date();
          const day = String(now.getDate()).padStart(2, '0');
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const year = now.getFullYear();
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          
          element.textContent = `${day}/${month}/${year} ${hours}:${minutes}`;
        }
      });
    });
  </script>
</head>
<body>
  <header class="receipt-header">
    <div class="logo-section">
      {% if avatar_url %}
        <img src="{{ avatar_url }}" alt="Company Logo" class="logo-image" />
      {% else %}
        <div style="width: 80px; height: 80px; background: #ecf0f1; border-radius: 50%; border: 2px solid #3498db;"></div>
      {% endif %}
    </div>

    <div class="title-section">
      <h1 class="receipt-title">Receipt</h1>
      <p class="receipt-subtitle">Transaction Record</p>
    </div>

    <div style="width: 80px;"></div>
  </header>

  <section class="company-info">
    <div class="company-name">{{ company_name }}</div>
    {% if phone_number %}
    <div class="company-contact">Phone: {{ phone_number }}</div>
    {% endif %}
    {% if secondary_email %}
    <div class="company-contact">Email: {{ secondary_email }}</div>
    {% endif %}
    <div class="company-message">Thank you for your business!</div>
  </section>

  <section class="client-details">
    <div class="detail-block">
      <div class="detail-label">Customer Details</div>
      <div class="detail-content">
        {% if client_name %}
          <strong>{{ client_name }}</strong><br>
        {% endif %}
        {% if client_email %}
          {{ client_email }}<br>
        {% endif %}
        {% if client_phone %}
          {{ client_phone }}<br>
        {% endif %}
        {% if client_address %}
          {{ client_address }}
        {% endif %}
      </div>
    </div>
    
    <div class="detail-block">
      <div class="detail-label">Receipt Details</div>
      <div class="detail-content">
        <strong>Receipt No:</strong> 
        {% if receipt_number %}
          {{ receipt_number }}
        {% else %}
          <span data-auto="receipt-number"></span>
        {% endif %}
        <br>
        <strong>Date:</strong> 
        {% if transaction_date %}
          {{ transaction_date }}
        {% else %}
          <span data-auto="date"></span>
        {% endif %}
      </div>
    </div>
  </section>

  <table class="items-table">
    <thead>
      <tr>
        <th>Item Description</th>
        <th>Qty</th>
        <th>Unit Price</th>
        <th>Total</th>
      </tr>
    </thead>
    <tbody>
      {% for item in items %}
      <tr>
        <td class="item-name">{{ item.name }}</td>
        <td>{{ item.quantity }}</td>
        <td>{{ currency_symbol }}{{ item.unit_price }}</td>
        <td>{{ currency_symbol }}{{ item.total_price }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>

  <section class="totals-section">
    <div class="total-row">
      <span class="total-label">Subtotal:</span>
      <span class="total-value">{{ currency_symbol }} {{ subtotal|default:total_price }}</span>
    </div>
    {% if tax_amount %}
    <div class="total-row">
      <span class="total-label">Tax:</span>
      <span class="total-value">{{ currency_symbol }} {{ tax_amount }}</span>
    </div>
    {% endif %}
    {% if discount_amount %}
    <div class="total-row">
      <span class="total-label">Discount:</span>
      <span class="total-value">-{{ currency_symbol }} {{ discount_amount }}</span>
    </div>
    {% endif %}
    <div class="total-row">
      <span class="total-label">Total Amount:</span>
      <span class="total-value">{{ currency_symbol }} {{ total_price }}</span>
    </div>
  </section>

  <section class="signatures-section">
    <div class="signature-block">
      <div class="signature-label">Authorized Signature</div>
      <div class="signature-area">
        {% if signature_url %}
          <img src="{{ signature_url }}" alt="Signature" />
        {% endif %}
      </div>
      <div class="signature-line">Signature</div>
    </div>

    <div class="signature-block">
      <div class="signature-label">Official Stamp</div>
      <div class="signature-area">
        {% if stamp_url %}
          <img src="{{ stamp_url }}" alt="Official Stamp" />
        {% endif %}
      </div>
      <div class="signature-line">Stamp</div>
    </div>
  </section>

  <footer class="receipt-footer">
    <p>This receipt serves as proof of transaction • Please retain for your records</p>
  </footer>
</body>
</html>