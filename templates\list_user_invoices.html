{% extends "base.html" %}

{% block title %}My Invoices{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Your Invoices</h2>
        <a href="{% url 'create_invoice' %}" class="btn btn-primary">Create New Invoice</a>
    </div>

    <!-- Date filters -->
    <form method="get" class="d-flex mb-3 gap-2">
        <input type="date" name="created_at" class="form-control" value="{{ created_at_filter }}" placeholder="Filter by creation date">
        <input type="date" name="issue_date" class="form-control" value="{{ issue_date_filter }}" placeholder="Filter by issue date">
        <button type="submit" class="btn btn-outline-secondary">Filter</button>
        {% if created_at_filter or issue_date_filter %}
            <a href="{% url 'list_user_invoices' %}" class="btn btn-outline-danger">Clear Filters</a>
        {% endif %}
    </form>
    
    {% if created_at_filter or issue_date_filter %}
        <div class="alert alert-info">
            <i class="fas fa-filter me-2"></i>
            Showing filtered invoices
            {% if created_at_filter %}
                (Created: {{ created_at_filter }})
            {% endif %}
            {% if issue_date_filter %}
                (Issue Date: {{ issue_date_filter }})
            {% endif %}
            <span class="badge bg-primary ms-2">{{ total_invoices }} invoice{{ total_invoices|pluralize }}</span>
        </div>
    {% endif %}

    <table class="table table-striped mt-3">
        <thead>
            <tr>
                <th>ID</th>
                <th>Invoice Number</th>
                <th>Template</th>
                <th>Total Price</th>
                <th>Issue Date</th>
                <th>Created At</th>
                <th>Updated At</th>
                <th>Actions</th>
            </tr>
        </thead>
        <tbody id="invoiceList">
            <!-- Invoices will load here -->
            {% for invoice in page_obj %}
            <tr>
                <td>{{ invoice.id }}</td>
                <td>{{ invoice.invoice_number }}</td>
                <td>{{ invoice.template.name }}</td>
                <td>{{ invoice.currency_symbol }}{{ invoice.total_price|floatformat:2 }}</td>
                <td>{{ invoice.issue_date|date:"M d, Y" }}</td>
                <td>{{ invoice.created_at|date:"M d, Y H:i" }}</td>
                <td>{{ invoice.updated_at|date:"M d, Y H:i" }}</td>
                <td>
                    <a href="/invoice-templates/{{ invoice.id }}/?invoice=1" target="_blank" class="btn btn-primary btn-sm me-2">Preview</a>
                    <a href="/invoices/{{ invoice.id }}/download/" class="btn btn-primary btn-sm me-2">Download PDF</a>
                    <button class="btn btn-outline-secondary btn-sm" onclick="openEmailModal({{ invoice.id }})">Send Email</button>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

    <!-- Pagination controls -->
    {% if page_obj.paginator.num_pages > 1 %}
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <span>Showing {{ page_obj.start_index }}-{{ page_obj.end_index }} of {{ page_obj.paginator.count }} invoices</span>
        </div>
        <div class="d-flex align-items-center">
            <!-- Page navigation -->
            <nav aria-label="Invoice pagination">
                <ul class="pagination mb-0 me-3">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}{% if issue_date_filter %}&issue_date={{ issue_date_filter }}{% endif %}" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}{% if issue_date_filter %}&issue_date={{ issue_date_filter }}{% endif %}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                    {% endif %}
                    
                    <!-- Show page numbers -->
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}{% if issue_date_filter %}&issue_date={{ issue_date_filter }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}{% if issue_date_filter %}&issue_date={{ issue_date_filter }}{% endif %}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if created_at_filter %}&created_at={{ created_at_filter }}{% endif %}{% if issue_date_filter %}&issue_date={{ issue_date_filter }}{% endif %}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            
            <!-- Direct page input -->
            <div class="input-group" style="width: 120px;">
                <input type="number" class="form-control form-control-sm" id="pageInput" 
                       placeholder="Page" min="1" max="{{ page_obj.paginator.num_pages }}" 
                       value="{{ page_obj.number }}">
                <button class="btn btn-outline-secondary btn-sm" type="button" onclick="goToPage()">Go</button>
            </div>
        </div>
    </div>
    {% endif %}

    <p id="noInvoicesMsg" style="display:none;">No invoices found. <a href="{% url 'create_invoice' %}">Create your first invoice</a>.</p>
</div>

<!-- Send Invoice Email Modal -->
<div class="modal fade" id="sendInvoiceEmailModal" tabindex="-1" aria-labelledby="sendInvoiceEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="sendInvoiceEmailModalLabel">Send Invoice via Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="sendInvoiceEmailForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="recipientEmail" class="form-label">Recipient's Email *</label>
                        <input type="email" class="form-control" id="recipientEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="customMessage" class="form-label">Custom Message (optional)</label>
                        <textarea class="form-control" id="customMessage" name="message" rows="3" placeholder="Enter a custom message to include with the invoice..."></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        The invoice will be sent as a PDF attachment.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-1"></i>Send Email
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Alert Container for notifications -->
<div id="alertContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>

<script>
async function loadInvoices() {
    try {
        // Get current URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const currentPage = urlParams.get('page') || '1';
        const createdAtFilter = urlParams.get('created_at') || '';
        const issueDateFilter = urlParams.get('issue_date') || '';
        
        // Build the API URL with current parameters
        let apiUrl = "{% url 'list_user_invoices' %}?format=json";
        if (currentPage !== '1') {
            apiUrl += `&page=${currentPage}`;
        }
        if (createdAtFilter) {
            apiUrl += `&created_at=${createdAtFilter}`;
        }
        if (issueDateFilter) {
            apiUrl += `&issue_date=${issueDateFilter}`;
        }
        
        const response = await fetch(apiUrl);
        if (!response.ok) throw new Error('Network response was not ok');
        const data = await response.json();

        const invoiceList = document.getElementById("invoiceList");
        const noInvoicesMsg = document.getElementById("noInvoicesMsg");
        
        // Clear existing content
        invoiceList.innerHTML = "";

        // Check if we have invoices data
        const invoices = data.invoices || data; // Handle both new and old response formats
        
        if (invoices.length === 0) {
            noInvoicesMsg.style.display = "block";
            return;
        }
        noInvoicesMsg.style.display = "none";

        invoices.forEach(invoice => {
            const row = document.createElement("tr");
            row.innerHTML = `
                <td>${invoice.id}</td>
                <td>${invoice.invoice_number}</td>
                <td>${invoice.template_name}</td>
                <td>${invoice.currency_symbol}${invoice.total_price.toFixed(2)}</td>
                <td>${new Date(invoice.issue_date).toLocaleDateString()}</td>
                <td>${new Date(invoice.created_at).toLocaleDateString()}</td>
                <td>${new Date(invoice.updated_at).toLocaleString()}</td>
                <td>
                    <a href="/invoice-templates/${invoice.id}/?invoice=1" target="_blank" class="btn btn-primary btn-sm me-2">Preview</a>
                    <a href="/invoices/${invoice.id}/download/" class="btn btn-primary btn-sm me-2">Download PDF</a>
                    <button class="btn btn-outline-white btn-primary btn-sm me-2" onclick="openEmailModal(${invoice.id})">Send Email</button>
                </td>
            `;
            invoiceList.appendChild(row);
        });
    } catch (error) {
        console.error("Error loading invoices:", error);
        document.getElementById("invoiceList").innerHTML = `<tr><td colspan="8">Failed to load invoices.</td></tr>`;
    }
}

function goToPage() {
    const pageInput = document.getElementById('pageInput');
    const pageNumber = parseInt(pageInput.value);
    const maxPages = {{ page_obj.paginator.num_pages }};
    
    if (pageNumber >= 1 && pageNumber <= maxPages) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set('page', pageNumber);
        window.location.search = urlParams.toString();
    } else {
        alert(`Please enter a valid page number between 1 and ${maxPages}`);
        pageInput.value = {{ page_obj.number }};
    }
}

function openEmailModal(invoiceId) {
    // Store the invoice ID for later use
    document.getElementById('sendInvoiceEmailForm').dataset.invoiceId = invoiceId;
    
    // Clear previous form data
    document.getElementById('recipientEmail').value = '';
    document.getElementById('customMessage').value = '';
    
    // Open the modal
    const modal = new bootstrap.Modal(document.getElementById('sendInvoiceEmailModal'));
    modal.show();
}

function showAlert(message, type = 'success') {
    const alertContainer = document.getElementById('alertContainer');
    const alertId = 'alert-' + Date.now();
    
    const alertHTML = `
        <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check' : 'exclamation'}-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    alertContainer.insertAdjacentHTML('beforeend', alertHTML);
    
    // Auto-remove alert after 5 seconds
    setTimeout(() => {
        const alert = document.getElementById(alertId);
        if (alert) {
            alert.remove();
        }
    }, 5000);
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

document.getElementById('sendInvoiceEmailForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const invoiceId = this.dataset.invoiceId;
    const email = document.getElementById('recipientEmail').value;
    const message = document.getElementById('customMessage').value;
    const submitButton = this.querySelector('button[type="submit"]');
    const originalButtonText = submitButton.innerHTML;

    // Disable button and show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Sending...';

    try {
        const response = await fetch(`/invoices/${invoiceId}/email/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken'),
            },
            body: JSON.stringify({ 
                email: email, 
                message: message 
            })
        });
        
        const result = await response.json();

        if (response.ok) {
            showAlert('Invoice sent successfully!', 'success');
            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('sendInvoiceEmailModal'));
            modal.hide();
            
            // Reset form
            this.reset();
        } else {
            showAlert('Error sending invoice: ' + (result.error || 'Unknown error'), 'danger');
        }
    } catch (err) {
        console.error('Error sending invoice:', err);
        showAlert('Error sending the invoice. Please try again.', 'danger');
    } finally {
        // Re-enable button and restore original text
        submitButton.disabled = false;
        submitButton.innerHTML = originalButtonText;
    }
});

document.addEventListener("DOMContentLoaded", loadInvoices);
</script>

{% endblock %}