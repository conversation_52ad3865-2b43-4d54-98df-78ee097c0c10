<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>InventoryPro - Smart Inventory Management</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --accent-color: #3b82f6;
            --dark-color: #1f2937;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 500"><polygon fill="rgba(255,255,255,0.1)" points="0,0 1000,300 1000,500 0,200"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-title {
            font-size: 3.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .hero-subtitle {
            font-size: 1.3rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 2rem;
            font-weight: 300;
        }

        .btn-hero {
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            text-decoration: none;
            transition: all 0.3s ease;
            display: inline-block;
            margin: 10px;
        }

        .btn-primary-hero {
            background: white;
            color: var(--primary-color);
            border: 2px solid white;
        }

        .btn-primary-hero:hover {
            background: transparent;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .btn-outline-hero {
            background: transparent;
            color: white;
            border: 2px solid white;
        }

        .btn-outline-hero:hover {
            background: white;
            color: var(--primary-color);
            transform: translateY(-2px);
        }

        .features-section {
            padding: 100px 0;
            background: #f8fafc;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 40px 30px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            height: 100%;
            border: none;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2rem;
            color: white;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 15px;
        }

        .feature-description {
            color: #6b7280;
            line-height: 1.7;
        }

        .stats-section {
            background: var(--dark-color);
            color: white;
            padding: 80px 0;
        }

        .stat-item {
            text-align: center;
            padding: 20px;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 700;
            color: var(--accent-color);
            display: block;
        }

        .stat-label {
            font-size: 1.1rem;
            opacity: 0.8;
            margin-top: 10px;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-link {
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .cta-section {
            background: linear-gradient(135deg, var(--accent-color), var(--primary-color));
            padding: 100px 0;
            text-align: center;
        }

        .cta-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 20px;
        }

        .cta-subtitle {
            font-size: 1.2rem;
            color: rgba(255,255,255,0.9);
            margin-bottom: 40px;
        }

        @media (max-width: 768px) {
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
            
            .btn-hero {
                display: block;
                margin: 10px auto;
                max-width: 250px;
            }
        }

        .loading {
            display: none;
        }

        .error-message {
            background: #fee2e2;
            color: #dc2626;
            padding: 10px 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: none;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="/">
                <i class="fas fa-boxes"></i> InventoryPro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">Features</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link"  href="/public/goods/">Browse Goods</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-primary px-3 ms-2" href="{% url 'register' %}">Sign Up</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6">
                    <div class="hero-content">
                        <h1 class="hero-title">Smart Inventory Management Made Simple</h1>
                        <p class="hero-subtitle">
                            Streamline your business operations with our powerful inventory management system. 
                            Track stock, manage stores, and grow your business efficiently.
                        </p>
                        <div class="hero-buttons">
                            <a href="/register/" class="btn-hero btn-primary-hero">
                                <i class="fas fa-rocket me-2"></i>Get Started Free
                            </a>
                            <a href="/public/goods/" class="btn-hero btn-outline-hero">
                                <i class="fas fa-search me-2"></i>Browse Products
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="text-center">
                        <i class="fas fa-warehouse" style="font-size: 15rem; color: rgba(255,255,255,0.2);"></i>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="row mb-5">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="display-4 fw-bold mb-3">Powerful Features for Your Business</h2>
                    <p class="lead text-muted">Everything you need to manage your inventory and grow your business</p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-store"></i>
                        </div>
                        <h3 class="feature-title">Multi-Store Management</h3>
                        <p class="feature-description">
                            Manage multiple stores and locations from a single dashboard. 
                            Keep track of inventory across all your business locations.
                        </p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <h3 class="feature-title">Inventory Tracking</h3>
                        <p class="feature-description">
                            Real-time inventory tracking with automated alerts for low stock. 
                            Never run out of your best-selling products again.
                        </p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h3 class="feature-title">Analytics & Reports</h3>
                        <p class="feature-description">
                            Comprehensive analytics and reporting tools to help you make 
                            data-driven decisions for your business growth.
                        </p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h3 class="feature-title">Mobile Friendly</h3>
                        <p class="feature-description">
                            Access your inventory management system from anywhere with our 
                            responsive design that works on all devices.
                        </p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-receipt"></i>
                        </div>
                        <h3 class="feature-title">Receipt Generation</h3>
                        <p class="feature-description">
                            Create professional receipts and invoices with customizable 
                            templates. Download as PDF for easy sharing.
                        </p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">Secure & Reliable</h3>
                        <p class="feature-description">
                            Your data is protected with enterprise-grade security. 
                            Reliable cloud infrastructure ensures 99.9% uptime.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="10000">0</span>
                        <div class="stat-label">Happy Customers</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="50000">0</span>
                        <div class="stat-label">Products Managed</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="1000">0</span>
                        <div class="stat-label">Stores Connected</div>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <div class="stat-item">
                        <span class="stat-number" data-count="99">0</span>
                        <div class="stat-label">Uptime %</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="cta-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-8 mx-auto">
                    <h2 class="cta-title">Ready to Transform Your Business?</h2>
                    <p class="cta-subtitle">
                        Join thousands of businesses that trust InventoryPro for their inventory management needs.
                    </p>
                    <a href="/register/" class="btn-hero btn-primary-hero">
                        <i class="fas fa-arrow-right me-2"></i>Start Your Free Trial
                    </a>
                </div>
            </div>
        </div>
    </section>

   <!-- Footer -->
<!-- Footer -->
<footer class="bg-dark text-light py-5">
    <div class="container">
        <div class="row">
            <!-- Company Info Section -->
            <div class="col-lg-4 mb-4">
                <h5 class="fw-bold mb-3 text-white">Company House</h5>
                <p class="text-light mb-3">
                    We are a registered IT company in the UK with company number <span class="text-warning">14676167</span>
                </p>
            </div>

            <!-- Contact Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Contact</h6>
                <div class="contact-info">
                    <p class="text-light mb-2">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <strong>Address:</strong> 13L Queensway, Ponders End, Enfield, London EN3 4SA
                    </p>
                    <p class="text-light mb-2">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <strong>Email:</strong> <EMAIL>
                    </p>
                    <p class="text-light mb-0">
                        <i class="fas fa-phone me-2 text-primary"></i>
                        <strong>Tel:</strong> 07500503952
                    </p>
                </div>
            </div>

            <!-- Subscribe Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Subscribe Now</h6>
                <p class="text-light mb-3">
                    Don't miss our future updates! Get Subscribed Today!
                </p>
                <div class="mb-3">
            <div class="col-md-6 text-md-start">
                <a href="/register/" class="btn btn-primary">
                    <i class="fas fa-rocket me-2"></i>Register
                </a>
            </div>
                </div>
                <!-- Social Icons -->
                <div class="social-icons">
                    <a href="https://www.facebook.com/amatip.info.tech" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://x.com/amatipIT" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.tiktok.com/@amatip_it?is_from_webapp=1&sender_device=pc" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-tiktok"></i>
                    </a>
                    <a href="https://youtube.com/@amatip_it?si=eQ54UaVIM-DgLOAr" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="https://www.instagram.com/amatip_it/profilecard/?igsh=MWRzbGV5b3h1MTQ2Yw==" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="d-flex flex-wrap gap-4">
                    <a href="#features" class="text-muted text-decoration-none">Features</a>
                    <a href="/public/goods/" class="text-muted text-decoration-none">Browse Goods</a>
                    <a href="/login/" class="text-muted text-decoration-none">Login</a>
                    <a href="/register/" class="text-muted text-decoration-none">Sign Up</a>
                </div>
            </div>

        </div>

        <hr class="my-4 border-secondary">
        
        <!-- Copyright -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-muted">&copy; 2023 Amatip. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <small class="text-muted">Built with Django & Bootstrap</small>
            </div>
        </div>
    </div>
</footer>



    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script>
        // Animated counter for stats
        function animateCounter(element, target) {
            let current = 0;
            const increment = target / 100;
            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                element.textContent = Math.floor(current).toLocaleString();
            }, 20);
        }

        // Intersection Observer for stats animation
        const observerCallback = (entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counters = entry.target.querySelectorAll('.stat-number');
                    counters.forEach(counter => {
                        const target = parseInt(counter.getAttribute('data-count'));
                        animateCounter(counter, target);
                    });
                    observer.unobserve(entry.target);
                }
            });
        };

        const observer = new IntersectionObserver(observerCallback, {
            threshold: 0.5
        });

        const statsSection = document.querySelector('.stats-section');
        if (statsSection) {
            observer.observe(statsSection);
        }

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Navbar background change on scroll
        window.addEventListener('scroll', () => {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.style.background = 'rgba(255,255,255,0.98)';
            } else {
                navbar.style.background = 'rgba(255,255,255,0.95)';
            }
        });

        // Add loading states for buttons
        document.querySelectorAll('a[href*="/register/"], a[href*="/login/"]').forEach(link => {
            link.addEventListener('click', function(e) {
                if (!this.classList.contains('loading')) {
                    this.classList.add('loading');
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
                    
                    // Reset after 3 seconds if page doesn't change
                    setTimeout(() => {
                        if (this.classList.contains('loading')) {
                            this.classList.remove('loading');
                            this.innerHTML = originalText;
                        }
                    }, 3000);
                }
            });
        });
    </script>
</body>
</html>