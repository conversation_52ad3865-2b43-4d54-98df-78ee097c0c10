{% extends 'base.html' %}

{% block title %}Email Verification{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-header i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.verification-code {
    font-size: 1.2rem;
    text-align: center;
    letter-spacing: 0.5rem;
    margin-bottom: 1rem;
}

.trust-device {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
}

.resend-code {
    text-align: center;
    margin-top: 1rem;
}

.email-info {
    background: #e3f2fd;
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 1rem;
    text-align: center;
}
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="auth-container">
        <div class="auth-header">
            <i class="fas fa-envelope-open"></i>
            <h2>Email Verification</h2>
            <p class="text-muted">Enter the verification code sent to your email</p>
        </div>

        {% if email %}
        <div class="email-info">
            <i class="fas fa-envelope me-2"></i>
            Code sent to: <strong>{{ email }}</strong>
        </div>
        {% endif %}

        {% if error %}
        <div class="alert alert-danger">
            <i class="fas fa-exclamation-triangle me-2"></i>
            {{ error }}
        </div>
        {% endif %}

        {% if message %}
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            {{ message }}
        </div>
        {% endif %}

        <form method="post" action="{% url 'verify_email_code' %}">
            {% csrf_token %}
            <div class="mb-3">
                <label for="code" class="form-label">Verification Code</label>
                <input type="text" 
                       class="form-control verification-code" 
                       id="code" 
                       name="code" 
                       maxlength="6" 
                       pattern="[0-9]{6}" 
                       placeholder="000000"
                       required
                       autocomplete="off">
                <div class="form-text">Enter the 6-digit code from your email</div>
            </div>

            <div class="trust-device">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="trust_device" name="trust_device" value="1">
                    <label class="form-check-label" for="trust_device">
                        <strong>Trust this device for 7 days</strong>
                        <small class="d-block text-muted">Skip verification on this device for a week</small>
                    </label>
                </div>
            </div>

            <button type="submit" class="btn btn-primary w-100 mb-3">
                <i class="fas fa-check me-2"></i>Verify & Login
            </button>
        </form>

        <div class="resend-code">
            <p class="text-muted mb-2">Didn't receive the code?</p>
            <a href="{% url 'login' %}" class="btn btn-outline-secondary btn-sm">
                <i class="fas fa-redo me-1"></i>Try Again
            </a>
        </div>

        <hr>
        
        <div class="text-center">
            <a href="{% url 'login' %}" class="btn btn-link">
                <i class="fas fa-arrow-left me-1"></i>Back to Login
            </a>
        </div>
    </div>
</div>

<script>
// Auto-focus on code input
document.getElementById('code').focus();

// Auto-submit when 6 digits are entered
document.getElementById('code').addEventListener('input', function(e) {
    if (e.target.value.length === 6) {
        // Small delay to allow user to see the complete code
        setTimeout(() => {
            e.target.form.submit();
        }, 500);
    }
});

// Only allow numbers
document.getElementById('code').addEventListener('keypress', function(e) {
    if (!/[0-9]/.test(e.key) && !['Backspace', 'Delete', 'Tab', 'Enter'].includes(e.key)) {
        e.preventDefault();
    }
});
</script>
{% endblock %}
