from django.shortcuts import redirect
from django.urls import reverse
from django.contrib.auth.models import AnonymousUser
from .models import UserProfile


class EmailVerificationMiddleware:
    """
    Middleware to ensure users cannot access protected pages without email verification
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # URLs that don't require email verification
        self.exempt_urls = [
            '/login/',
            '/accounts/login/',
            '/register/',
            '/enhanced-register/',
            '/verify-email/',
            '/verify-2fa-code/',  # 2FA verification
            '/resend-verification/',
            '/forget-password/',
            '/reset-password/',
            '/logout/',
            '/admin/',
            '/static/',
            '/media/',
            '/',  # Home page
            '/public/',  # Public pages
        ]
        
        # URLs that require email verification (protected pages)
        self.protected_urls = [
            '/dashboard/',
            '/stores/',
            '/goods/',
            '/categories/',
            '/receipts/',
            '/invoices/',
            '/profile/',
            '/notifications/',
            '/property-management/',
            '/private-seller-store/',
            '/manage-inventory/',
            '/goods-management/',
            '/contact-support/',
            '/my-tickets/',
        ]

    def __call__(self, request):
        # Skip middleware for anonymous users
        if isinstance(request.user, AnonymousUser):
            response = self.get_response(request)
            return response
            
        # Skip middleware for exempt URLs
        if any(request.path.startswith(url) for url in self.exempt_urls):
            response = self.get_response(request)
            return response
            
        # Check if user is authenticated and accessing protected URLs
        if request.user.is_authenticated:
            # Check if accessing protected URLs
            if any(request.path.startswith(url) for url in self.protected_urls):
                try:
                    profile = request.user.profile
                    if not profile.is_verified:
                        # User is not verified, redirect to verification page
                        request.session['verification_email'] = request.user.email
                        request.session['verification_user_id'] = request.user.id
                        return redirect(f'/verify-email/?user_id={request.user.id}&email={request.user.email}')
                except UserProfile.DoesNotExist:
                    # Create profile for existing users who don't have one
                    UserProfile.objects.create(user=request.user, is_verified=False)
                    request.session['verification_email'] = request.user.email
                    request.session['verification_user_id'] = request.user.id
                    return redirect(f'/verify-email/?user_id={request.user.id}&email={request.user.email}')

        response = self.get_response(request)
        return response
