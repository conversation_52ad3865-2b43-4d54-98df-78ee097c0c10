from django.db import models
from django.contrib.auth.models import User
from django.utils.safestring import mark_safe
from django.urls import reverse
from django.conf import settings
from django_countries.fields import CountryField

# -------------------------------
# Profile and Location Models
# -------------------------------

class Country(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=3)

    def __str__(self):
        return self.name


class State(models.Model):
    country = models.ForeignKey(Country, on_delete=models.CASCADE)
    name = models.CharField(max_length=100)

    def __str__(self):
        return self.name


class UserProfile(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    phone_number = models.CharField(max_length=20, blank=True, null=True)
    secondary_email = models.EmailField(blank=True, null=True)
    avatar = models.ImageField(upload_to='avatars/', blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    bio = models.TextField(blank=True, null=True)
    website_url = models.URLField(blank=True, null=True)
    company_name = models.CharField(max_length=255, blank=True, null=True)

    # Location fields for user signup location
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True, help_text="User's country during signup")
    state = models.ForeignKey(State, on_delete=models.SET_NULL, null=True, blank=True, help_text="User's state during signup")

    # New fields for 2FA
    verification_code = models.CharField(max_length=6, blank=True, null=True)  # To store the verification code
    is_verified = models.BooleanField(default=False)  # Track whether the user has verified their email

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.user.username


# -------------------------------
# Store and Category Models
# -------------------------------

class Store(models.Model):
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='stores')
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, default='')
    location = models.CharField(max_length=255, blank=True, null=True)
    is_active = models.BooleanField(default=True)
    authorized_users = models.ManyToManyField(User, related_name='authorized_stores', blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Store'
        verbose_name_plural = 'Stores'

    def __str__(self):
        return self.name


class Category(models.Model):
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='categories')
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)  # Add this line
    updated_at = models.DateTimeField(auto_now=True)  

    class Meta:
        unique_together = ('owner', 'name')
        ordering = ['created_at']
        verbose_name = 'Category'
        verbose_name_plural = 'Categories'

    def __str__(self):
        return self.name

# -------------------------------
# Goods Model
# -------------------------------

class Goods(models.Model):
    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='goods')
    store = models.ForeignKey(Store, on_delete=models.CASCADE, related_name='goods', null=True, blank=True)
    name = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    quantity = models.PositiveIntegerField(default=0)
    currency = models.CharField(max_length=3, default='NGN', choices=[
        ('NGN', '₦'),
        ('USD', '$'),
        ('EUR', '€'),
        ('GBP', '£'),
    ])
    description = models.TextField(blank=True)
    category = models.ForeignKey(Category, on_delete=models.SET_NULL, null=True, blank=True, related_name='goods')
    images = models.JSONField(default=list, blank=True)  # To store image filenames
    is_used = models.BooleanField(default=False)
    available_for_delivery = models.BooleanField(default=False)
    
    DELIVERY_CHOICES = [
        ('within_state', 'Within State'),
        ('within_country', 'Within Country'),
        ('outside_country', 'Outside Country'),
    ]
    delivery_type = models.CharField(max_length=20, choices=DELIVERY_CHOICES, blank=True, null=True)
    available_for_bulk_sales = models.BooleanField(default=False)
    
    # Enhanced Inventory Management Fields
    quantity = models.PositiveIntegerField(default=0, help_text="Current stock quantity")
    units_sold = models.PositiveIntegerField(default=0, help_text="Total units sold")
    cost_price = models.DecimalField(max_digits=10, decimal_places=2, null=True, blank=True, help_text="Cost price per unit")
    track_inventory = models.BooleanField(default=True, help_text="Enable inventory tracking")
    low_stock_threshold = models.PositiveIntegerField(default=5, help_text="Alert when stock falls below this level")
    sku = models.CharField(max_length=100, blank=True, null=True, help_text="Stock Keeping Unit")

    # Location and Rating Fields
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    state = models.ForeignKey(State, on_delete=models.SET_NULL, null=True, blank=True)

    STAR_RATING_CHOICES = [
        (1, '⭐ 1 Star'),
        (2, '⭐⭐ 2 Stars'),
        (3, '⭐⭐⭐ 3 Stars'),
        (4, '⭐⭐⭐⭐ 4 Stars'),
        (5, '⭐⭐⭐⭐⭐ 5 Stars'),
    ]
    star_rating = models.PositiveSmallIntegerField(
        choices=STAR_RATING_CHOICES,
        null=True,
        blank=True,
        help_text="Product rating (1-5 stars)"
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        ordering = ['-created_at']
        unique_together = ('store', 'name')
        verbose_name = 'Goods'
        verbose_name_plural = 'Goods'
    
    def __str__(self):
        return self.name
    
    def get_absolute_url(self):
        return reverse('public_goods_detail', args=[str(self.id)])
    
    def get_first_four_images(self):
        return self.images[:4] if len(self.images) > 4 else self.images
        
    @property
    def first_image_url(self):
        """Get the URL of the first image"""
        if self.images and len(self.images) > 0:
            # Remove any leading slashes or 'media/' to avoid double paths
            image_path = self.images[0]
            if image_path.startswith('/media/'):
                image_path = image_path[7:]  # Remove '/media/' prefix
            elif image_path.startswith('media/'):
                image_path = image_path[6:]  # Remove 'media/' prefix
            return f"{settings.MEDIA_URL}{image_path}"
        return None
        
    @property
    def all_image_urls(self):
        """Get URLs for all images"""
        if self.images:
            urls = []
            for img in self.images:
                # Clean up the path
                image_path = img
                if image_path.startswith('/media/'):
                    image_path = image_path[7:]
                elif image_path.startswith('media/'):
                    image_path = image_path[6:]
                urls.append(f"{settings.MEDIA_URL}{image_path}")
            return urls
        return []
    
    @property
    def currency_symbol(self):
        """Get the currency symbol"""
        currency_symbols = {
            'NGN': '₦',
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
        }
        return currency_symbols.get(self.currency, '₦')

    # Enhanced Inventory Management Methods
    def get_stock_status(self):
        """Get current stock status"""
        if not self.track_inventory:
            return 'not_tracked'
        if self.quantity == 0:
            return 'out_of_stock'
        elif self.quantity <= self.low_stock_threshold:
            return 'low_stock'
        else:
            return 'in_stock'

    def get_stock_status_display(self):
        """Get human-readable stock status"""
        status = self.get_stock_status()
        status_map = {
            'in_stock': 'In Stock',
            'low_stock': 'Low Stock',
            'out_of_stock': 'Out of Stock',
            'not_tracked': 'Not Tracked'
        }
        return status_map.get(status, 'Unknown')

    def is_low_stock(self):
        """Check if item is low on stock"""
        return self.track_inventory and self.quantity <= self.low_stock_threshold and self.quantity > 0

    def is_out_of_stock(self):
        """Check if item is out of stock"""
        return self.track_inventory and self.quantity == 0

    def total_value(self):
        """Calculate total inventory value"""
        return self.quantity * self.price

    def profit_per_unit(self):
        """Calculate profit per unit"""
        if self.cost_price:
            return self.price - self.cost_price
        return None

    def total_profit_potential(self):
        """Calculate total profit potential"""
        profit = self.profit_per_unit()
        if profit:
            return profit * self.quantity
        return None

    def sell_stock(self, quantity, notes=""):
        """Sell stock and update quantities"""
        if not self.track_inventory:
            return True

        if self.quantity >= quantity:
            self.quantity -= quantity
            self.units_sold += quantity
            self.save()

            # Create transaction record
            InventoryTransaction.objects.create(
                goods=self,
                transaction_type='sale',
                quantity=-quantity,
                notes=notes
            )
            return True
        return False

    def add_stock(self, quantity, cost_price=None, notes=""):
        """Add stock and update quantities"""
        self.quantity += quantity
        if cost_price:
            self.cost_price = cost_price
        self.save()

        # Create transaction record
        InventoryTransaction.objects.create(
            goods=self,
            transaction_type='restock',
            quantity=quantity,
            notes=notes
        )
        return True

    def get_star_rating_display(self):
        """Get star rating display"""
        if self.star_rating:
            return '⭐' * self.star_rating
        return 'No Rating'


# -------------------------------
# Inventory Transaction Model
# -------------------------------

class InventoryTransaction(models.Model):
    TRANSACTION_TYPES = [
        ('sale', 'Sale'),
        ('restock', 'Restock'),
        ('adjustment', 'Adjustment'),
        ('return', 'Return'),
    ]

    goods = models.ForeignKey(Goods, on_delete=models.CASCADE, related_name='transactions')
    transaction_type = models.CharField(max_length=20, choices=TRANSACTION_TYPES)
    quantity = models.IntegerField(help_text="Positive for additions, negative for deductions")
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.goods.name} - {self.get_transaction_type_display()}: {self.quantity}"

    def get_transaction_type_display(self):
        """Get human-readable transaction type"""
        return dict(self.TRANSACTION_TYPES).get(self.transaction_type, self.transaction_type)


# -------------------------------
# Receipt and Invoice Models
# -------------------------------

class ReceiptTemplate(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    html_content = models.TextField(help_text="Full HTML template content", blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name

    def preview(self):
        return mark_safe(self.html_content[:200] + "...")


class Receipt(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='receipts', null=True, blank=True)
    template = models.ForeignKey(ReceiptTemplate, on_delete=models.SET_NULL, null=True, blank=True)
    signature = models.ImageField(upload_to='receipts/signatures/', blank=True, null=True)
    stamp = models.ImageField(upload_to='receipts/stamps/', blank=True, null=True)
    total_price = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    currency = models.CharField(max_length=3, default='USD')
    tax_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True, null=True)
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, blank=True, null=True)
    client_name = models.CharField(max_length=200, blank=True, null=True)
    client_email = models.EmailField(blank=True, null=True)
    client_phone = models.CharField(max_length=20, blank=True, null=True)
    client_address = models.TextField(blank=True, null=True)
    client_company = models.CharField(max_length=200, blank=True, null=True)

    def __str__(self):
        return f"Receipt #{self.id} by {self.user.username}"
    
    @property
    def currency_symbol(self):
        symbols = {
            'USD': '$',
            'EUR': '€',
            'NGN': '₦',
            'GBP': '£',
            'JPY': '¥',
            'AUD': 'A$',
            'CAD': 'C$',
            'INR': '₹',
            'CNY': '¥',
            'MXN': '$',
            'BRL': 'R$',
            'ZAR': 'R',
            'CHF': 'CHF',
            'HKD': 'HK$',
            'SGD': 'S$',
            'MYR': 'RM',
            'PKR': '₨',
            'SAR': 'ر.س',
            'TRY': '₺',
            'AED': 'د.إ',
            'SEK': 'kr',
            'NOK': 'kr',
            'DKK': 'kr',
            'THB': '฿',
            'KES': 'KSh',
        }
        return symbols.get(self.currency.upper(), self.currency)


class ReceiptItem(models.Model):
    receipt = models.ForeignKey(Receipt, on_delete=models.CASCADE, related_name='items')
    name = models.CharField(max_length=255)
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=12, decimal_places=2, editable=False)

    def save(self, *args, **kwargs):
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.quantity} x {self.name} @ {self.unit_price}"


class InvoiceTemplate(models.Model):
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    html_content = models.TextField(help_text="Full HTML template content", blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name


class Invoice(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='invoices')
    template = models.ForeignKey(InvoiceTemplate, on_delete=models.SET_NULL, null=True, blank=True)
    invoice_number = models.CharField(max_length=50, unique=True)
    issue_date = models.DateField()
    due_date = models.DateField(null=True, blank=True)
    client_name = models.CharField(max_length=255)
    client_email = models.EmailField(blank=True, null=True)
    client_address = models.TextField(blank=True, null=True)
    client_phone = models.CharField(max_length=30, blank=True, null=True)
    currency = models.CharField(max_length=5, default='USD')
    subtotal = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    tax = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    total_price = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    payment_terms = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    signature = models.ImageField(upload_to='invoices/signatures/', blank=True, null=True)
    stamp = models.ImageField(upload_to='invoices/stamps/', blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Invoice #{self.invoice_number} by {self.user.username}"


class InvoiceItem(models.Model):
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='items')
    sku = models.CharField(max_length=100, blank=True, null=True)
    name = models.CharField(max_length=255)
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=12, decimal_places=2, editable=False)

    def save(self, *args, **kwargs):
        self.total_price = self.quantity * self.unit_price
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.quantity} x {self.name} (SKU: {self.sku}) @ {self.unit_price}"

# -------------------------------
# Miscellaneous
# -------------------------------

class Notification(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    message = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    read = models.BooleanField(default=False)

    def __str__(self):
        return f"Notification for {self.user.username}: {self.message[:20]}"


class ManagedGood(models.Model):
    owner = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    quantity = models.PositiveIntegerField(default=0)
    units_sold = models.PositiveIntegerField(default=0)
    category = models.CharField(max_length=100, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def total_value(self):
        return self.price * self.quantity

    def __str__(self):
        return f"{self.name} - {self.quantity} items"


# -------------------------------
# Property Management Model
# -------------------------------

class Property(models.Model):
    """
    Model for managing user properties with sale functionality
    This handles both personal property management and properties for sale
    """
    PROPERTY_TYPES = [
        ('real_estate', 'Real Estate'),
        ('vehicle', 'Vehicle'),
        ('electronics', 'Electronics'),
        ('furniture', 'Furniture'),
        ('jewelry', 'Jewelry'),
        ('artwork', 'Artwork'),
        ('collectibles', 'Collectibles'),
        ('appliances', 'Appliances'),
        ('tools', 'Tools'),
        ('other', 'Other'),
    ]

    CONDITION_CHOICES = [
        ('excellent', 'Excellent'),
        ('very_good', 'Very Good'),
        ('good', 'Good'),
        ('fair', 'Fair'),
        ('poor', 'Poor'),
    ]

    owner = models.ForeignKey(User, on_delete=models.CASCADE, related_name='properties')
    name = models.CharField(max_length=255, help_text="Property name or title")
    description = models.TextField(blank=True, help_text="Detailed description of the property")
    property_type = models.CharField(max_length=20, choices=PROPERTY_TYPES, default='other')

    # Financial Information
    purchase_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True, help_text="Original purchase price")
    current_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True, help_text="Current estimated value")
    currency = models.CharField(max_length=3, default='NGN', choices=[
        ('NGN', '₦'),
        ('USD', '$'),
        ('EUR', '€'),
        ('GBP', '£'),
    ])

    # Property Details
    condition = models.CharField(max_length=20, choices=CONDITION_CHOICES, default='good')
    purchase_date = models.DateField(null=True, blank=True, help_text="Date when property was acquired")
    warranty_expiry = models.DateField(null=True, blank=True, help_text="Warranty expiration date if applicable")

    # Location and Storage
    location = models.CharField(max_length=255, blank=True, help_text="Where the property is located/stored")
    country = models.ForeignKey(Country, on_delete=models.SET_NULL, null=True, blank=True)
    state = models.ForeignKey(State, on_delete=models.SET_NULL, null=True, blank=True)

    # Documentation
    serial_number = models.CharField(max_length=100, blank=True, help_text="Serial number or unique identifier")
    model_number = models.CharField(max_length=100, blank=True, help_text="Model number if applicable")
    brand = models.CharField(max_length=100, blank=True, help_text="Brand or manufacturer")

    # Insurance and Legal
    insured = models.BooleanField(default=False, help_text="Is this property insured?")
    insurance_value = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True, help_text="Insurance coverage amount")
    insurance_expiry = models.DateField(null=True, blank=True, help_text="Insurance expiration date")

    # Images and Documents
    images = models.JSONField(default=list, blank=True, help_text="List of image filenames")
    documents = models.JSONField(default=list, blank=True, help_text="List of document filenames (receipts, warranties, etc.)")

    # Sale Information
    for_sale = models.BooleanField(default=False, help_text="Is this property available for sale?")
    sale_price = models.DecimalField(max_digits=12, decimal_places=2, null=True, blank=True, help_text="Asking price for sale")
    is_negotiable = models.BooleanField(default=True, help_text="Is the price negotiable?")
    sale_description = models.TextField(blank=True, help_text="Additional description for sale listing")
    contact_phone = models.CharField(max_length=20, blank=True, help_text="Contact phone for inquiries")
    contact_email = models.EmailField(blank=True, help_text="Contact email for inquiries")

    # Additional Notes
    notes = models.TextField(blank=True, help_text="Additional notes or comments")
    tags = models.CharField(max_length=500, blank=True, help_text="Comma-separated tags for easy searching")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Property'
        verbose_name_plural = 'Properties'

    def __str__(self):
        return f"{self.name} ({self.get_property_type_display()})"

    def get_depreciation(self):
        """Calculate depreciation if both purchase and current values are available"""
        if self.purchase_price and self.current_value:
            return self.purchase_price - self.current_value
        return None

    def get_appreciation(self):
        """Calculate appreciation if current value is higher than purchase price"""
        depreciation = self.get_depreciation()
        if depreciation is not None and depreciation < 0:
            return abs(depreciation)
        return None

    def get_value_change_percentage(self):
        """Calculate percentage change in value"""
        if self.purchase_price and self.current_value and self.purchase_price > 0:
            change = ((self.current_value - self.purchase_price) / self.purchase_price) * 100
            return round(change, 2)
        return None

    def is_warranty_valid(self):
        """Check if warranty is still valid"""
        if self.warranty_expiry:
            from django.utils import timezone
            return self.warranty_expiry > timezone.now().date()
        return False

    def is_insurance_valid(self):
        """Check if insurance is still valid"""
        if self.insured and self.insurance_expiry:
            from django.utils import timezone
            return self.insurance_expiry > timezone.now().date()
        return False

    def get_tags_list(self):
        """Return tags as a list"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []


# -------------------------------
# Two-Factor Authentication Models
# -------------------------------

class TwoFactorAuth(models.Model):
    """
    Model for handling two-factor authentication codes
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='two_factor_codes')
    code = models.CharField(max_length=6, help_text="6-digit verification code")
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(help_text="When this code expires")
    is_used = models.BooleanField(default=False)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Two Factor Authentication'
        verbose_name_plural = 'Two Factor Authentications'

    def __str__(self):
        return f"{self.user.username} - {self.code} ({'Used' if self.is_used else 'Active'})"

    def is_expired(self):
        """Check if the code has expired"""
        from django.utils import timezone
        return timezone.now() > self.expires_at

    def is_valid(self):
        """Check if the code is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired()


class TrustedDevice(models.Model):
    """
    Model for tracking trusted devices to avoid repeated 2FA
    """
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='trusted_devices')
    device_token = models.CharField(max_length=255, unique=True, help_text="Unique token for this device")
    device_name = models.CharField(max_length=255, help_text="Human-readable device name")
    ip_address = models.GenericIPAddressField()
    user_agent = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    last_used = models.DateTimeField(auto_now=True)
    expires_at = models.DateTimeField(help_text="When this trust expires (7 days)")
    is_active = models.BooleanField(default=True)

    class Meta:
        ordering = ['-last_used']
        verbose_name = 'Trusted Device'
        verbose_name_plural = 'Trusted Devices'

    def __str__(self):
        return f"{self.user.username} - {self.device_name}"

    def is_expired(self):
        """Check if the device trust has expired"""
        from django.utils import timezone
        return timezone.now() > self.expires_at

    def is_valid(self):
        """Check if the device is still trusted"""
        return self.is_active and not self.is_expired()


# -------------------------------
# Contact Support Model
# -------------------------------

class SupportTicket(models.Model):
    """
    Model for handling user support requests
    """
    PRIORITY_CHOICES = [
        ('low', 'Low'),
        ('medium', 'Medium'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ]

    STATUS_CHOICES = [
        ('open', 'Open'),
        ('in_progress', 'In Progress'),
        ('resolved', 'Resolved'),
        ('closed', 'Closed'),
    ]

    CATEGORY_CHOICES = [
        ('technical', 'Technical Issue'),
        ('account', 'Account Issue'),
        ('billing', 'Billing Issue'),
        ('feature', 'Feature Request'),
        ('bug', 'Bug Report'),
        ('other', 'Other'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='support_tickets', null=True, blank=True)
    email = models.EmailField(help_text="Contact email")
    name = models.CharField(max_length=255, help_text="Contact name")
    subject = models.CharField(max_length=255, help_text="Ticket subject")
    message = models.TextField(help_text="Detailed message")
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, default='other')
    priority = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='medium')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='open')

    # Admin fields
    assigned_to = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='assigned_tickets')
    admin_notes = models.TextField(blank=True, help_text="Internal admin notes")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    resolved_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Support Ticket'
        verbose_name_plural = 'Support Tickets'

    def __str__(self):
        return f"#{self.id} - {self.subject} ({self.get_status_display()})"


# -------------------------------
# Store Sharing Model
# -------------------------------

class StoreShare(models.Model):
    """
    Model for managing store sharing via URLs and email
    """
    store = models.ForeignKey(Store, on_delete=models.CASCADE, related_name='shares')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='created_shares')

    # Sharing Details
    share_token = models.CharField(max_length=64, unique=True, help_text="Unique token for accessing shared store")
    title = models.CharField(max_length=255, blank=True, help_text="Custom title for the shared store")
    description = models.TextField(blank=True, help_text="Custom description for the shared store")

    # Access Control
    is_active = models.BooleanField(default=True, help_text="Whether this share link is active")
    expires_at = models.DateTimeField(null=True, blank=True, help_text="When this share link expires")
    password_protected = models.BooleanField(default=False, help_text="Whether access requires a password")
    access_password = models.CharField(max_length=128, blank=True, help_text="Password for accessing the shared store")

    # Analytics
    view_count = models.PositiveIntegerField(default=0, help_text="Number of times this share has been viewed")
    last_accessed = models.DateTimeField(null=True, blank=True, help_text="Last time this share was accessed")

    # Email Sharing
    shared_via_email = models.BooleanField(default=False, help_text="Whether this was shared via email")
    recipient_emails = models.JSONField(default=list, blank=True, help_text="List of email addresses this was shared with")

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Store Share'
        verbose_name_plural = 'Store Shares'

    def __str__(self):
        return f"Share: {self.store.name} by {self.created_by.username}"

    def is_expired(self):
        """Check if the share link has expired"""
        if self.expires_at:
            from django.utils import timezone
            return timezone.now() > self.expires_at
        return False

    def is_accessible(self):
        """Check if the share is currently accessible"""
        return self.is_active and not self.is_expired()

    def increment_view_count(self):
        """Increment view count and update last accessed time"""
        from django.utils import timezone
        self.view_count += 1
        self.last_accessed = timezone.now()
        self.save(update_fields=['view_count', 'last_accessed'])

    def get_share_url(self, request=None):
        """Generate the full share URL"""
        from django.urls import reverse
        path = reverse('shared_store_view', kwargs={'token': self.share_token})
        if request:
            return request.build_absolute_uri(path)
        return f"http://localhost:8000{path}"  # Fallback for non-request contexts


# -------------------------------
# Goods Activity Model
# -------------------------------

class GoodsActivity(models.Model):
    """
    Model for tracking all activities related to goods/products
    """
    ACTIVITY_TYPES = [
        ('created', 'Product Created'),
        ('updated', 'Product Updated'),
        ('stock_added', 'Stock Added'),
        ('stock_reduced', 'Stock Reduced'),
        ('sale_recorded', 'Sale Recorded'),
        ('price_updated', 'Price Updated'),
        ('status_changed', 'Status Changed'),
        ('category_changed', 'Category Changed'),
        ('store_changed', 'Store Changed'),
        ('image_added', 'Image Added'),
        ('image_removed', 'Image Removed'),
        ('description_updated', 'Description Updated'),
        ('inventory_adjustment', 'Inventory Adjustment'),
        ('viewed', 'Product Viewed'),
        ('shared', 'Product Shared'),
    ]

    goods = models.ForeignKey(Goods, on_delete=models.CASCADE, related_name='activities')
    activity_type = models.CharField(max_length=50, choices=ACTIVITY_TYPES)
    title = models.CharField(max_length=200)
    description = models.TextField()
    old_value = models.TextField(blank=True, null=True)  # Store old value for comparison
    new_value = models.TextField(blank=True, null=True)  # Store new value
    quantity_change = models.IntegerField(default=0)  # For stock changes
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    metadata = models.JSONField(default=dict, blank=True)  # Additional data

    def __str__(self):
        return f"{self.goods.name} - {self.title}"

    @property
    def icon(self):
        """Get FontAwesome icon for activity type"""
        icon_map = {
            'created': 'plus-circle',
            'updated': 'edit',
            'stock_added': 'arrow-up',
            'stock_reduced': 'arrow-down',
            'sale_recorded': 'shopping-cart',
            'price_updated': 'dollar-sign',
            'status_changed': 'toggle-on',
            'category_changed': 'folder',
            'store_changed': 'store',
            'image_added': 'image',
            'image_removed': 'trash',
            'description_updated': 'file-text',
            'inventory_adjustment': 'settings',
            'viewed': 'eye',
            'shared': 'share',
        }
        return icon_map.get(self.activity_type, 'info')

    @property
    def color_class(self):
        """Get Bootstrap color class for activity type"""
        color_map = {
            'created': 'success',
            'updated': 'info',
            'stock_added': 'success',
            'stock_reduced': 'warning',
            'sale_recorded': 'primary',
            'price_updated': 'warning',
            'status_changed': 'info',
            'category_changed': 'secondary',
            'store_changed': 'secondary',
            'image_added': 'info',
            'image_removed': 'danger',
            'description_updated': 'info',
            'inventory_adjustment': 'warning',
            'viewed': 'light',
            'shared': 'info',
        }
        return color_map.get(self.activity_type, 'secondary')

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Goods Activity'
        verbose_name_plural = 'Goods Activities'

    @classmethod
    def log_activity(cls, goods, activity_type, title, description, user, **kwargs):
        """
        Helper method to log activities
        """
        return cls.objects.create(
            goods=goods,
            activity_type=activity_type,
            title=title,
            description=description,
            created_by=user,
            old_value=kwargs.get('old_value'),
            new_value=kwargs.get('new_value'),
            quantity_change=kwargs.get('quantity_change', 0),
            metadata=kwargs.get('metadata', {})
        )