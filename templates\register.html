<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Inventory Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .nav-link {
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .nav-link:hover {
            color: var(--primary-color) !important;
        }

        .register-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            margin-top: 50px;
        }

        .register-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 600px;
            animation: slideUp 0.6s ease-out;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-header h1 {
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .register-header p {
            color: #666;
            margin: 0;
        }

        .form-control {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .input-group {
            position: relative;
            margin-bottom: 20px;
        }

        .input-group-text {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            z-index: 5;
        }

        .form-control.with-icon {
            padding-left: 50px;
        }

        .form-select.with-icon {
            padding-left: 50px;
        }

        .btn-register {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            padding: 15px;
            font-weight: bold;
            font-size: 16px;
            width: 100%;
            color: white;
            transition: all 0.3s ease;
        }

        .btn-register:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
            color: white;
        }

        .btn-register:disabled {
            opacity: 0.7;
            transform: none;
            box-shadow: none;
        }

        .divider {
            text-align: center;
            margin: 30px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e9ecef;
        }

        .divider span {
            background: rgba(255, 255, 255, 0.95);
            padding: 0 20px;
            color: #666;
        }

        .auth-links {
            text-align: center;
        }

        .auth-links a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .auth-links a:hover {
            color: #764ba2;
        }

        .alert {
            border-radius: 12px;
            border: none;
            margin-bottom: 20px;
        }

        .loading-spinner {
            display: none;
        }

        .password-toggle {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #666;
            cursor: pointer;
            z-index: 5;
        }

        .password-strength {
            font-size: 12px;
            margin-top: -15px;
            margin-bottom: 15px;
        }

        .strength-weak { color: #dc3545; }
        .strength-medium { color: #ffc107; }
        .strength-strong { color: #198754; }

        .form-row {
            display: flex;
            gap: 15px;
        }

        .form-row .input-group {
            flex: 1;
        }

        .form-select {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 15px 20px;
            font-size: 16px;
            transition: all 0.3s ease;
            margin-bottom: 20px;
            background-color: white;
        }

        .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .form-select:disabled {
            background-color: #f8f9fa;
            opacity: 0.6;
        }

        @media (max-width: 576px) {
            .register-card {
                padding: 30px 20px;
                margin: 10px;
            }
            
            .form-row {
                flex-direction: column;
                gap: 0;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="/">
                <i class="fas fa-boxes"></i> InventoryPro
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/">Home</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/public/goods/">Browse Goods</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/login/">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link btn btn-outline-primary px-3 ms-2" href="/register/">Sign Up</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="register-container">
        <div class="register-card">
            <div class="register-header">
                <h1><i class="fas fa-boxes text-primary"></i> Inventory Pro</h1>
                <p>Create your account</p>
            </div>

            <div id="alertContainer"></div>

            <form id="registerForm" method="POST" action="/register/">
                {% csrf_token %}
                
                <!-- Name Fields -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control with-icon" id="first_name" name="first_name" 
                               placeholder="First Name" required>
                    </div>

                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-user"></i>
                        </span>
                        <input type="text" class="form-control with-icon" id="last_name" name="last_name" 
                               placeholder="Last Name" required>
                    </div>
                </div>

                <!-- Username -->
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-user-circle"></i>
                    </span>
                    <input type="text" class="form-control with-icon" id="username" name="username" 
                           placeholder="Username" required>
                </div>

                <!-- Email -->
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-envelope"></i>
                    </span>
                    <input type="email" class="form-control with-icon" id="email" name="email" 
                           placeholder="Email Address" required>
                </div>

                <!-- Country and State Row -->
                <div class="form-row">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-globe"></i>
                        </span>
                        <select id="country" name="country" class="form-select with-icon" required>
                            <option value="">Select Country</option>
                        </select>
                    </div>

                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-map-marker-alt"></i>
                        </span>
                        <select id="state" name="state" class="form-select with-icon">
                            <option value="">Select State</option>
                        </select>
                    </div>
                </div>  

                <!-- Password Fields -->
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" class="form-control with-icon" id="password1" name="password1" 
                           placeholder="Password" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('password1')">
                        <i class="fas fa-eye" id="password1ToggleIcon"></i>
                    </button>
                </div>
                <div id="passwordStrength" class="password-strength"></div>

                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-lock"></i>
                    </span>
                    <input type="password" class="form-control with-icon" id="password2" name="password2" 
                           placeholder="Confirm Password" required>
                    <button type="button" class="password-toggle" onclick="togglePassword('password2')">
                        <i class="fas fa-eye" id="password2ToggleIcon"></i>
                    </button>
                </div>

                <button type="submit" class="btn btn-register" id="registerBtn">
                    <span class="register-text">Create Account</span>
                    <span class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i> Creating Account...
                    </span>
                </button>
            </form>

            <div class="divider">
                <span>or</span>
            </div>

            <div class="auth-links">
                <p class="mb-0">Already have an account? <a href="/login/">Sign in here</a></p>
            </div>
        </div>
    </div>

<footer class="bg-dark text-light py-5">
    <div class="container">
        <div class="row">
            <!-- Company Info Section -->
            <div class="col-lg-4 mb-4">
                <h5 class="fw-bold mb-3 text-white">Company House</h5>
                <p class="text-light mb-3">
                    We are a registered IT company in the UK with company number <span class="text-warning">********</span>
                </p>
            </div>

            <!-- Contact Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Contact</h6>
                <div class="contact-info">
                    <p class="text-light mb-2">
                        <i class="fas fa-map-marker-alt me-2 text-primary"></i>
                        <strong>Address:</strong> 13L Queensway, Ponders End, Enfield, London EN3 4SA
                    </p>
                    <p class="text-light mb-2">
                        <i class="fas fa-envelope me-2 text-primary"></i>
                        <strong>Email:</strong> <EMAIL>
                    </p>
                    <p class="text-light mb-0">
                        <i class="fas fa-phone me-2 text-primary"></i>
                        <strong>Tel:</strong> 07500503952
                    </p>
                </div>
            </div>

            <!-- Subscribe Section -->
            <div class="col-lg-4 mb-4">
                <h6 class="fw-bold mb-3 text-white">Subscribe Now</h6>
                <p class="text-light mb-3">
                    Don't miss our future updates! Get Subscribed Today!
                </p>
                <div class="mb-3">
            <div class="col-md-6 text-md-start">
                <a href="/register/" class="btn btn-primary">
                    <i class="fas fa-rocket me-2"></i>Register
                </a>
            </div>
                </div>
                <!-- Social Icons -->
                <div class="social-icons">
                    <a href="https://www.facebook.com/amatip.info.tech" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <a href="https://x.com/amatipIT" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <a href="https://www.tiktok.com/@amatip_it?is_from_webapp=1&sender_device=pc" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-tiktok"></i>
                    </a>
                    <a href="https://youtube.com/@amatip_it?si=eQ54UaVIM-DgLOAr" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <a href="https://www.instagram.com/amatip_it/profilecard/?igsh=MWRzbGV5b3h1MTQ2Yw==" class="btn btn-outline-light btn-sm rounded-circle me-2" style="width: 40px; height: 40px;" target="_blank">
                        <i class="fab fa-instagram"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <div class="row mt-4">
            <div class="col-md-6">
                <div class="d-flex flex-wrap gap-4">
                    <a href="#features" class="text-muted text-decoration-none">Features</a>
                    <a href="/public/goods/" class="text-muted text-decoration-none">Browse Goods</a>
                    <a href="/login/" class="text-muted text-decoration-none">Login</a>
                    <a href="/register/" class="text-muted text-decoration-none">Sign Up</a>
                </div>
            </div>
        </div>

        <hr class="my-4 border-secondary">
        
        <!-- Copyright -->
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="mb-0 text-muted">&copy; 2023 Amatip. All rights reserved.</p>
            </div>
            <div class="col-md-6 text-md-end">
                <small class="text-muted">Built with Django & Bootstrap</small>
            </div>
        </div>
    </div>
</footer>

    </footer>

<script>
        // Countries and States Data - Only countries with states/provinces
        const countriesAndStates = {
            "United States": ["Alabama", "Alaska", "Arizona", "Arkansas", "California", "Colorado", "Connecticut", "Delaware", "Florida", "Georgia", "Hawaii", "Idaho", "Illinois", "Indiana", "Iowa", "Kansas", "Kentucky", "Louisiana", "Maine", "Maryland", "Massachusetts", "Michigan", "Minnesota", "Mississippi", "Missouri", "Montana", "Nebraska", "Nevada", "New Hampshire", "New Jersey", "New Mexico", "New York", "North Carolina", "North Dakota", "Ohio", "Oklahoma", "Oregon", "Pennsylvania", "Rhode Island", "South Carolina", "South Dakota", "Tennessee", "Texas", "Utah", "Vermont", "Virginia", "Washington", "West Virginia", "Wisconsin", "Wyoming"],
            "Canada": ["Alberta", "British Columbia", "Manitoba", "New Brunswick", "Newfoundland and Labrador", "Northwest Territories", "Nova Scotia", "Nunavut", "Ontario", "Prince Edward Island", "Quebec", "Saskatchewan", "Yukon"],
            "United Kingdom": ["England", "Scotland", "Wales", "Northern Ireland"],
            "Australia": ["Australian Capital Territory", "New South Wales", "Northern Territory", "Queensland", "South Australia", "Tasmania", "Victoria", "Western Australia"],
            "Germany": ["Baden-Württemberg", "Bavaria", "Berlin", "Brandenburg", "Bremen", "Hamburg", "Hesse", "Lower Saxony", "Mecklenburg-Vorpommern", "North Rhine-Westphalia", "Rhineland-Palatinate", "Saarland", "Saxony", "Saxony-Anhalt", "Schleswig-Holstein", "Thuringia"],
            "India": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal"],
            "Nigeria": ["Abia", "Adamawa", "Akwa Ibom", "Anambra", "Bauchi", "Bayelsa", "Benue", "Borno", "Cross River", "Delta", "Ebonyi", "Edo", "Ekiti", "Enugu", "Gombe", "Imo", "Jigawa", "Kaduna", "Kano", "Katsina", "Kebbi", "Kogi", "Kwara", "Lagos", "Nasarawa", "Niger", "Ogun", "Ondo", "Osun", "Oyo", "Plateau", "Rivers", "Sokoto", "Taraba", "Yobe", "Zamfara", "FCT"],
            "South Africa": ["Eastern Cape", "Free State", "Gauteng", "KwaZulu-Natal", "Limpopo", "Mpumalanga", "Northern Cape", "North West", "Western Cape"],
            "Brazil": ["Acre", "Alagoas", "Amapá", "Amazonas", "Bahia", "Ceará", "Distrito Federal", "Espírito Santo", "Goiás", "Maranhão", "Mato Grosso", "Mato Grosso do Sul", "Minas Gerais", "Pará", "Paraíba", "Paraná", "Pernambuco", "Piauí", "Rio de Janeiro", "Rio Grande do Norte", "Rio Grande do Sul", "Rondônia", "Roraima", "Santa Catarina", "São Paulo", "Sergipe", "Tocantins"],
            "Mexico": ["Aguascalientes", "Baja California", "Baja California Sur", "Campeche", "Chiapas", "Chihuahua", "Coahuila", "Colima", "Durango", "Guanajuato", "Guerrero", "Hidalgo", "Jalisco", "México", "Michoacán", "Morelos", "Nayarit", "Nuevo León", "Oaxaca", "Puebla", "Querétaro", "Quintana Roo", "San Luis Potosí", "Sinaloa", "Sonora", "Tabasco", "Tamaulipas", "Tlaxcala", "Veracruz", "Yucatán", "Zacatecas"],
            "China": ["Anhui", "Beijing", "Chongqing", "Fujian", "Gansu", "Guangdong", "Guangxi", "Guizhou", "Hainan", "Hebei", "Heilongjiang", "Henan", "Hubei", "Hunan", "Inner Mongolia", "Jiangsu", "Jiangxi", "Jilin", "Liaoning", "Ningxia", "Qinghai", "Shaanxi", "Shandong", "Shanghai", "Shanxi", "Sichuan", "Tianjin", "Tibet", "Xinjiang", "Yunnan", "Zhejiang"],
            "France": ["Auvergne-Rhône-Alpes", "Bourgogne-Franche-Comté", "Brittany", "Centre-Val de Loire", "Corsica", "Grand Est", "Hauts-de-France", "Île-de-France", "Normandy", "Nouvelle-Aquitaine", "Occitanie", "Pays de la Loire", "Provence-Alpes-Côte d'Azur"],
            "Italy": ["Abruzzo", "Basilicata", "Calabria", "Campania", "Emilia-Romagna", "Friuli-Venezia Giulia", "Lazio", "Liguria", "Lombardy", "Marche", "Molise", "Piedmont", "Apulia", "Sardinia", "Sicily", "Tuscany", "Trentino-Alto Adige", "Umbria", "Aosta Valley", "Veneto"],
            "Japan": ["Aichi", "Akita", "Aomori", "Chiba", "Ehime", "Fukui", "Fukuoka", "Fukushima", "Gifu", "Gunma", "Hiroshima", "Hokkaido", "Hyogo", "Ibaraki", "Ishikawa", "Iwate", "Kagawa", "Kagoshima", "Kanagawa", "Kochi", "Kumamoto", "Kyoto", "Mie", "Miyagi", "Miyazaki", "Nagano", "Nagasaki", "Nara", "Niigata", "Oita", "Okayama", "Okinawa", "Osaka", "Saga", "Saitama", "Shiga", "Shimane", "Shizuoka", "Tochigi", "Tokushima", "Tokyo", "Tottori", "Toyama", "Wakayama", "Yamagata", "Yamaguchi", "Yamanashi"],
            "Spain": ["Andalusia", "Aragon", "Asturias", "Balearic Islands", "Basque Country", "Canary Islands", "Cantabria", "Castile and León", "Castile-La Mancha", "Catalonia", "Extremadura", "Galicia", "La Rioja", "Madrid", "Murcia", "Navarre", "Valencia"],
            "Russia": ["Adygea", "Altai Krai", "Altai Republic", "Amur Oblast", "Arkhangelsk Oblast", "Astrakhan Oblast", "Bashkortostan", "Belgorod Oblast", "Bryansk Oblast", "Buryatia", "Chelyabinsk Oblast", "Chechnya", "Chukotka", "Chuvashia", "Dagestan", "Ingushetia", "Irkutsk Oblast", "Ivanovo Oblast", "Jewish Autonomous Oblast", "Kabardino-Balkaria", "Kaliningrad Oblast", "Kalmykia", "Kaluga Oblast", "Kamchatka Krai", "Karachay-Cherkessia", "Karelia", "Kemerovo Oblast", "Khabarovsk Krai", "Khakassia", "Khanty-Mansi", "Kirov Oblast", "Komi", "Kostroma Oblast", "Krasnodar Krai", "Krasnoyarsk Krai", "Kurgan Oblast", "Kursk Oblast", "Leningrad Oblast", "Lipetsk Oblast", "Magadan Oblast", "Mari El", "Mordovia", "Moscow", "Moscow Oblast", "Murmansk Oblast", "Nenets", "Nizhny Novgorod Oblast", "North Ossetia", "Novgorod Oblast", "Novosibirsk Oblast", "Omsk Oblast", "Orenburg Oblast", "Oryol Oblast", "Penza Oblast", "Perm Krai", "Primorsky Krai", "Pskov Oblast", "Rostov Oblast", "Ryazan Oblast", "Sakha", "Sakhalin Oblast", "Samara Oblast", "Saint Petersburg", "Saratov Oblast", "Smolensk Oblast", "Stavropol Krai", "Sverdlovsk Oblast", "Tambov Oblast", "Tatarstan", "Tomsk Oblast", "Tula Oblast", "Tuva", "Tver Oblast", "Tyumen Oblast", "Udmurtia", "Ulyanovsk Oblast", "Vladimir Oblast", "Volgograd Oblast", "Vologda Oblast", "Voronezh Oblast", "Yamalo-Nenets", "Yaroslavl Oblast", "Zabaykalsky Krai"]
        };

        // Populate countries dropdown - only countries with states
        function populateCountries() {
            const countrySelect = document.getElementById('country');
            
            // Get all countries that have states/provinces
            const countriesWithStates = Object.keys(countriesAndStates).sort();
            
            countriesWithStates.forEach(country => {
                const option = document.createElement('option');
                option.value = country;
                option.textContent = country;
                countrySelect.appendChild(option);
            });
        }

        // Populate states dropdown based on selected country
        function populateStates(selectedCountry) {
            const stateSelect = document.getElementById('state');
            console.log('Populating states for:', selectedCountry); // Debug log
            
            if (!stateSelect) {
                console.error('State select element not found');
                return;
            }
            
            // Clear existing options
            stateSelect.innerHTML = '<option value="">Select State/Province</option>';
            
            if (selectedCountry && countriesAndStates[selectedCountry]) {
                console.log('States found:', countriesAndStates[selectedCountry]); // Debug log
                // Enable state dropdown and populate with states
                stateSelect.disabled = false;
                stateSelect.required = true;
                
                countriesAndStates[selectedCountry].forEach(state => {
                    const option = document.createElement('option');
                    option.value = state;
                    option.textContent = state;
                    stateSelect.appendChild(option);
                });
            } else {
                console.log('No states found for:', selectedCountry); // Debug log
                // This shouldn't happen since we only have countries with states
                stateSelect.disabled = true;
                stateSelect.required = false;
                const option = document.createElement('option');
                option.value = "";
                option.textContent = "No states available";
                stateSelect.appendChild(option);
            }
        }

        // Enhanced UI Functions
        function showAlert(message, type = 'info') {
            const alertContainer = document.getElementById('alertContainer');
            const alertId = 'alert-' + Date.now();
            
            const alertHTML = `
                <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                    <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            alertContainer.insertAdjacentHTML('beforeend', alertHTML);
            
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }

        function getAlertIcon(type) {
            const icons = {
                'success': 'check-circle',
                'danger': 'exclamation-triangle',
                'warning': 'exclamation-circle',
                'info': 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        function togglePassword(inputId) {
            const passwordInput = document.getElementById(inputId);
            const toggleIcon = document.getElementById(inputId + 'ToggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.replace('fa-eye', 'fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.replace('fa-eye-slash', 'fa-eye');
            }
        }

        function checkPasswordStrength(password) {
            const strengthIndicator = document.getElementById('passwordStrength');
            let strength = 0;
            let feedback = '';

            if (password.length >= 8) strength++;
            if (/[a-z]/.test(password)) strength++;
            if (/[A-Z]/.test(password)) strength++;
            if (/[0-9]/.test(password)) strength++;
            if (/[^A-Za-z0-9]/.test(password)) strength++;

            switch (strength) {
                case 0:
                case 1:
                case 2:
                    feedback = '<i class="fas fa-exclamation-triangle strength-weak"></i> Weak password';
                    strengthIndicator.className = 'password-strength strength-weak';
                    break;
                case 3:
                case 4:
                    feedback = '<i class="fas fa-check-circle strength-medium"></i> Medium password';
                    strengthIndicator.className = 'password-strength strength-medium';
                    break;
                case 5:
                    feedback = '<i class="fas fa-shield-alt strength-strong"></i> Strong password';
                    strengthIndicator.className = 'password-strength strength-strong';
                    break;
            }

            strengthIndicator.innerHTML = password.length > 0 ? feedback : '';
        }

        function setLoading(isLoading) {
            const registerBtn = document.getElementById('registerBtn');
            const registerText = registerBtn.querySelector('.register-text');
            const loadingSpinner = registerBtn.querySelector('.loading-spinner');
            
            if (isLoading) {
                registerBtn.disabled = true;
                registerText.style.display = 'none';
                loadingSpinner.style.display = 'inline';
            } else {
                registerBtn.disabled = false;
                registerText.style.display = 'inline';
                loadingSpinner.style.display = 'none';
            }
        }

        // Form submission with country and state
        document.getElementById("registerForm").addEventListener("submit", function(e) {
            e.preventDefault();
            setLoading(true);

            let formData = new FormData(this);
            let registerData = {
                first_name: formData.get("first_name"),
                last_name: formData.get("last_name"),
                username: formData.get("username"),
                email: formData.get("email"),
                password1: formData.get("password1"),
                password2: formData.get("password2"),
                country: formData.get("country"),
                state: formData.get("state") || ""
            };

            // Validate required fields
            if (!registerData.country.trim()) {
                setLoading(false);
                showAlert('Please select your country.', 'warning');
                return;
            }

            // State is now required for all countries since we only have countries with states
            if (!registerData.state.trim()) {
                setLoading(false);
                showAlert('Please select your state/province.', 'warning');
                return;
            }

            // Make POST request to the register endpoint
            fetch('/register/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name="csrfmiddlewaretoken"]').value,
                },
                body: JSON.stringify(registerData),
            })
            .then(response => response.json())
            .then(data => {
                setLoading(false);
                if (data.requires_verification) {
                    // Show email verification message
                    showAlert(data.message, 'success');

                    // Check if there was an email error
                    if (data.email_error) {
                        showAlert('There was an issue sending the verification email. Please contact support if you don\'t receive it.', 'warning');
                    }

                    // Redirect to email verification page
                    setTimeout(() => {
                        window.location.href = `/verify-email/?user_id=${data.user_id}&email=${encodeURIComponent(data.email)}`;
                    }, 3000);
                } else if (data.message && data.message.includes("successful")) {
                    showAlert('Registration successful! Redirecting to dashboard...', 'success');
                    setTimeout(() => {
                        window.location.href = '/dashboard/';
                    }, 2000);
                } else {
                    // Handle form errors
                    let errorMessage = 'Registration failed: ';
                    if (data.errors) {
                        // Format Django form errors
                        for (let field in data.errors) {
                            errorMessage += `${field}: ${data.errors[field].join(', ')}. `;
                        }
                    } else {
                        errorMessage += 'Unknown error occurred.';
                    }
                    showAlert(errorMessage, 'danger');
                }
            })
            .catch(error => {
                setLoading(false);
                console.error('Error:', error);
                showAlert('There was an error during registration. Please try again.', 'danger');
            });
        });

        // Initialize page functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Populate countries dropdown
            populateCountries();
            
            // Handle country selection change
            const countrySelect = document.getElementById('country');
            if (countrySelect) {
                countrySelect.addEventListener('change', function() {
                    console.log('Country changed to:', this.value); // Debug log
                    populateStates(this.value);
                });
            }

            // Auto-focus on first name field
            const firstNameInput = document.getElementById('first_name');
            if (firstNameInput) {
                firstNameInput.focus();
            }
        });

        // Password strength checker
        document.getElementById('password1').addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    </script>
</body>
</html>