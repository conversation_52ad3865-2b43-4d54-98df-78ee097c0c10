{% extends 'base.html' %}

{% block title %}User Profile - Inventory Management System{% endblock %}

{% block extra_css %}
<style>
    .profile-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        padding: 2rem;
        margin-bottom: 2rem;
        position: relative;
        overflow: hidden;
    }
    
    .profile-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
    }
    
    .profile-content {
        position: relative;
        z-index: 2;
    }
    
    .profile-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        border: 4px solid rgba(255, 255, 255, 0.3);
        object-fit: cover;
        margin-bottom: 1rem;
    }
    
    .profile-avatar-placeholder {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 1rem;
    }
    
    .profile-info-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        margin-bottom: 1.5rem;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid #f8f9fa;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #3498db, #5dade2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-right: 1rem;
        flex-shrink: 0;
    }
    
    .info-content {
        flex: 1;
    }
    
    .info-label {
        font-weight: 600;
        color: #2c3e50;
        font-size: 0.9rem;
        margin-bottom: 0.25rem;
    }
    
    .info-value {
        color: #7f8c8d;
        font-size: 0.95rem;
    }
    
    .edit-profile-btn {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        border: none;
        border-radius: 25px;
        padding: 12px 30px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .edit-profile-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        color: white;
    }
    
    .avatar-upload {
        position: relative;
        display: inline-block;
    }
    
    .avatar-upload-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
        cursor: pointer;
    }
    
    .avatar-upload:hover .avatar-upload-overlay {
        opacity: 1;
    }
    
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .stats-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Profile Header -->
    <div class="profile-header">
        <div class="profile-content">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <div class="d-flex align-items-center">
                        <div class="avatar-upload me-4">
                            <img id="profileAvatar" class="profile-avatar" src="" alt="Profile Avatar" style="display: none;">
                            <div id="avatarPlaceholder" class="profile-avatar-placeholder">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="avatar-upload-overlay" onclick="document.getElementById('avatarInput').click()">
                                <i class="fas fa-camera"></i>
                            </div>
                        </div>
                        <div>
                            <h2 class="mb-2" id="profileUsername">Loading...</h2>
                            <p class="mb-0 opacity-75" id="profileBio">No bio available</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn edit-profile-btn" data-bs-toggle="modal" data-bs-target="#editProfileModal">
                        <i class="fas fa-edit me-2"></i>Edit Profile
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-user-circle me-2"></i>Profile Information</h5>
                </div>
                <div class="card-body">
                    <div id="profileInfo">
                        <!-- Profile info will be loaded here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="col-lg-4">
            <div class="stats-card">
                <div class="stats-number" id="memberSince">--</div>
                <div class="stats-label">Member Since</div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="changePassword()">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteProfile()">
                            <i class="fas fa-trash me-2"></i>Delete Profile
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Profile Modal -->
<div class="modal fade" id="editProfileModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Profile</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="profileForm" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phoneNumber" name="phone_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Secondary Email</label>
                                <input type="email" class="form-control" id="secondaryEmail" name="secondary_email">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Date of Birth</label>
                                <input type="date" class="form-control" id="dateOfBirth" name="date_of_birth">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Website URL</label>
                                <input type="url" class="form-control" id="websiteUrl" name="website_url">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Company Name</label>
                        <input type="text" class="form-control" id="companyName" name="company_name">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Bio</label>
                        <textarea class="form-control" id="bio" name="bio" rows="3" placeholder="Tell us about yourself..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Profile Avatar</label>
                        <input type="file" class="form-control" id="avatarInput" name="avatar" accept="image/*">
                        <div class="form-text">Upload a profile picture (JPG, PNG, GIF)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>


<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-key me-2"></i>Change Password</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="changePasswordForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="currentPassword" name="current_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">New Password</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password" required minlength="8">
                        <div class="form-text">Password must be at least 8 characters long</div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirm_password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Change Password</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let profileData = null;

    // Load profile data on page load
    document.addEventListener('DOMContentLoaded', function() {
        loadProfile();
    });

    async function loadProfile() {
        try {
            const data = await apiRequest('/profile/');
            profileData = data;
            displayProfile(data);
        } catch (error) {
            // Profile doesn't exist, show create form
            showCreateProfileForm();
        }
    }

    function displayProfile(data) {
        // Update header
        document.getElementById('profileUsername').textContent = currentUser?.username || 'User';
        document.getElementById('profileBio').textContent = data.bio || 'No bio available';
        
        // Update avatar
        if (data.avatar_url) {
            document.getElementById('profileAvatar').src = data.avatar_url;
            document.getElementById('profileAvatar').style.display = 'block';
            document.getElementById('avatarPlaceholder').style.display = 'none';
        }

        // Update member since
        if (data.created_at) {
            const memberSince = new Date(data.created_at).getFullYear();
            document.getElementById('memberSince').textContent = memberSince;
        }

        // Update profile info
        const profileInfo = document.getElementById('profileInfo');
        profileInfo.innerHTML = generateProfileInfoHTML(data);

        // Populate form
        populateForm(data);
    }

    function generateProfileInfoHTML(data) {
        const items = [
            { icon: 'phone', label: 'Phone', value: data.phone_number || 'Not provided' },
            { icon: 'envelope', label: 'Secondary Email', value: data.secondary_email || 'Not provided' },
            { icon: 'birthday-cake', label: 'Date of Birth', value: data.date_of_birth ? new Date(data.date_of_birth).toLocaleDateString() : 'Not provided' },
            { icon: 'globe', label: 'Website', value: data.website_url ? `<a href="${data.website_url}" target="_blank">${data.website_url}</a>` : 'Not provided' },
            { icon: 'building', label: 'Company', value: data.company_name || 'Not provided' },
        ];

        return items.map(item => `
            <div class="info-item">
                <div class="info-icon">
                    <i class="fas fa-${item.icon}"></i>
                </div>
                <div class="info-content">
                    <div class="info-label">${item.label}</div>
                    <div class="info-value">${item.value}</div>
                </div>
            </div>
        `).join('');
    }

    function populateForm(data) {
        document.getElementById('phoneNumber').value = data.phone_number || '';
        document.getElementById('secondaryEmail').value = data.secondary_email || '';
        document.getElementById('dateOfBirth').value = data.date_of_birth || '';
        document.getElementById('websiteUrl').value = data.website_url || '';
        document.getElementById('companyName').value = data.company_name || '';
        document.getElementById('bio').value = data.bio || '';
    }

    function showCreateProfileForm() {
        showAlert('Profile not found. Please create your profile.', 'info');
        // Show the modal automatically
        const modal = new bootstrap.Modal(document.getElementById('editProfileModal'));
        modal.show();
    }

    // Handle form submission
    document.getElementById('profileForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        
        try {
            const response = await fetch('/profile/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            const data = await response.json();

            if (response.ok) {
                showAlert(data.message, 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('editProfileModal'));
                modal.hide();
                
                // Reload profile data
                setTimeout(() => {
                    loadProfile();
                }, 1000);
            } else {
                showAlert(data.error || 'Failed to update profile', 'danger');
            }
        } catch (error) {
            showAlert('Error updating profile: ' + error.message, 'danger');
        }
    });

    // Handle avatar preview
    document.getElementById('avatarInput').addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                const img = document.getElementById('profileAvatar');
                const placeholder = document.getElementById('avatarPlaceholder');
                
                img.src = e.target.result;
                img.style.display = 'block';
                placeholder.style.display = 'none';
            };
            reader.readAsDataURL(file);
        }
    });

  // Change password function
    function changePassword() {
        const modal = new bootstrap.Modal(document.getElementById('changePasswordModal'));
        modal.show();
    }

    // Handle change password form submission
    document.getElementById('changePasswordForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const currentPassword = document.getElementById('currentPassword').value;
        const newPassword = document.getElementById('newPassword').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        
        // Validate passwords match
        if (newPassword !== confirmPassword) {
            showAlert('New passwords do not match', 'danger');
            return;
        }
        
        // Validate password strength (basic validation)
        if (newPassword.length < 8) {
            showAlert('Password must be at least 8 characters long', 'danger');
            return;
        }
        
        // Additional password validation
        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
            showAlert('Password must contain at least one uppercase letter, one lowercase letter, and one number', 'danger');
            return;
        }
        
        try {
            const response = await fetch('/auth/change-password/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    current_password: currentPassword,
                    new_password: newPassword,
                    confirm_password: confirmPassword
                })
            });

            const data = await response.json();

            if (response.ok) {
                showAlert(data.message || 'Password changed successfully', 'success');
                const modal = bootstrap.Modal.getInstance(document.getElementById('changePasswordModal'));
                modal.hide();
                
                // Clear the form
                document.getElementById('changePasswordForm').reset();
            } else {
                showAlert(data.error || 'Failed to change password', 'danger');
            }
        } catch (error) {
            showAlert('Error changing password: ' + error.message, 'danger');
        }
    });

    async function deleteProfile() {
        if (confirm('Are you sure you want to delete your profile? This action cannot be undone.')) {
            try {
                await apiRequest('/profile/', { method: 'DELETE' });
                showAlert('Profile deleted successfully', 'success');
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } catch (error) {
                // Error already handled by apiRequest
            }
        }
    }
</script>
{% endblock %}