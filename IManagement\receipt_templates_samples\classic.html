<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Professional Receipt Template</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.4;
      color: #2c3e50;
      background: #f8f9fa;
      padding: 10px;
      font-size: 13px;
    }

    .receipt-container {
      max-width: 580px;
      margin: 0 auto;
      background: #ffffff;
      border-radius: 6px;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      border: 1px solid #e1e8ed;
    }

    .receipt-header {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
      padding: 20px;
      text-align: center;
      position: relative;
    }

    .receipt-header::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    }

    .receipt-title {
      font-size: 1.8em;
      font-weight: 300;
      letter-spacing: 1px;
      margin-bottom: 5px;
      text-transform: uppercase;
    }

    .receipt-subtitle {
      font-size: 0.9em;
      opacity: 0.9;
      font-weight: 300;
    }

    .receipt-body {
      padding: 25px;
    }

    .company-info {
      text-align: center;
      margin-bottom: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 4px;
      border-left: 3px solid #3498db;
    }

    .company-name {
      font-size: 1.2em;
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .company-message {
      color: #7f8c8d;
      font-size: 0.9em;
      font-style: italic;
    }

    .receipt-details {
      margin-bottom: 20px;
    }

    .details-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 15px;
    }

    .detail-item {
      background: #ffffff;
      padding: 10px;
      border-radius: 4px;
      border: 1px solid #e1e8ed;
    }

    .detail-label {
      font-size: 0.8em;
      color: #7f8c8d;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      margin-bottom: 3px;
    }

    .detail-value {
      font-size: 1em;
      font-weight: 600;
      color: #2c3e50;
    }

    .items-table {
      width: 100%;
      border-collapse: separate;
      border-spacing: 0;
      margin-bottom: 15px;
      border-radius: 6px;
      overflow: hidden;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      font-size: 0.9em;
    }

    .items-table thead {
      background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
      color: white;
    }

    .items-table th {
      padding: 12px 10px;
      text-align: left;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-size: 0.85em;
    }

    .items-table tbody tr {
      border-bottom: 1px solid #e1e8ed;
      transition: background-color 0.2s ease;
    }

    .items-table tbody tr:hover {
      background-color: #f8f9fa;
    }

    .items-table tbody tr:last-child {
      border-bottom: none;
    }

    .items-table td {
      padding: 10px;
      vertical-align: middle;
    }

    .item-name {
      font-weight: 500;
      color: #2c3e50;
    }

    .item-quantity, .item-price {
      font-family: 'Courier New', monospace;
      text-align: right;
    }

    .totals-section {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 6px;
      border: 1px solid #e1e8ed;
      margin-bottom: 20px;
    }

    .total-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      border-bottom: 1px solid #dee2e6;
    }

    .total-row:last-child {
      border-bottom: none;
      font-size: 1.1em;
      font-weight: 700;
      color: #2c3e50;
      padding-top: 10px;
      border-top: 2px solid #3498db;
    }

    .total-label {
      font-weight: 500;
    }

    .total-value {
      font-family: 'Courier New', monospace;
      font-weight: 600;
    }

    .signatures-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      margin-top: 20px;
    }

    .signature-block {
      text-align: center;
      padding: 10px;
      background: #ffffff;
      border: 2px dashed #dee2e6;
      border-radius: 6px;
      height: 100px;
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
    }

    .signature-label {
      font-weight: 600;
      color: #34495e;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      font-size: 0.8em;
      flex-shrink: 0;
    }

    .signature-area {
      flex-grow: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 60px;
      padding-bottom: 8px;
      position: relative;
    }

    .signature-area img {
      max-width: 90%;
      max-height: 55px;
      object-fit: contain;
      border: none;
      border-radius: 0;
      padding: 0;
      background: transparent;
    }


    .signature-line {
      width: 100%;
      height: 1px;
      background: #dee2e6;
      margin-top: 5px;
    }

    .receipt-footer {
      background: #34495e;
      color: white;
      text-align: center;
      padding: 12px;
      font-size: 0.75em;
      opacity: 0.9;
    }

    .company-contact {
  margin-top: 5px;
  font-size: 0.9em;
  color: #555;
}
.company-contact div {
  margin-bottom: 2px;
}

.logo-image {
  width: 80px;          /* Adjust size as needed */
  height: 80px;
  border-radius: 50%;   /* Makes it a perfect circle */
  object-fit: cover;    /* Ensures image covers the circle without distortion */
  border: 2px solid #3498db; /* Optional: border color and thickness */
  background: white;    /* Optional: white background behind the image */
}



    @media (max-width: 768px) {
      .receipt-container {
        margin: 10px;
        border-radius: 0;
      }
      
      .receipt-body {
        padding: 15px;
      }
      
      .details-grid,
      .signatures-section {
        grid-template-columns: 1fr;
        gap: 10px;
      }
      
      .items-table th,
      .items-table td {
        padding: 8px 6px;
        font-size: 0.8em;
      }
      
      .receipt-title {
        font-size: 1.5em;
      }
    }

    @media print {
      body {
        background: white;
        padding: 0;
      }
      
      .receipt-container {
        box-shadow: none;
        border: 1px solid #ccc;
        margin: 0;
        border-radius: 0;
      }
    }
  </style>
  <script>
    // Auto-generate receipt number and date if not provided
    document.addEventListener('DOMContentLoaded', function() {
      // Generate random receipt number
      const receiptNumberElements = document.querySelectorAll('[data-auto="receipt-number"]');
      receiptNumberElements.forEach(element => {
        if (!element.textContent.trim()) {
          const randomNumber = Math.floor(Math.random() * 900000) + 100000; // 6-digit number
          element.textContent = `#RCP${randomNumber}`;
        }
      });
      
      // Generate current date and time
      const dateElements = document.querySelectorAll('[data-auto="date"]');
      dateElements.forEach(element => {
        if (!element.textContent.trim()) {
          const now = new Date();
          const day = String(now.getDate()).padStart(2, '0');
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const year = now.getFullYear();
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          
          element.textContent = `${day}/${month}/${year} ${hours}:${minutes}`;
        }
      });
    });
  </script>
</head>
<body>
  <div class="receipt-container">
<header class="receipt-header" style="display: flex; align-items: center; justify-content: space-between; padding: 20px;">
  <div style="flex-shrink: 0;">
    {% if avatar_url %}
      <img src="{{ avatar_url }}" alt="Company Logo" class="logo-image" />
    {% else %}
      <div style="width: 60px; height: 60px; background: #fff; border-radius: 6px;"></div>
    {% endif %}
  </div>

  <div style="flex-grow: 1; text-align: center;">
    <h1 class="receipt-title" style="margin-bottom: 5px;">Receipt</h1>
    <p class="receipt-subtitle" style="margin: 0;">Transaction Record</p>
  </div>

  <div style="width: 60px;"></div>
</header>


<main class="receipt-body">
<section class="company-info">
  <div class="company-name">{{ company_name }}</div>
  {% if phone_number %}
  <div class="company-phone">Phone: {{ phone_number }}</div>
  {% endif %}
  {% if secondary_email %}
  <div class="company-email">Email: {{ secondary_email }}</div>
  {% endif %}
  <div class="company-message">Thank you for your business!</div>
</section>
<!-- Add this section after the company-info section and before receipt-details -->
<section class="client-info">
  <div class="details-grid">
    <div class="detail-item">
      <div class="detail-label">Customer Details</div>
      <div class="detail-value">
        {% if client_name %}
          <strong>{{ client_name }}</strong><br>
        {% endif %}
        {% if client_email %}
          {{ client_email }}<br>
        {% endif %}
        {% if client_phone %}
          {{ client_phone }}<br>
        {% endif %}
        {% if client_address %}
          {{ client_address }}
        {% endif %}
      </div>
    </div>
    <div class="detail-item">
      <div class="detail-label">Receipt Details</div>
      <div class="detail-value">
        Receipt No: 
        {% if receipt_number %}
          {{ receipt_number }}
        {% else %}
          <span data-auto="receipt-number"></span>
        {% endif %}
        <br>
        Date: 
        {% if transaction_date %}
          {{ transaction_date }}
        {% else %}
          <span data-auto="date"></span>
        {% endif %}
      </div>
    </div>
  </div>
</section>

      <section class="items-section">
        <table class="items-table">
          <thead>
            <tr>
              <th>Item Description</th>
              <th style="text-align: center;">Qty</th>
              <th style="text-align: right;">Unit Price</th>
              <th style="text-align: right;">Total</th>
            </tr>
          </thead>
          <tbody>
            {% for item in items %}
            <tr>
              <td class="item-name">{{ item.name }}</td>
              <td class="item-quantity" style="text-align: center;">{{ item.quantity }}</td>
<td class="item-price">{{ currency_symbol }}{{ item.unit_price }}</td>
<td class="item-price">{{ currency_symbol }}{{ item.total_price }}</td>

            </tr>
            {% endfor %}
          </tbody>
        </table>
      </section>

<section class="totals-section">
  <div class="total-row">
    <span class="total-label">Subtotal:</span>
    <span class="total-value">{{ currency_symbol }} {{ subtotal|default:total_price }}</span>
  </div>
  {% if tax_amount %}
  <div class="total-row">
    <span class="total-label">Tax:</span>
    <span class="total-value">{{ currency_symbol }} {{ tax_amount }}</span>
  </div>
  {% endif %}
  {% if discount_amount %}
  <div class="total-row">
    <span class="total-label">Discount:</span>
    <span class="total-value">-{{ currency_symbol }} {{ discount_amount }}</span>
  </div>
  {% endif %}
  <div class="total-row">
    <span class="total-label">Total Amount:</span>
    <span class="total-value">{{ currency_symbol }} {{ total_price }}</span>
  </div>
</section>

 <section class="signatures-section">
        <div class="signature-block">
          <div class="signature-label">Authorized Signature</div>
          <div class="signature-area">
            {% if signature_url %}
              <img src="{{ signature_url }}" alt="Signature" />
            {% endif %}
          </div>
          <div class="signature-line"></div>
        </div>

        <div class="signature-block">
          <div class="signature-label">Official Stamp</div>
          <div class="signature-area">
            {% if stamp_url %}
              <img src="{{ stamp_url }}" alt="Official Stamp" />
            {% endif %}
          </div>
          <div class="signature-line"></div>
        </div>
      </section>
    </main>

    <footer class="receipt-footer">
      <p>This receipt serves as proof of transaction • Please retain for your records</p>
    </footer>
  </div>
</body>
</html>
