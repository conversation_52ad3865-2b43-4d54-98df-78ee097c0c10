<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            width: 100%;
            max-width: 450px;
            text-align: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }

        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
            font-weight: 600;
        }

        p {
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        label {
            display: block;
            margin-bottom: 8px;
            color: #333;
            font-weight: 500;
        }

        input[type="password"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
            outline: none;
        }

        input[type="password"]:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .password-strength {
            margin-top: 8px;
            padding: 8px;
            border-radius: 4px;
            font-size: 14px;
            text-align: left;
        }

        .strength-weak {
            background-color: #ffebee;
            color: #c62828;
        }

        .strength-medium {
            background-color: #fff3e0;
            color: #ef6c00;
        }

        .strength-strong {
            background-color: #e8f5e8;
            color: #2e7d32;
        }

        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .back-link {
            margin-top: 20px;
            display: inline-block;
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .back-link:hover {
            color: #764ba2;
        }

        .message {
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            margin-left: 10px;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-top: 2px solid transparent;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .requirements {
            font-size: 14px;
            color: #666;
            text-align: left;
            margin-top: 8px;
        }

        .requirements ul {
            margin: 8px 0;
            padding-left: 20px;
        }

        .requirements li {
            margin: 4px 0;
        }

        .requirement-met {
            color: #2e7d32;
        }

        .requirement-unmet {
            color: #c62828;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔐</div>
        <h1>Reset Password</h1>
        <p>Enter your new password below.</p>
        
        <div id="messageContainer"></div>
        
        <form id="resetPasswordForm">
            <div class="form-group">
                <label for="newPassword">New Password</label>
                <input type="password" id="newPassword" name="newPassword" required>
                <div class="requirements">
                    <ul id="passwordRequirements">
                        <li id="req-length" class="requirement-unmet">At least 8 characters</li>
                        <li id="req-uppercase" class="requirement-unmet">At least one uppercase letter</li>
                        <li id="req-lowercase" class="requirement-unmet">At least one lowercase letter</li>
                        <li id="req-number" class="requirement-unmet">At least one number</li>
                    </ul>
                </div>
            </div>
            
            <div class="form-group">
                <label for="confirmPassword">Confirm Password</label>
                <input type="password" id="confirmPassword" name="confirmPassword" required>
                <div class="password-strength" id="passwordMatch" style="display: none;"></div>
            </div>
            
            <button type="submit" class="btn" id="submitBtn">
                Reset Password
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                </div>
            </button>
        </form>
        
        <a href="/login/" class="back-link">← Back to Login</a>
    </div>

    <script>
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const passwordMatch = document.getElementById('passwordMatch');
        const submitBtn = document.getElementById('submitBtn');

        // Password strength checking
        newPasswordInput.addEventListener('input', function() {
            const password = this.value;
            
            // Check requirements
            const requirements = {
                length: password.length >= 8,
                uppercase: /[A-Z]/.test(password),
                lowercase: /[a-z]/.test(password),
                number: /\d/.test(password)
            };
            
            // Update requirement indicators
            Object.keys(requirements).forEach(req => {
                const element = document.getElementById(`req-${req}`);
                if (requirements[req]) {
                    element.className = 'requirement-met';
                } else {
                    element.className = 'requirement-unmet';
                }
            });
            
            checkPasswordMatch();
        });

        confirmPasswordInput.addEventListener('input', checkPasswordMatch);

        function checkPasswordMatch() {
            const password = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            if (confirmPassword === '') {
                passwordMatch.style.display = 'none';
                return;
            }
            
            passwordMatch.style.display = 'block';
            
            if (password === confirmPassword) {
                passwordMatch.textContent = 'Passwords match';
                passwordMatch.className = 'password-strength strength-strong';
            } else {
                passwordMatch.textContent = 'Passwords do not match';
                passwordMatch.className = 'password-strength strength-weak';
            }
        }

        function isPasswordValid() {
            const password = newPasswordInput.value;
            const confirmPassword = confirmPasswordInput.value;
            
            return password.length >= 8 &&
                   /[A-Z]/.test(password) &&
                   /[a-z]/.test(password) &&
                   /\d/.test(password) &&
                   password === confirmPassword;
        }

        // Get UID and token from URL
        const urlParts = window.location.pathname.split('/');
        const uidb64 = urlParts[urlParts.length - 3];
        const token = urlParts[urlParts.length - 2];

        document.getElementById('resetPasswordForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            if (!isPasswordValid()) {
                document.getElementById('messageContainer').innerHTML = `
                    <div class="message error">
                        Please ensure your password meets all requirements and passwords match.
                    </div>
                `;
                return;
            }
            
            const newPassword = newPasswordInput.value;
            const loading = document.getElementById('loading');
            const messageContainer = document.getElementById('messageContainer');
            
            // Show loading state
            submitBtn.disabled = true;
            loading.style.display = 'inline-block';
            messageContainer.innerHTML = '';
            
            try {
                const response = await fetch(`/reset-password/${uidb64}/${token}/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ new_password: newPassword })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    messageContainer.innerHTML = `
                        <div class="message success">
                            ${data.message} Redirecting to login...
                        </div>
                    `;
                    
                    // Redirect to login after 3 seconds
                    setTimeout(() => {
                        window.location.href = '/login/';
                    }, 3000);
                } else {
                    messageContainer.innerHTML = `
                        <div class="message error">
                            ${data.error}
                        </div>
                    `;
                }
            } catch (error) {
                messageContainer.innerHTML = `
                    <div class="message error">
                        An error occurred. Please try again.
                    </div>
                `;
            } finally {
                // Hide loading state
                submitBtn.disabled = false;
                loading.style.display = 'none';
            }
        });
    </script>
</body>
</html>