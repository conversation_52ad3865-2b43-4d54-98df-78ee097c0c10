{% extends 'base.html' %}

{% block title %}{{ good.name }} - Good Details{% endblock %}

{% block content %}
<div class="container">
    <div class="row">
        <div class="col-12">
            <!-- Breadcrumb -->
            <nav aria-label="breadcrumb" class="mb-4">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'goods-management' %}">Goods Management</a></li>
                    <li class="breadcrumb-item active">{{ good.name }}</li>
                </ol>
            </nav>

            <!-- Display Messages -->
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="row">
                <!-- Good Details Card -->
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">Good Details</h5>
                            <button type="button" class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#editModal">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </div>
                        <div class="card-body">
                            <dl class="row">
                                <dt class="col-sm-3">Name:</dt>
                                <dd class="col-sm-9">{{ good.name }}</dd>

                                <dt class="col-sm-3">Description:</dt>
                                <dd class="col-sm-9">{{ good.description|default:"No description provided" }}</dd>

                                <dt class="col-sm-3">Price:</dt>
                                <dd class="col-sm-9">${{ good.price|floatformat:2 }}</dd>

                                <dt class="col-sm-3">Quantity:</dt>
                                <dd class="col-sm-9">
                                    <span class="badge {% if good.quantity == 0 %}bg-danger{% elif good.quantity < 10 %}bg-warning{% else %}bg-success{% endif %} fs-6">
                                        {{ good.quantity }}
                                    </span>
                                </dd>

                                <dt class="col-sm-3">Units Sold:</dt>
                                <dd class="col-sm-9">{{ good.units_sold }}</dd>

                                <dt class="col-sm-3">Category:</dt>
                                <dd class="col-sm-9">{{ good.category|default:"Uncategorized" }}</dd>

                                <dt class="col-sm-3">Total Value:</dt>
                                <dd class="col-sm-9"><strong>${{ good.total_value|floatformat:2 }}</strong></dd>

                                <dt class="col-sm-3">Created:</dt>
                                <dd class="col-sm-9">{{ good.created_at|date:"F d, Y H:i" }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>

                <!-- Actions Card -->
                <div class="col-md-4">
                    <div class="card">
                        <div class="card-header">
                            <h6 class="mb-0">Quick Actions</h6>
                        </div>
                        <div class="card-body">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success" onclick="openRestockModal()">
                                    <i class="fas fa-plus"></i> Restock
                                </button>
                                <button type="button" class="btn btn-info" onclick="openSellModal()" {% if good.quantity == 0 %}disabled{% endif %}>
                                    <i class="fas fa-shopping-cart"></i> Sell
                                </button>
                                <button type="button" class="btn btn-danger" onclick="confirmDelete()">
                                    <i class="fas fa-trash"></i> Delete
                                </button>
                                <a href="{% url 'goods-management' %}" class="btn btn-secondary">
                                    <i class="fas fa-arrow-left"></i> Back to List
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Stock Status Card -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">Stock Status</h6>
                        </div>
                        <div class="card-body text-center">
                            {% if good.quantity == 0 %}
                                <i class="fas fa-exclamation-triangle fa-2x text-danger mb-2"></i>
                                <p class="text-danger mb-0">Out of Stock</p>
                            {% elif good.quantity < 10 %}
                                <i class="fas fa-exclamation-circle fa-2x text-warning mb-2"></i>
                                <p class="text-warning mb-0">Low Stock</p>
                            {% else %}
                                <i class="fas fa-check-circle fa-2x text-success mb-2"></i>
                                <p class="text-success mb-0">In Stock</p>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Modal -->
<div class="modal fade" id="editModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Good</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name *</label>
                        <input type="text" class="form-control" id="name" name="name" value="{{ good.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3">{{ good.description }}</textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">Price *</label>
                                <input type="number" class="form-control" id="price" name="price" step="0.01" min="0" value="{{ good.price }}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">Quantity *</label>
                                <input type="number" class="form-control" id="quantity" name="quantity" min="0" value="{{ good.quantity }}" required>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <input type="text" class="form-control" id="category" name="category" value="{{ good.category }}">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Restock Modal -->
<div class="modal fade" id="restockModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Restock {{ good.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{% url 'restock-good' good.id %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <p>Current Stock: <strong>{{ good.quantity }}</strong></p>
                    <div class="mb-3">
                        <label for="restockQuantity" class="form-label">Quantity to Add *</label>
                        <input type="number" class="form-control" id="restockQuantity" name="quantity" min="1" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Restock</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sell Modal -->
<div class="modal fade" id="sellModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Sell {{ good.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{% url 'sell-good' good.id %}" method="post">
                {% csrf_token %}
                <div class="modal-body">
                    <p>Available Stock: <strong>{{ good.quantity }}</strong></p>
                    <div class="mb-3">
                        <label for="sellQuantity" class="form-label">Quantity to Sell *</label>
                        <input type="number" class="form-control" id="sellQuantity" name="quantity" min="1" max="{{ good.quantity }}" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">Sell</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>{{ good.name }}</strong>?</p>
                <p class="text-danger">This action cannot be undone.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form action="{% url 'delete-good' good.id %}" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">Delete</button>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function openRestockModal() {
    document.getElementById('restockQuantity').value = '';
    new bootstrap.Modal(document.getElementById('restockModal')).show();
}

function openSellModal() {
    document.getElementById('sellQuantity').value = '';
    new bootstrap.Modal(document.getElementById('sellModal')).show();
}

function confirmDelete() {
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Auto-dismiss alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        var alerts = document.querySelectorAll('.alert');
        alerts.forEach(function(alert) {
            var bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        });
    }, 5000);
});
</script>
{% endblock %}