{% extends 'base.html' %}

{% block title %}Protected Store - Enter Password{% endblock %}

{% block extra_css %}
<style>
    .password-container {
        min-height: 80vh;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .password-card {
        background: white;
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        max-width: 400px;
        width: 100%;
        text-align: center;
    }
    
    .lock-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        color: white;
        font-size: 2rem;
    }
    
    .password-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 10px;
    }
    
    .password-subtitle {
        color: #666;
        margin-bottom: 30px;
    }
    
    .store-info {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .store-name {
        font-weight: 600;
        color: #2c3e50;
    }
    
    .password-input {
        border: 2px solid #e0e0e0;
        border-radius: 8px;
        padding: 12px 16px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }
    
    .password-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .unlock-btn {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .unlock-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .error-message {
        background: #fee;
        color: #c33;
        padding: 10px;
        border-radius: 6px;
        margin-bottom: 20px;
        border: 1px solid #fcc;
    }
    
    .shared-by {
        font-size: 0.9rem;
        color: #888;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="password-container">
    <div class="password-card">
        <div class="lock-icon">
            <i class="fas fa-lock"></i>
        </div>
        
        <h1 class="password-title">Protected Store</h1>
        <p class="password-subtitle">This store is password protected. Please enter the password to continue.</p>
        
        <div class="store-info">
            <div class="store-name">{{ store_share.title|default:store_share.store.name }}</div>
            {% if store_share.description %}
            <div class="text-muted mt-1">{{ store_share.description }}</div>
            {% endif %}
        </div>
        
        {% if error %}
        <div class="error-message">
            <i class="fas fa-exclamation-triangle"></i> {{ error }}
        </div>
        {% endif %}
        
        <form method="POST">
            {% csrf_token %}
            <div class="mb-3">
                <input type="password" 
                       class="form-control password-input" 
                       name="password" 
                       placeholder="Enter password" 
                       required 
                       autofocus>
            </div>
            <button type="submit" class="btn unlock-btn w-100">
                <i class="fas fa-unlock"></i> Unlock Store
            </button>
        </form>
        
        <div class="shared-by">
            <i class="fas fa-user"></i> Shared by {{ store_share.created_by.get_full_name|default:store_share.created_by.username }}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Auto-focus on password input
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.querySelector('input[name="password"]');
    if (passwordInput) {
        passwordInput.focus();
    }
});

// Handle form submission with loading state
document.querySelector('form').addEventListener('submit', function() {
    const button = document.querySelector('.unlock-btn');
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Unlocking...';
    button.disabled = true;
});
</script>
{% endblock %}
