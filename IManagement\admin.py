from django.contrib import admin
from .models import (
    Store, Goods, UserProfile, Category, Country, State,
    Receipt, ReceiptItem, ReceiptTemplate,
    Invoice, InvoiceItem, InvoiceTemplate,
    ManagedGood, Notification, InventoryTransaction,
    Property, StoreShare
)

# Register your models here.

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ['user', 'phone_number', 'is_verified', 'created_at']
    list_filter = ['is_verified', 'created_at']
    search_fields = ['user__username', 'user__email', 'phone_number']

@admin.register(Store)
class StoreAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'is_active', 'created_at']
    list_filter = ['is_active', 'created_at']
    search_fields = ['name', 'owner__username', 'description']
    filter_horizontal = ['authorized_users']

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'owner__username']

@admin.register(Goods)
class GoodsAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'store', 'price', 'quantity', 'currency', 'created_at']
    list_filter = ['currency', 'is_used', 'available_for_delivery', 'created_at']
    search_fields = ['name', 'owner__username', 'store__name', 'description']
    readonly_fields = ['created_at', 'updated_at']

@admin.register(Property)
class PropertyAdmin(admin.ModelAdmin):
    list_display = ['name', 'owner', 'property_type', 'condition', 'current_value', 'currency', 'created_at']
    list_filter = ['property_type', 'condition', 'currency', 'insured', 'created_at']
    search_fields = ['name', 'owner__username', 'description', 'brand', 'serial_number']
    readonly_fields = ['created_at', 'updated_at']
    fieldsets = (
        ('Basic Information', {
            'fields': ('owner', 'name', 'description', 'property_type', 'condition')
        }),
        ('Financial Details', {
            'fields': ('purchase_price', 'current_value', 'currency', 'purchase_date')
        }),
        ('Location & Storage', {
            'fields': ('location', 'country', 'state')
        }),
        ('Documentation', {
            'fields': ('serial_number', 'model_number', 'brand', 'warranty_expiry')
        }),
        ('Insurance', {
            'fields': ('insured', 'insurance_value', 'insurance_expiry')
        }),
        ('Media & Notes', {
            'fields': ('images', 'documents', 'notes', 'tags')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(StoreShare)
class StoreShareAdmin(admin.ModelAdmin):
    list_display = ['store', 'created_by', 'title', 'is_active', 'view_count', 'expires_at', 'created_at']
    list_filter = ['is_active', 'password_protected', 'shared_via_email', 'created_at']
    search_fields = ['store__name', 'created_by__username', 'title', 'share_token']
    readonly_fields = ['share_token', 'view_count', 'last_accessed', 'created_at', 'updated_at']
    fieldsets = (
        ('Basic Information', {
            'fields': ('store', 'created_by', 'title', 'description')
        }),
        ('Access Control', {
            'fields': ('is_active', 'expires_at', 'password_protected', 'access_password')
        }),
        ('Sharing Details', {
            'fields': ('share_token', 'shared_via_email', 'recipient_emails')
        }),
        ('Analytics', {
            'fields': ('view_count', 'last_accessed'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

# Register other models with basic admin
admin.site.register(Country)
admin.site.register(State)
admin.site.register(ManagedGood)
admin.site.register(Notification)
admin.site.register(InventoryTransaction)
admin.site.register(Receipt)
admin.site.register(ReceiptItem)
admin.site.register(ReceiptTemplate)
admin.site.register(Invoice)
admin.site.register(InvoiceItem)
admin.site.register(InvoiceTemplate)
