{% extends 'base.html' %}

{% block title %}Inventory Management{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2><i class="fas fa-warehouse me-2"></i>Inventory Management</h2>
            <p class="text-muted mb-0">Manage your goods inventory and track stock levels</p>
        </div>
        <div class="d-flex gap-2">
            <a href="{% url 'goods_list_create' %}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>Add New Goods
            </a>
            <button class="btn btn-outline-secondary" onclick="exportInventory()">
                <i class="fas fa-download me-1"></i>Export
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ statistics.total_goods }}</h4>
                    <small class="text-muted">Total Goods</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                    <h4 class="mb-1">${{ statistics.total_inventory_value|floatformat:2 }}</h4>
                    <small class="text-muted">Inventory Value</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ statistics.low_stock_count }}</h4>
                    <small class="text-muted">Low Stock</small>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                    <h4 class="mb-1">{{ statistics.out_of_stock_count }}</h4>
                    <small class="text-muted">Out of Stock</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Inventory Table -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">Goods Inventory</h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" id="storeFilter">
                                <option value="">All Stores</option>
                                {% for store in stores %}
                                    <option value="{{ store.id }}">{{ store.name }}</option>
                                {% endfor %}
                            </select>
                            <select class="form-select form-select-sm" id="stockFilter">
                                <option value="">All Stock</option>
                                <option value="in_stock">In Stock</option>
                                <option value="low_stock">Low Stock</option>
                                <option value="out_of_stock">Out of Stock</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th>Product</th>
                                    <th>Store</th>
                                    <th>Current Stock</th>
                                    <th>Price</th>
                                    <th>Total Value</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in goods %}
                                <tr data-goods-id="{{ item.id }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if item.first_image_url %}
                                                <img src="{{ item.first_image_url }}" class="rounded me-2" 
                                                     style="width: 40px; height: 40px; object-fit: cover;" alt="{{ item.name }}">
                                            {% else %}
                                                <div class="bg-light rounded me-2 d-flex align-items-center justify-content-center" 
                                                     style="width: 40px; height: 40px;">
                                                    <i class="fas fa-image text-muted"></i>
                                                </div>
                                            {% endif %}
                                            <div>
                                                <div class="fw-medium">{{ item.name }}</div>
                                                <small class="text-muted">{{ item.category.name|default:"No Category" }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ item.store.name|default:"No Store" }}</td>
                                    <td>
                                        <span class="badge {% if item.quantity == 0 %}bg-danger{% elif item.is_low_stock %}bg-warning{% else %}bg-success{% endif %}">
                                            {{ item.quantity }}
                                        </span>
                                    </td>
                                    <td>{{ item.currency_symbol }}{{ item.price|floatformat:2 }}</td>
                                    <td>{{ item.currency_symbol }}{{ item.total_value|floatformat:2 }}</td>
                                    <td>
                                        <span class="badge {% if item.quantity == 0 %}bg-danger{% elif item.is_low_stock %}bg-warning text-dark{% else %}bg-success{% endif %}">
                                            {{ item.stock_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-primary" onclick="adjustStock({{ item.id }}, '{{ item.name }}')" title="Adjust Stock">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-info" onclick="viewActivity({{ item.id }}, '{{ item.name }}')" title="View Activity">
                                                <i class="fas fa-history"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">
                                        <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
                                        <h5>No goods found</h5>
                                        <p class="text-muted">Start by adding some goods to your inventory</p>
                                        <a href="{% url 'goods_list_create' %}" class="btn btn-primary">
                                            <i class="fas fa-plus me-1"></i>Add Your First Product
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity Sidebar -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recentActivityContainer">
                        {% if recent_activities %}
                            {% for activity in recent_activities|slice:":3" %}
                            <div class="activity-item mb-3 pb-3 {% if not forloop.last %}border-bottom{% endif %}">
                                <div class="d-flex align-items-start">
                                    <div class="activity-icon me-3">
                                        <div class="rounded-circle bg-primary bg-opacity-10 p-2">
                                            <i class="fas fa-{{ activity.icon|default:'box' }} text-primary"></i>
                                        </div>
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-medium">{{ activity.title|default:activity.action }}</div>
                                        <div class="text-muted small">{{ activity.description|default:activity.goods_name }}</div>
                                        <div class="text-muted small">
                                            <i class="fas fa-clock me-1"></i>
                                            {{ activity.timestamp|timesince }} ago
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                            
                            {% if recent_activities|length > 3 %}
                            <div class="text-center mt-3">
                                <a href="{% url 'goods_activity_history' %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye me-1"></i>Read More
                                </a>
                            </div>
                            {% endif %}
                        {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <h6>No Recent Activity</h6>
                            <p class="text-muted small">Your inventory activities will appear here</p>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Stock Adjustment Modal -->
<div class="modal fade" id="stockAdjustmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Adjust Stock</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="stockAdjustmentForm">
                <div class="modal-body">
                    <input type="hidden" id="goodsId" name="goods_id">
                    <div class="mb-3">
                        <label class="form-label">Product</label>
                        <input type="text" class="form-control" id="goodsName" readonly>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Action</label>
                        <select class="form-select" id="stockAction" name="action" required>
                            <option value="">Select Action</option>
                            <option value="add">Add Stock</option>
                            <option value="remove">Remove Stock</option>
                            <option value="set">Set Stock Level</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Quantity</label>
                        <input type="number" class="form-control" id="stockQuantity" name="quantity" min="1" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes (Optional)</label>
                        <textarea class="form-control" id="stockNotes" name="notes" rows="2" 
                                  placeholder="Reason for stock adjustment..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i>Update Stock
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function adjustStock(goodsId, goodsName) {
    document.getElementById('goodsId').value = goodsId;
    document.getElementById('goodsName').value = goodsName;
    document.getElementById('stockAdjustmentForm').reset();
    document.getElementById('goodsId').value = goodsId;
    document.getElementById('goodsName').value = goodsName;
    
    new bootstrap.Modal(document.getElementById('stockAdjustmentModal')).show();
}

function viewActivity(goodsId, goodsName) {
    window.location.href = `{% url 'goods_activity_history' %}?goods_id=${goodsId}`;
}

function exportInventory() {
    window.location.href = '{% url "manage_goods_inventory" %}?export=csv';
}

// Handle stock adjustment form submission
document.getElementById('stockAdjustmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch('{% url "manage_goods_inventory" %}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            bootstrap.Modal.getInstance(document.getElementById('stockAdjustmentModal')).hide();
            location.reload();
        } else {
            alert(data.error || 'Error updating stock');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error updating stock');
    });
});
</script>
{% endblock %}
