import os
from django.core.management.base import BaseCommand
from IManagement.models import ReceiptTemplate

class Command(BaseCommand):
    help = "Load initial receipt templates from HTML files into the database"

    def handle(self, *args, **kwargs):
        base_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..', 'receipt_templates_samples')
        base_dir = os.path.normpath(base_dir)

        if not os.path.exists(base_dir):
            self.stdout.write(self.style.ERROR(f"Directory does not exist: {base_dir}"))
            return

        loaded_count = 0
        for filename in os.listdir(base_dir):
            if filename.endswith('.html'):
                path = os.path.join(base_dir, filename)
                with open(path, 'r', encoding='utf-8') as f:
                    html_content = f.read()

                name = filename.replace('.html', '').replace('_', ' ').title()
                description = f"Auto-loaded template from {filename}"

                obj, created = ReceiptTemplate.objects.update_or_create(
                    name=name,
                    defaults={
                        'description': description,
                        'html_content': html_content,
                    }
                )

                action = "Created" if created else "Updated"
                self.stdout.write(self.style.SUCCESS(f"{action} template: {name}"))
                loaded_count += 1

        self.stdout.write(self.style.SUCCESS(f"Loaded {loaded_count} receipt templates successfully."))
