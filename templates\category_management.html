{% comment %} {% extends 'base.html' %}

{% block title %}Categories Management{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-md-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2 class="text-primary fw-bold">
                    <i class="fas fa-tags me-2"></i>Category Management
                </h2>
                <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                    <i class="fas fa-plus me-2"></i>Create Category
                </button>
            </div>

            <!-- Categories Grid -->
            <div class="row" id="categoriesGrid">
                <!-- Categories will be loaded here -->
            </div>

            <!-- Empty State -->
            <div id="emptyState" class="text-center py-5" style="display: none;">
                <div class="mb-4">
                    <i class="fas fa-tags fa-5x text-muted"></i>
                </div>
                <h4 class="text-muted">No Categories Found</h4>
                <p class="text-muted">Create your first category to organize your goods</p>
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                    <i class="fas fa-plus me-2"></i>Create Category
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Create Category Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1" aria-labelledby="createCategoryModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="createCategoryModalLabel">
                    <i class="fas fa-plus me-2"></i>Create New Category
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="createCategoryForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label fw-bold">Category Name</label>
                        <input type="text" class="form-control form-control-lg" id="categoryName" name="name" 
                               placeholder="Enter category name" required maxlength="100">
                        <div class="form-text">Choose a descriptive name for your category (max 100 characters)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="createCategoryBtn">
                        <i class="fas fa-save me-2"></i>Create Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="text-center py-5">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-2 text-muted">Loading categories...</p>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed bottom-0 end-0 p-3">
    <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-success text-white">
            <i class="fas fa-check-circle me-2"></i>
            <strong class="me-auto">Success</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="successMessage"></div>
    </div>
    
    <div id="errorToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-danger text-white">
            <i class="fas fa-exclamation-circle me-2"></i>
            <strong class="me-auto">Error</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="errorMessage"></div>
    </div>
</div>

<style>
    .category-card {
        transition: all 0.3s ease;
        border: none;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 15px;
        overflow: hidden;
    }

    .category-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 20px rgba(0,0,0,0.15);
    }

    .category-icon {
        font-size: 3rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .category-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1.5rem;
        border-radius: 15px 15px 0 0;
    }

    .modal-content {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
    }

    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }

    .category-stats {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        border-radius: 10px;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        font-weight: 600;
    }

    .fade-in {
        animation: fadeIn 0.5s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .empty-state-icon {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const categoriesGrid = document.getElementById('categoriesGrid');
    const emptyState = document.getElementById('emptyState');
    const loadingSpinner = document.getElementById('loadingSpinner');
    const createCategoryForm = document.getElementById('createCategoryForm');
    const createCategoryBtn = document.getElementById('createCategoryBtn');
    const successToast = new bootstrap.Toast(document.getElementById('successToast'));
    const errorToast = new bootstrap.Toast(document.getElementById('errorToast'));

    // Load categories on page load
    loadCategories();

    // Create category form submission
    createCategoryForm.addEventListener('submit', function(e) {
        e.preventDefault();
        createCategory();
    });

    function loadCategories() {
        showLoading(true);
        
        fetch('/categories/', {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.categories && data.categories.length > 0) {
                displayCategories(data.categories);
                emptyState.style.display = 'none';
            } else {
                categoriesGrid.innerHTML = '';
                emptyState.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Error loading categories:', error);
            showLoading(false);
            showError('Failed to load categories. Please try again.');
        });
    }

    function displayCategories(categories) {
        categoriesGrid.innerHTML = '';
        
        categories.forEach((category, index) => {
            const categoryCard = createCategoryCard(category, index);
            categoriesGrid.appendChild(categoryCard);
        });
    }

    function createCategoryCard(category, index) {
        const col = document.createElement('div');
        col.className = 'col-md-4 col-lg-3 mb-4 fade-in';
        col.style.animationDelay = `${index * 0.1}s`;
        
        const icons = ['fas fa-tag', 'fas fa-cube', 'fas fa-star', 'fas fa-heart', 'fas fa-leaf', 'fas fa-gem'];
        const randomIcon = icons[Math.floor(Math.random() * icons.length)];
        
        col.innerHTML = `
            <div class="card category-card h-100">
                <div class="category-header text-center">
                    <i class="${randomIcon} category-icon mb-2"></i>
                    <h5 class="mb-0 fw-bold">${category.name}</h5>
                </div>
                <div class="card-body text-center">
                    <div class="category-stats mb-3">
                        <i class="fas fa-box me-1"></i>
                        <span id="goods-count-${category.id}">0</span> goods
                    </div>
                    <p class="text-muted mb-3">
                        Category ID: ${category.id}
                    </p>
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="viewCategoryGoods(${category.id})">
                            <i class="fas fa-eye me-1"></i>View Goods
                        </button>
                        <button class="btn btn-outline-danger btn-sm" onclick="deleteCategory(${category.id}, '${category.name}')">
                            <i class="fas fa-trash me-1"></i>Delete
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // Load goods count for this category
        loadGoodsCount(category.id);
        
        return col;
    }

    function loadGoodsCount(categoryId) {
        fetch(`/goods/?category_id=${categoryId}`, {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCsrfToken(),
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            const countElement = document.getElementById(`goods-count-${categoryId}`);
            if (countElement && data.goods) {
                countElement.textContent = data.goods.length;
            }
        })
        .catch(error => {
            console.error('Error loading goods count:', error);
        });
    }

    function createCategory() {
        const formData = new FormData(createCategoryForm);
        const categoryName = formData.get('name').trim();
        
        if (!categoryName) {
            showError('Please enter a category name');
            return;
        }
        
        createCategoryBtn.disabled = true;
        createCategoryBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
        
        fetch('/categories/create/', {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCsrfToken()
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                showSuccess(data.message);
                createCategoryForm.reset();
                bootstrap.Modal.getInstance(document.getElementById('createCategoryModal')).hide();
                loadCategories(); // Reload categories
            } else if (data.error) {
                showError(data.error);
            }
        })
        .catch(error => {
            console.error('Error creating category:', error);
            showError('Failed to create category. Please try again.');
        })
        .finally(() => {
            createCategoryBtn.disabled = false;
            createCategoryBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Category';
        });
    }

    function deleteCategory(categoryId, categoryName) {
        if (confirm(`Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`)) {
            // Note: You'll need to implement the delete endpoint in your backend
            fetch(`/categories/${categoryId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => {
                if (response.ok) {
                    showSuccess(`Category "${categoryName}" deleted successfully`);
                    loadCategories();
                } else {
                    showError('Failed to delete category');
                }
            })
            .catch(error => {
                console.error('Error deleting category:', error);
                showError('Failed to delete category. Please try again.');
            });
        }
    }

    function viewCategoryGoods(categoryId) {
        window.location.href = `/goods/?category_id=${categoryId}`;
    }

    function showLoading(show) {
        loadingSpinner.style.display = show ? 'block' : 'none';
        categoriesGrid.style.display = show ? 'none' : 'block';
    }

    function showSuccess(message) {
        document.getElementById('successMessage').textContent = message;
        successToast.show();
    }

    function showError(message) {
        document.getElementById('errorMessage').textContent = message;
        errorToast.show();
    }

    function getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value || 
               document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    }

    // Make functions globally available
    window.deleteCategory = deleteCategory;
    window.viewCategoryGoods = viewCategoryGoods;
});
</script>
{% endblock %} {% endcomment %}