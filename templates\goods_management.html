{% extends 'base.html' %}

{% block title %}Goods Management{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Display Messages -->
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">Goods Management</h4>
                    <div>
                        <span class="badge bg-success me-2">Net Total: ${{ net_total|floatformat:2 }}</span>
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGoodModal">
                            <i class="fas fa-plus"></i> Add Good
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    {% if goods %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>Category</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Units Sold</th>
                                        <th>Total Value</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for good in goods %}
                                    <tr>
                                        <td>{{ good.name }}</td>
                                        <td>{{ good.description|default:"-" }}</td>
                                        <td>{{ good.category|default:"-" }}</td>
                                        <td>${{ good.price|floatformat:2 }}</td>
                                        <td>
                                            <span class="badge {% if good.quantity == 0 %}bg-danger{% elif good.quantity < 10 %}bg-warning{% else %}bg-success{% endif %}">
                                                {{ good.quantity }}
                                            </span>
                                        </td>
                                        <td>{{ good.units_sold }}</td>
                                        <td>${{ good.total_value|floatformat:2 }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="editGood({{ good.id }}, '{{ good.name }}', '{{ good.description }}', '{{ good.category }}', {{ good.price }}, {{ good.quantity }})" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="restockGood({{ good.id }}, '{{ good.name }}')" title="Restock">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="sellGood({{ good.id }}, '{{ good.name }}', {{ good.quantity }})" title="Sell">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteGood({{ good.id }}, '{{ good.name }}')" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-5">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No goods found</h5>
                            <p class="text-muted">Start by adding your first good!</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Good Modal -->
<div class="modal fade" id="addGoodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" action="{% url 'goods-management' %}">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title">Add New Good</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Name *</label>
                        <input type="text" class="form-control" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="category" class="form-label">Category</label>
                        <input type="text" class="form-control" name="category">
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="price" class="form-label">Price *</label>
                                <input type="number" class="form-control" name="price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="quantity" class="form-label">Quantity *</label>
                                <input type="number" class="form-control" name="quantity" min="0" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Good</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Good Modal -->
<div class="modal fade" id="editGoodModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post" id="editGoodForm">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title">Edit Good</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Name *</label>
                        <input type="text" class="form-control" name="name" id="edit_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" name="description" id="edit_description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit_category" class="form-label">Category</label>
                        <input type="text" class="form-control" name="category" id="edit_category">
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_price" class="form-label">Price *</label>
                                <input type="number" class="form-control" name="price" id="edit_price" step="0.01" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="edit_quantity" class="form-label">Quantity *</label>
                                <input type="number" class="form-control" name="quantity" id="edit_quantity" min="0" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Good</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Restock Modal -->
<div class="modal fade" id="restockModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <form method="post" id="restockForm">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="restockModalTitle">Restock Good</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="restock_quantity" class="form-label">Quantity to Add *</label>
                        <input type="number" class="form-control" name="quantity" id="restock_quantity" min="1" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">Restock</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Sell Modal -->
<div class="modal fade" id="sellModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <form method="post" id="sellForm">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="sellModalTitle">Sell Good</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="sell_quantity" class="form-label">Quantity to Sell *</label>
                        <input type="number" class="form-control" name="quantity" id="sell_quantity" min="1" required>
                    </div>
                    <p class="text-muted small" id="availableStock"></p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Sell</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <form method="post" id="deleteForm">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title">Confirm Delete</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete <strong id="deleteGoodName"></strong>?</p>
                    <p class="text-muted small">This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editGood(id, name, description, category, price, quantity) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_description').value = description;
    document.getElementById('edit_category').value = category;
    document.getElementById('edit_price').value = price;
    document.getElementById('edit_quantity').value = quantity;
    document.getElementById('editGoodForm').action = `/goods-management/${id}/`;
    new bootstrap.Modal(document.getElementById('editGoodModal')).show();
}

function restockGood(id, name) {
    document.getElementById('restockModalTitle').textContent = `Restock: ${name}`;
    document.getElementById('restockForm').action = `/goods-management/${id}/restock/`;
    document.getElementById('restock_quantity').value = '';
    new bootstrap.Modal(document.getElementById('restockModal')).show();
}

function sellGood(id, name, availableQuantity) {
    document.getElementById('sellModalTitle').textContent = `Sell: ${name}`;
    document.getElementById('sellForm').action = `/goods-management/${id}/sell/`;
    document.getElementById('sell_quantity').value = '';
    document.getElementById('sell_quantity').max = availableQuantity;
    document.getElementById('availableStock').textContent = `Available stock: ${availableQuantity}`;
    new bootstrap.Modal(document.getElementById('sellModal')).show();
}

function deleteGood(id, name) {
    document.getElementById('deleteGoodName').textContent = name;
    document.getElementById('deleteForm').action = `/goods-management/${id}/delete/`;
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}

// Auto-hide alerts after 5 seconds
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }, 5000);
    });
});
</script>
{% endblock %}