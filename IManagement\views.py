import os
import json
import random
import string
import logging
import traceback
import pathlib
from datetime import datetime
from decimal import Decimal, InvalidOperation
from django.utils import timezone
from django.http import JsonResponse
from django_countries.fields import CountryField
from .models import State , Country
from django.shortcuts import get_object_or_404
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_GET
from .models import Goods, Store, Category
import traceback
import json
from django.views.decorators.http import require_GET

from django.shortcuts import render, redirect, get_object_or_404
from django.http import (
    JsonResponse, HttpResponse, HttpResponseNotAllowed, HttpResponseForbidden
)
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.decorators import login_required
from django.contrib.auth import (
    login, authenticate, logout, update_session_auth_hash
)
from django.contrib.auth.forms import PasswordChangeForm
from django.contrib.auth.models import User
from django.core.mail import send_mail
from django.conf import settings
from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
from django.contrib.auth.tokens import default_token_generator
from django.contrib.sites.shortcuts import get_current_site
from django.views.decorators.http import require_http_methods, require_GET
from django.db.models import Q, Sum, F
from django.core.cache import cache
from django.core.files import File
from django.urls import reverse
from django.template import Template, Context
from weasyprint import HTML
from django.utils.dateparse import parse_date
from django.utils.decorators import method_decorator
from django.views import View

from .models import (
    Store, Goods, UserProfile, Notification,
    Receipt, ReceiptItem, ReceiptTemplate,
    Invoice, InvoiceItem, InvoiceTemplate,
    ManagedGood, Country, State, Category,
    InventoryTransaction, Property, StoreShare, GoodsActivity,
    TwoFactorAuth, TrustedDevice, SupportTicket
)
from IManagement.forms import (
    CustomUserCreationForm, CustomUserChangeForm,
    EnhancedUserCreationForm, EmailVerificationForm, VerificationCodeForm,
    PublicGoodsFilterForm, PrivateStoreInventoryForm
)


# Helper functions
def get_safe_share_link(request, path):
    """Safely generate share links, handling test environments"""
    try:
        return request.build_absolute_uri(path)
    except Exception:
        # Fallback for test environments or when host is not available
        return f"http://localhost:8000{path}"


def home(request):
    """Home page - shows public goods"""
    return render(request, 'home.html')

# AUTH
# This is the auth list 
@csrf_exempt
def register(request):
    if request.method == "POST":
        # Handle form submission
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON format"}, status=400)

        form = CustomUserCreationForm(data)
        if form.is_valid():
            user = form.save()  # Save the user

            # Create or update user profile with verification code
            profile, created = UserProfile.objects.get_or_create(user=user)

            # Generate verification code
            verification_code = ''.join(random.choices(string.digits, k=6))
            profile.verification_code = verification_code
            profile.is_verified = False
            profile.save()

            # Send verification email
            try:
                from django.core.mail import send_mail
                from django.template.loader import render_to_string
                from django.utils.html import strip_tags

                # Prepare email content
                subject = 'Verify Your Email - Inventory Management System'

                # Create HTML email content
                html_message = render_to_string('emails/verification_email.html', {
                    'user': user,
                    'verification_code': verification_code,
                    'site_name': 'Inventory Management System'
                })

                # Create plain text version
                plain_message = f"""
Hello {user.first_name} {user.last_name},

Thank you for registering with Inventory Management System!

To complete your registration, please verify your email address by entering the following 6-digit code:

{verification_code}

This code will expire in 24 hours for security reasons.

If you didn't create this account, please ignore this email.

Best regards,
Inventory Management System Team
                """.strip()

                # Send email
                send_mail(
                    subject=subject,
                    message=plain_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[user.email],
                    html_message=html_message,
                    fail_silently=False,
                )

                logger.info(f"Verification email sent successfully to {user.email}")

                return JsonResponse({
                    "message": "Registration successful! Please check your email for the verification code.",
                    "requires_verification": True,
                    "user_id": user.id,
                    "email": user.email
                }, status=201)

            except Exception as e:
                logger.error(f"Error sending verification email: {str(e)}")
                # If email fails, still allow user to proceed but log the error
                return JsonResponse({
                    "message": "Registration successful! However, there was an issue sending the verification email. Please contact support.",
                    "requires_verification": True,
                    "user_id": user.id,
                    "email": user.email,
                    "email_error": True
                }, status=201)
        else:
            return JsonResponse({"errors": form.errors}, status=400)

    # For GET request, render the registration page
    form = CustomUserCreationForm()
    return render(request, "register.html", {"form": form})


# Endpoint to fetch all countries
def get_countries(request):
    countries = Country.objects.all()
    country_data = [{"id": country.id, "name": country.name, "code": country.code} for country in countries]
    return JsonResponse({"countries": country_data})

# Endpoint to fetch states based on country
def get_states(request, country_id):
    # Filter states by country ID
    states = State.objects.filter(country_id=country_id)
    state_data = [{"id": state.id, "name": state.name} for state in states]
    return JsonResponse({"states": state_data})


@csrf_exempt
def user_login(request):
    if request.method == 'POST':
        try:
            username = request.POST.get('username')
            password = request.POST.get('password')
            remember_me = request.POST.get('remember_me')  # Get remember me checkbox

            if not username or not password:
                messages.error(request, 'Username and password are required.')
                return render(request, 'login.html')

            # Authenticate user
            user = authenticate(request, username=username, password=password)

            if user is not None:
                # Check if user's email is verified
                try:
                    profile = user.profile
                    if not profile.is_verified:
                        # User exists but email is not verified - redirect to verification page
                        messages.error(request, "Email not verified. Please verify your email first.")

                        # Store user info in session for verification
                        request.session['verification_email'] = user.email
                        request.session['verification_user_id'] = user.id

                        # Redirect to verification page instead of showing login with verification link
                        return redirect(f'/verify-email/?user_id={user.id}&email={user.email}')

                    # User is verified, proceed with login
                    login(request, user)

                    # Handle remember me functionality
                    if remember_me:
                        # Set session to expire in 30 days
                        request.session.set_expiry(30 * 24 * 60 * 60)  # 30 days in seconds
                    else:
                        # Set session to expire when browser closes
                        request.session.set_expiry(0)

                    messages.success(request, "Login successful! Welcome back.")

                    # Check for next parameter for redirect
                    next_url = request.GET.get('next') or request.POST.get('next')
                    if next_url:
                        return redirect(next_url)

                    return redirect('dashboard')

                except UserProfile.DoesNotExist:
                    # Create profile for existing users who don't have one
                    profile = UserProfile.objects.create(user=user, is_verified=False)
                    messages.error(request, "User profile not found. Please verify your email first.")

                    # Store user info in session for verification
                    request.session['verification_email'] = user.email
                    request.session['verification_user_id'] = user.id

                    # Redirect to verification page instead of showing login with verification link
                    return redirect(f'/verify-email/?user_id={user.id}&email={user.email}')

            else:
                messages.error(request, "Invalid username or password.")
                return render(request, 'login.html')

        except Exception as e:
            logger.error(f"Error in user_login: {str(e)}")
            messages.error(request, f"An unexpected error occurred: {str(e)}")
            return render(request, 'login.html')

    elif request.method == "GET":
        # Handle GET request to render the login page
        return render(request, 'login.html')

    else:
        return JsonResponse({"error": "Method not allowed"}, status=405)




@csrf_exempt
@login_required
def user_logout(request):
    if request.method != "POST":
        return JsonResponse({"error": "Method not allowed"}, status=405)

    logout(request)
    return redirect('login')  # Redirect to login page after logout



@login_required
def get_authenticated_user(request):
    user = request.user
    return JsonResponse({
        "id": user.id,
        "username": user.username,
        "email": user.email,
        "first_name": user.first_name,
        "last_name": user.last_name,
    })




# Set up logging
logger = logging.getLogger(__name__)

@csrf_exempt
def forget_password(request):
    # If GET request, serve the HTML page
    if request.method == 'GET':
        return render(request, 'forget_password.html')
    
    # If POST request, handle the API logic
    if request.method != 'POST':
        return JsonResponse({"error": "Method not allowed"}, status=405)
    
    try:
        data = json.loads(request.body)
        email = data.get("email")
    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON format"}, status=400)
    
    if not email:
        return JsonResponse({"error": "Email is required"}, status=400)
    
    try:
        user = User.objects.get(email=email)
    except User.DoesNotExist:
        return JsonResponse({"error": "Email address not found"}, status=404)
    
    # Generate token and uid
    token = default_token_generator.make_token(user)
    uid = urlsafe_base64_encode(str(user.pk).encode())
    
    # Build reset link
    current_site = get_current_site(request)
    domain = current_site.domain
    protocol = 'https' if request.is_secure() else 'http'
    reset_link = f"{protocol}://{domain}/reset-password/{uid}/{token}/"
    
    # Email content
    email_subject = 'Password Reset Request'
    email_body = f"""
    <html>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
        <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
            <div style="text-align: center; margin-bottom: 30px;">
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                           width: 80px; height: 80px; border-radius: 50%; 
                           margin: 0 auto; display: flex; align-items: center; 
                           justify-content: center; color: white; font-size: 24px;">
                    🔒
                </div>
            </div>
            
            <h2 style="color: #333; text-align: center; margin-bottom: 20px;">Password Reset Request</h2>
            
            <p style="margin-bottom: 20px;">Hello {user.first_name or user.username},</p>
            
            <p style="margin-bottom: 20px;">
                We received a request to reset your password for your account. 
                If you made this request, click the button below to reset your password:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <a href="{reset_link}" 
                   style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                          color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 8px; font-weight: 600; display: inline-block;">
                    Reset Your Password
                </a>
            </div>
            
            <p style="margin-bottom: 20px;">
                Or copy and paste this link into your browser:<br>
                <a href="{reset_link}" style="color: #667eea; word-break: break-all;">{reset_link}</a>
            </p>
            
            <p style="margin-bottom: 20px;">
                <strong>This link will expire in 24 hours for security reasons.</strong>
            </p>
            
            <p style="margin-bottom: 20px;">
                If you did not request a password reset, please ignore this email. 
                Your password will remain unchanged.
            </p>
            
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            
            <p style="font-size: 14px; color: #666; text-align: center;">
                This is an automated message, please do not reply to this email.
            </p>
        </div>
    </body>
    </html>
    """
    
    try:
        # Debug logging
        logger.info(f"Attempting to send email to: {email}")
        logger.info(f"Email host: {getattr(settings, 'EMAIL_HOST', 'Not set')}")
        logger.info(f"Email user: {getattr(settings, 'EMAIL_HOST_USER', 'Not set')}")
        logger.info(f"Default from email: {getattr(settings, 'DEFAULT_FROM_EMAIL', 'Not set')}")
        
        send_mail(
            email_subject,
            '',  # Plain text version (empty since we're using HTML)
            settings.DEFAULT_FROM_EMAIL,
            [email],
            fail_silently=False,
            html_message=email_body
        )
        return JsonResponse({"message": "Password reset email sent successfully."})
    except Exception as e:
        logger.error(f"Email sending failed: {str(e)}")
        return JsonResponse({"error": f"Failed to send email: {str(e)}"}, status=500)

@csrf_exempt
def reset_password(request, uidb64, token):
    # If GET request, serve the HTML page
    if request.method == 'GET':
        # Validate the token first
        try:
            uid = urlsafe_base64_decode(uidb64).decode()
            user = User.objects.get(pk=uid)
        except (TypeError, ValueError, User.DoesNotExist):
            return render(request, 'reset_password.html', {
                'error': 'Invalid reset link or the link has expired.'
            })
        
        if not default_token_generator.check_token(user, token):
            return render(request, 'reset_password.html', {
                'error': 'Invalid reset link or the link has expired.'
            })
        
        return render(request, 'reset_password.html')
    
    # If POST request, handle the API logic
    if request.method != 'POST':
        return JsonResponse({"error": "Method not allowed"}, status=405)
    
    try:
        uid = urlsafe_base64_decode(uidb64).decode()
        user = User.objects.get(pk=uid)
    except (TypeError, ValueError, User.DoesNotExist):
        return JsonResponse({"error": "Invalid link or expired."}, status=400)
    
    if not default_token_generator.check_token(user, token):
        return JsonResponse({"error": "Invalid token or expired."}, status=400)
    
    try:
        data = json.loads(request.body)
        new_password = data.get('new_password')
    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON format"}, status=400)
    
    if not new_password:
        return JsonResponse({"error": "New password is required"}, status=400)
    
    # Basic password validation
    if len(new_password) < 8:
        return JsonResponse({"error": "Password must be at least 8 characters long"}, status=400)
    
    user.set_password(new_password)
    user.save()
    
    return JsonResponse({"message": "Password has been reset successfully."})


# Store Endpoints Begins here 
@login_required
def dashboard(request):
    if request.method != 'GET':
        return HttpResponseNotAllowed(['GET'])

    # Fetch the store count for the logged-in user
    store_count = Store.objects.filter(owner=request.user).count()
     # Fetch notifications for the logged-in user
    notifications = Notification.objects.filter(user=request.user).order_by('-created_at')

    # Optionally, mark notifications as read when the user views the dashboard
    notifications.update(read=True)
    
    # Pass the store count and username to the dashboard template
    return render(request, 'dashboard.html', {
        'store_count': store_count,
        'username': request.user.username,
        'notifications': notifications,
    })


@csrf_exempt
@login_required
def stores_list_create(request):
    try:
        if request.method == 'POST':
            # Get JSON data or form data fallback
            try:
                data = json.loads(request.body)
                name = data.get('name')
                description = data.get('description', '')
            except Exception:
                # fallback for form POST (from HTML form submission)
                name = request.POST.get('name')
                description = request.POST.get('description', '')

            if not name:
                stores = Store.objects.filter(owner=request.user)
                return render(request, 'stores_list.html', {
                    'stores': stores,
                    'error': 'Name is required',
                    'old_name': name,
                    'old_description': description,
                })

            store = Store.objects.create(owner=request.user, name=name, description=description)
            return redirect('stores_list_create')  # redirect after POST to avoid re-submission

        elif request.method == 'GET':
            stores = Store.objects.filter(owner=request.user)
            return render(request, 'stores_list.html', {'stores': stores})

        else:
            return HttpResponseNotAllowed(['GET', 'POST'])

    except Exception:
        traceback.print_exc()
        return render(request, 'stores_list.html', {'error': 'Server error occurred'})


@login_required
@csrf_exempt
def store_detail(request, store_id):
    store = get_object_or_404(Store, pk=store_id)

    try:
        if request.method == 'GET':
            return render(request, 'store_detail.html', {'store': store})

        if request.method == 'PUT':
            if store.owner != request.user:
                return render(request, 'store_detail.html', {'error': "Only the store owner can edit the store.", 'store': store})

            try:
                data = json.loads(request.body)
                store.name = data.get('name', store.name)
                store.description = data.get('description', store.description)
                store.save()
                return render(request, 'store_detail.html', {'message': "Store updated successfully!", 'store': store})

            except json.JSONDecodeError:
                return render(request, 'store_detail.html', {'error': "Invalid data format", 'store': store})

        if request.method == 'DELETE':
            if store.owner != request.user:
                return render(request, 'store_detail.html', {'error': "Only the store owner can delete the store.", 'store': store})

            store.delete()
            return render(request, 'store_detail.html', {'message': "Store deleted successfully!"})

        return render(request, 'store_detail.html', {'error': "Method not allowed", 'store': store})

    except Exception as e:
        traceback.print_exc()
        return render(request, 'store_detail.html', {'error': "Server error occurred", 'details': str(e)})


@csrf_exempt
@login_required
def store_add_users(request, store_id):
    user = request.user
    cache_key = f"add_users_rate_limit_{user.id}"
    max_requests = 5
    time_window = 60  # seconds

    request_count = cache.get(cache_key, 0)
    if request_count >= max_requests:
        error_msg = "Rate limit exceeded. Please try again later."
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({"error": error_msg}, status=429)
        else:
            return render(request, 'store_add_users.html', {
                'store_id': store_id,
                'error': error_msg,
            })

    if request.method == 'POST':
        try:
            if request_count == 0:
                cache.set(cache_key, 1, timeout=time_window)
            else:
                cache.incr(cache_key)

            data = json.loads(request.body) if request.headers.get('Content-Type') == 'application/json' else request.POST
            email = data.get('email')

            if not email:
                error_msg = 'Email is required.'
                if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                    return JsonResponse({'error': error_msg}, status=400)
                else:
                    return render(request, 'store_add_users.html', {'store_id': store_id, 'error': error_msg})

            store = get_object_or_404(Store, pk=store_id, owner=user)

            try:
                user_to_add = User.objects.get(email=email)
            except User.DoesNotExist:
                error_msg = 'User with this email does not exist.'
                if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                    return JsonResponse({'error': error_msg}, status=404)
                else:
                    return render(request, 'store_add_users.html', {'store_id': store_id, 'error': error_msg})

            if user_to_add == store.owner:
                error_msg = "Owner cannot be added as an authorized user."
                if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                    return JsonResponse({'error': error_msg}, status=400)
                else:
                    return render(request, 'store_add_users.html', {'store_id': store_id, 'error': error_msg})

            if user_to_add == user:
                error_msg = "You cannot add yourself to the store."
                if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                    return JsonResponse({'error': error_msg}, status=400)
                else:
                    return render(request, 'store_add_users.html', {'store_id': store_id, 'error': error_msg})

            store.authorized_users.add(user_to_add)
            store.save()

            # Send email notification
            send_mail(
                subject='You have been added to a store',
                message=f'Hello {user_to_add.username},\n\nYou have been granted access to the store "{store.name}". You can now manage goods under this store.',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[email],
                fail_silently=True,
            )

            # Create notification record
            Notification.objects.create(
                user=user_to_add,
                message=f'You have been added to the store "{store.name}".'
            )

            success_msg = f'User {email} added to the store successfully.'

            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({'message': success_msg})
            else:
                return render(request, 'store_add_users.html', {
                    'store_id': store_id,
                    'success': success_msg,
                })

        except Exception as e:
            traceback.print_exc()
            error_msg = f'Server error: {str(e)}'
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({'error': error_msg}, status=500)
            else:
                return render(request, 'store_add_users.html', {'store_id': store_id, 'error': error_msg})

    # GET method - render page with form
    elif request.method == 'GET':
        return render(request, 'store_add_users.html', {'store_id': store_id})

    else:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'error': 'Method not allowed'}, status=405)
        else:
            return render(request, 'store_add_users.html', {'store_id': store_id, 'error': 'Method not allowed'})



@login_required
def list_authorized_stores(request):
    stores = request.user.authorized_stores.all()
    return render(request, 'authorized_stores.html', {'stores': stores})





from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_GET
from .models import Store,  Goods, Receipt, Invoice
from django.utils.timezone import now
import datetime

@login_required
@require_GET
def api_store_count(request):
    count = Store.objects.filter(owner=request.user).count()
    return JsonResponse({'count': count})


@login_required
@require_GET
def api_goods_count(request):
    count = Goods.objects.filter(owner=request.user).count()
    return JsonResponse({'count': count})

@login_required
@require_GET
def api_categories_count(request):
    count = Category.objects.filter(owner=request.user).count()  # Get the count of categories for the logged-in user
    return JsonResponse({'count': count})

@login_required
@require_GET
def api_receipts_count(request):
    today = now()
    start_of_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    count = Receipt.objects.filter(user=request.user, created_at__gte=start_of_month).count()
    return JsonResponse({'count': count})

@login_required
@require_GET
def api_invoices_count(request):
    today = now()
    start_of_month = today.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    count = Invoice.objects.filter(user=request.user, created_at__gte=start_of_month).count()
    return JsonResponse({'count': count})

@login_required
@require_GET
def api_properties_count(request):
    count = Property.objects.filter(owner=request.user).count()
    return JsonResponse({'count': count})

@login_required
@require_GET
def api_user_stores(request):
    """API endpoint to get user's stores for email sharing"""
    stores = Store.objects.filter(
        Q(owner=request.user) | Q(authorized_users=request.user)
    ).distinct().values('id', 'name', 'description', 'created_at')

    stores_list = list(stores)
    for store in stores_list:
        store['created_at'] = store['created_at'].isoformat()

    return JsonResponse({
        'success': True,
        'stores': stores_list,
        'count': len(stores_list)
    })


@login_required
@csrf_exempt
@require_http_methods(["POST", "GET"])
def remove_users_from_store(request, store_id):
    if request.method == 'GET':
        # Render form page to remove users (frontend)
        return render(request, 'store_remove_users.html', {'store_id': store_id})

    # POST method: process removal
    try:
        store = get_object_or_404(Store, pk=store_id)
    except Store.DoesNotExist:
        return JsonResponse({"error": "Store not found"}, status=404)

    # Only owner can remove users
    if store.owner != request.user:
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({"error": "Only store owner can remove users"}, status=403)
        else:
            return render(request, 'store_remove_users.html', {
                'store_id': store_id,
                'error': "Only store owner can remove users"
            })

    try:
        if request.headers.get('Content-Type') == 'application/json':
            data = json.loads(request.body)
        else:
            # fallback for form submission (not ajax)
            emails_raw = request.POST.get('emails', '')
            # split emails by comma or newlines
            emails = [e.strip() for e in emails_raw.split(',') if e.strip()]
            data = {'emails': emails}

        emails = data.get("emails", [])

        if not isinstance(emails, list) or not emails:
            error_msg = "Provide a non-empty list of emails"
            if request.headers.get('x-requested-with') == 'XMLHttpRequest':
                return JsonResponse({"error": error_msg}, status=400)
            else:
                return render(request, 'store_remove_users.html', {
                    'store_id': store_id,
                    'error': error_msg
                })

        removed_users = []
        not_found_emails = []

        for email in emails:
            try:
                user = User.objects.get(email=email)
                if user in store.authorized_users.all():
                    store.authorized_users.remove(user)
                    removed_users.append(email)
                else:
                    not_found_emails.append(email)
            except User.DoesNotExist:
                not_found_emails.append(email)

        store.save()

        success_msg = "Users removal process completed"
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({
                "message": success_msg,
                "removed_users": removed_users,
                "not_found_or_not_authorized": not_found_emails
            })
        else:
            return render(request, 'store_remove_users.html', {
                'store_id': store_id,
                'success': success_msg,
                'removed_users': removed_users,
                'not_found_emails': not_found_emails
            })

    except Exception as e:
        traceback.print_exc()
        error_msg = f"Server error: {str(e)}"
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({"error": error_msg}, status=500)
        else:
            return render(request, 'store_remove_users.html', {'store_id': store_id, 'error': error_msg})
import traceback
from django.shortcuts import render
from django.http import JsonResponse, HttpResponseNotAllowed
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.core.exceptions import ValidationError
from django.utils.dateparse import parse_date
from .models import UserProfile  # Make sure to import your UserProfile model

@login_required
def user_profile(request):
    """
    Handle user profile operations - GET, POST (create/update), DELETE
    """
    try:
        user = request.user

        if request.method == 'GET':
            # Check if this is an AJAX request (API call)
            if request.headers.get('Content-Type') == 'application/json' or request.META.get('HTTP_ACCEPT', '').startswith('application/json'):
                try:
                    profile = user.profile
                    data = {
                        "user_id": user.id,
                        "username": user.username,
                        "email": user.email,
                        "phone_number": profile.phone_number or "",
                        "secondary_email": profile.secondary_email or "",
                        "avatar_url": profile.avatar.url if profile.avatar else None,
                        "date_of_birth": profile.date_of_birth.isoformat() if profile.date_of_birth else None,
                        "bio": profile.bio or "",
                        "website_url": profile.website_url or "",
                        "company_name": profile.company_name or "",
                        "created_at": profile.created_at.isoformat(),
                        "updated_at": profile.updated_at.isoformat(),
                    }
                    return JsonResponse(data)
                except UserProfile.DoesNotExist:
                    return JsonResponse({"error": "User profile does not exist"}, status=404)
            else:
                # Return the HTML template for regular page requests
                return render(request, 'profile.html')

        elif request.method == 'POST':
            # Handle form data (multipart/form-data for file uploads)
            try:
                # Get or create profile
                profile, created = UserProfile.objects.get_or_create(user=user)
                
                # Update fields from form data
                phone_number = request.POST.get('phone_number', '').strip()
                secondary_email = request.POST.get('secondary_email', '').strip()
                date_of_birth_str = request.POST.get('date_of_birth', '').strip()
                bio = request.POST.get('bio', '').strip()
                website_url = request.POST.get('website_url', '').strip()
                company_name = request.POST.get('company_name', '').strip()
                avatar = request.FILES.get('avatar')

                # Validate and update phone number
                if phone_number:
                    profile.phone_number = phone_number

                # Validate and update secondary email
                if secondary_email:
                    # Basic email validation
                    if '@' in secondary_email and '.' in secondary_email:
                        profile.secondary_email = secondary_email
                    else:
                        return JsonResponse({"error": "Invalid secondary email format"}, status=400)

                # Validate and update date of birth
                if date_of_birth_str:
                    try:
                        date_of_birth = parse_date(date_of_birth_str)
                        if date_of_birth:
                            profile.date_of_birth = date_of_birth
                        else:
                            return JsonResponse({"error": "Invalid date format"}, status=400)
                    except (ValueError, TypeError):
                        return JsonResponse({"error": "Invalid date format"}, status=400)

                # Update text fields
                profile.bio = bio
                profile.website_url = website_url
                profile.company_name = company_name

                # Handle avatar upload
                if avatar:
                    # Validate file type
                    allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
                    if avatar.content_type not in allowed_types:
                        return JsonResponse({"error": "Invalid file type. Please upload JPG, PNG, GIF, or WebP images only."}, status=400)
                    
                    # Validate file size (e.g., max 5MB)
                    if avatar.size > 5 * 1024 * 1024:  # 5MB
                        return JsonResponse({"error": "File too large. Maximum size is 5MB."}, status=400)
                    
                    profile.avatar = avatar

                # Save the profile
                profile.save()

                message = "User profile created successfully" if created else "User profile updated successfully"
                status_code = 201 if created else 200
                
                response_data = {
                    "message": message,
                    "profile_id": profile.id,
                    "created": created
                }
                
                return JsonResponse(response_data, status=status_code)

            except ValidationError as e:
                return JsonResponse({"error": str(e)}, status=400)
            except Exception as e:
                traceback.print_exc()
                return JsonResponse({"error": f"Failed to save profile: {str(e)}"}, status=500)

        elif request.method == 'DELETE':
            try:
                profile = user.profile
                profile.delete()
                return JsonResponse({"message": "User profile deleted successfully"})
            except UserProfile.DoesNotExist:
                return JsonResponse({"error": "User profile does not exist"}, status=404)

        else:
            return HttpResponseNotAllowed(['GET', 'POST', 'DELETE'])

    except Exception as e:
        traceback.print_exc()
        return JsonResponse({"error": f"Server error: {str(e)}"}, status=500)
    

@login_required
def receipt_templates_list(request):
    if request.method != "GET":
        return HttpResponseNotAllowed(["GET"])
    
    try:
        # Fetch all receipt templates
        templates = ReceiptTemplate.objects.all()

        # Render the receipt_templates_list.html template with the templates
        return render(request, 'receipt_templates_list.html', {'templates': templates})
    
    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})



@login_required
def receipt_template_preview(request, template_id):
    if request.method != "GET":
        return HttpResponseNotAllowed(["GET"])
    
    try:
        # If ?receipt=1 in URL, treat template_id as receipt ID instead
        if request.GET.get('receipt'):
            # Preview receipt by ID
            receipt = get_object_or_404(Receipt, id=template_id, user=request.user)
            items = receipt.items.all()

            # Fetch company details from user profile (if available)
            company_name = getattr(request.user.profile, 'company_name', "Your Company")
            phone_number = getattr(request.user.profile, 'phone_number', "")
            secondary_email = getattr(request.user.profile, 'secondary_email', "")

            # Get absolute file URI for avatar (company logo)
            avatar_url = ''
            if hasattr(request.user, 'profile') and getattr(request.user.profile, 'avatar', None):
                avatar_url = request.build_absolute_uri(request.user.profile.avatar.url)

            # Fetch the currency symbol
            currency_symbol = CURRENCY_SYMBOLS.get(receipt.currency, '$')

            # Generate receipt number and transaction date
            import random
            from datetime import datetime
            receipt_number = f"#RCP{random.randint(100000, 999999)}"
            transaction_date = datetime.now().strftime("%d/%m/%Y %H:%M")

            # Fetch the template content for rendering
            template_str = receipt.template.html_content

            # Prepare items data
            items_data = [
                {
                    'name': item.name,
                    'quantity': item.quantity,
                    'unit_price': f"{item.unit_price:.2f}",
                    'total_price': f"{item.total_price:.2f}",
                }
                for item in items
            ]

            # Signature URL
            # Simplified signature URL
            signature_url = ''
            if receipt.signature and receipt.signature.name:
                signature_url = request.build_absolute_uri(receipt.signature.url)

            # Simplified stamp URL
            stamp_url = ''
            if receipt.stamp and receipt.stamp.name:
                stamp_url = request.build_absolute_uri(receipt.stamp.url)

            # Prepare the context for rendering the template
            context = Context({
                "items": items_data,
                "total_price": f"{receipt.total_price:.2f}",
                "subtotal": f"{(receipt.total_price - receipt.tax_amount + receipt.discount_amount):.2f}",
                "tax_amount": f"{receipt.tax_amount:.2f}" if receipt.tax_amount else None,
                "discount_amount": f"{receipt.discount_amount:.2f}" if receipt.discount_amount else None,
                "currency_symbol": currency_symbol,
                "company_name": company_name,
                "phone_number": phone_number,
                "secondary_email": secondary_email,
                "avatar_url": avatar_url, 
                "signature_url": signature_url,
                "stamp_url": stamp_url,
                "receipt_number": receipt_number,
                "transaction_date": transaction_date,
                "currency": receipt.currency,
                "client_name": receipt.client_name,
                "client_email": receipt.client_email,
                "client_phone": receipt.client_phone,
                "client_address": receipt.client_address,
                "client_company": receipt.client_company,
                
            })

            # Render the template with context
            template_obj = Template(template_str)
            rendered_html = template_obj.render(context)

            # Return the preview response
            return HttpResponse(rendered_html)

        else:
            # Preview blank template by template ID
            template = get_object_or_404(ReceiptTemplate, pk=template_id)
            return render(request, 'receipt_template_preview.html', {'template': template})

    except Receipt.DoesNotExist:
        return JsonResponse({"error": "Receipt not found"}, status=404)
    except Exception as e:
        traceback.print_exc()
        return JsonResponse({"error": "Server error", "details": str(e)}, status=500)









# Adjust this path to where your default_stamp.png is stored
DEFAULT_STAMP_PATH = os.path.join('media', 'default_stamp.png')


@csrf_exempt
@login_required
def create_receipt(request):
    try:
        if request.method == 'POST':
            template_id = request.POST.get('template_id')
            currency_code = request.POST.get('currency', 'USD')
            tax_amount = request.POST.get('tax_amount')
            discount_amount = request.POST.get('discount_amount')
            
            # Get client information
            client_name = request.POST.get('client_name', '')
            client_email = request.POST.get('client_email', '')
            client_phone = request.POST.get('client_phone', '')
            client_address = request.POST.get('client_address', '')
            client_company = request.POST.get('client_company', '')

            # Convert tax and discount to float, default to 0 if empty
            tax_amount = float(tax_amount) if tax_amount else 0.0
            discount_amount = float(discount_amount) if discount_amount else 0.0

            item_names = request.POST.getlist('item_name[]')
            item_quantities = request.POST.getlist('item_quantity[]')
            item_unit_prices = request.POST.getlist('item_unit_price[]')

            items_data = []
            for i in range(len(item_names)):
                items_data.append({
                    'name': item_names[i],
                    'quantity': item_quantities[i],
                    'unit_price': item_unit_prices[i]
                })

            template = ReceiptTemplate.objects.get(id=template_id)

            receipt = Receipt.objects.create(
                user=request.user,
                template=template,
                total_price=0,
                currency=currency_code,
                tax_amount=tax_amount,
                discount_amount=discount_amount,
                # Add client information
                client_name=client_name,
                client_email=client_email,
                client_phone=client_phone,
                client_address=client_address,
                client_company=client_company
            )
            
            # Save signature file
            signature_file = request.FILES.get('signature')
            if signature_file:
                receipt.signature.save(signature_file.name, signature_file, save=True)

            # Save stamp file
            stamp_file = request.FILES.get('stamp')
            if stamp_file:
                receipt.stamp.save(stamp_file.name, stamp_file, save=True)

            total_price = 0
            for item_data in items_data:
                name = item_data.get('name')
                quantity = int(item_data.get('quantity', 1))
                unit_price = float(item_data.get('unit_price', 0))
                total_item_price = quantity * unit_price

                ReceiptItem.objects.create(
                    receipt=receipt,
                    name=name,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=total_item_price
                )
                total_price += total_item_price

            # Add tax and subtract discount from total
            total_price = total_price + tax_amount - discount_amount

            receipt.total_price = total_price
            receipt.save()

            return redirect('list_user_receipts')

        else:
            templates = ReceiptTemplate.objects.all()
            return render(request, 'receipt_create.html', {'templates': templates})

    except Exception:
        traceback.print_exc()
        return render(request, 'receipt_create.html', {'error': 'Server error'})




# Map currency codes to symbols
CURRENCY_SYMBOLS = {
    'USD': '$',
    'EUR': '€',
    'GBP': '£',
    'NGN': '₦',
    'JPY': '¥',  # Japanese Yen
    'AUD': 'A$',  # Australian Dollar
    'CAD': 'C$',  # Canadian Dollar
    'INR': '₹',  # Indian Rupee
    'CNY': '¥',  # Chinese Yuan
    'MXN': '$',  # Mexican Peso
    'BRL': 'R$',  # Brazilian Real
    'ZAR': 'R',  # South African Rand
    'CHF': 'CHF',  # Swiss Franc
    'HKD': 'HK$',  # Hong Kong Dollar
    'SGD': 'S$',  # Singapore Dollar
    'MYR': 'RM',  # Malaysian Ringgit
    'PKR': '₨',  # Pakistani Rupee
    'SAR': 'ر.س',  # Saudi Riyal
    'TRY': '₺',  # Turkish Lira
    'AED': 'د.إ',  # UAE Dirham
    'SEK': 'kr',  # Swedish Krona
    'NOK': 'kr',  # Norwegian Krone
    'DKK': 'kr',  # Danish Krone
    'THB': '฿',  # Thai Baht
    'KES': 'KSh',  # Kenyan Shilling
    # Add more currency codes and symbols as needed
}

    
from django.core.paginator import Paginator
from django.db.models import Q
from datetime import datetime
from django.utils.dateparse import parse_date

def list_user_receipts(request):
    # Start with all receipts for the current logged-in user, ordered by creation date (newest first)
    receipts = Receipt.objects.filter(user=request.user).order_by('-created_at')
    
    # Date filtering
    created_at_filter = request.GET.get('created_at')
    if created_at_filter:
        try:
            # Parse the date string
            filter_date = parse_date(created_at_filter)
            if filter_date:
                # Filter receipts created on the specific date
                receipts = receipts.filter(created_at__date=filter_date)
        except ValueError:
            # If date parsing fails, ignore the filter
            pass
    
    # Pagination - 10 receipts per page
    paginator = Paginator(receipts, 10)
    page_number = request.GET.get('page', 1)
    
    try:
        page_obj = paginator.get_page(page_number)
    except:
        page_obj = paginator.get_page(1)
    
    # If the request expects JSON data
    if request.headers.get('Accept') == 'application/json' or request.GET.get('format') == 'json':
        data = []
        for receipt in page_obj:
            symbol = CURRENCY_SYMBOLS.get(receipt.currency, '$')  # Default to '$' if currency is unknown
            data.append({
                'id': receipt.id,
                'template_name': receipt.template.name if receipt.template else 'N/A',
                'currency_symbol': receipt.currency_symbol,
                'template_id': receipt.template.id if receipt.template else None,
                'total_price': float(receipt.total_price),
                'created_at': receipt.created_at.isoformat(),
                'updated_at': receipt.updated_at.isoformat(),
            })
        
        # Return JSON with pagination info
        return JsonResponse({
            'receipts': data,
            'pagination': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'has_previous': page_obj.has_previous(),
                'has_next': page_obj.has_next(),
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None,
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                'total_receipts': paginator.count,
            }
        }, safe=False)
    
    # If not JSON, render the regular HTML page with pagination
    context = {
        'page_obj': page_obj,
        'receipts': page_obj,  # For backward compatibility
        'total_receipts': paginator.count,
        'created_at_filter': created_at_filter,
    }
    
    return render(request, 'list_user_receipts.html', context)



@csrf_exempt
@login_required
def download_receipt(request, receipt_id):
    try:
        receipt = Receipt.objects.get(id=receipt_id, user=request.user)
        items = receipt.items.all()
        company_name = request.user.profile.company_name if hasattr(request.user, 'profile') else "Your Company"
        phone_number = request.user.profile.phone_number if hasattr(request.user, 'profile') else ""
        secondary_email = request.user.profile.secondary_email if hasattr(request.user, 'profile') else ""


  # Get absolute file URI for avatar (logo)
        avatar_url = ''
        if hasattr(request.user, 'profile') and request.user.profile.avatar:
            avatar_path = pathlib.Path(settings.MEDIA_ROOT) / request.user.profile.avatar.name
            avatar_url = avatar_path.as_uri()

        currency_symbol = CURRENCY_SYMBOLS.get(receipt.currency, '$')

        # Generate receipt number and date
        import random
        from datetime import datetime
        receipt_number = f"#RCP{random.randint(100000, 999999)}"
        transaction_date = datetime.now().strftime("%d/%m/%Y %H:%M")

        template_str = receipt.template.html_content

        items_data = [
            {
                'name': item.name,
                'quantity': item.quantity,
                'unit_price': f"{item.unit_price:.2f}",
                'total_price': f"{item.total_price:.2f}",
            }
            for item in items
        ]

        # Signature URL
        signature_url = ''
        if receipt.signature and receipt.signature.name:
            signature_path = pathlib.Path(settings.MEDIA_ROOT) / receipt.signature.name
            signature_url = signature_path.as_uri()

        # Stamp URL and default assign
        stamp_url = ''
        if receipt.stamp and receipt.stamp.name:
            stamp_path = pathlib.Path(settings.MEDIA_ROOT) / receipt.stamp.name
            stamp_url = stamp_path.as_uri()
        else:
            if os.path.exists(DEFAULT_STAMP_PATH):
                with open(DEFAULT_STAMP_PATH, 'rb') as f:
                    receipt.stamp.save('default_stamp.png', File(f), save=True)
                stamp_path = pathlib.Path(settings.MEDIA_ROOT) / receipt.stamp.name
                stamp_url = stamp_path.as_uri()

        # Example: add tax_amount, discount_amount, subtotal if you calculate those

        context = Context({
            "items": items_data,
            "total_price": f"{receipt.total_price:.2f}",
            "subtotal": f"{(receipt.total_price - receipt.tax_amount + receipt.discount_amount):.2f}",
            "tax_amount": f"{receipt.tax_amount:.2f}" if receipt.tax_amount else None,
            "discount_amount": f"{receipt.discount_amount:.2f}" if receipt.discount_amount else None,
            "currency_symbol": currency_symbol,
            "company_name": company_name,
            "phone_number": phone_number,
            "secondary_email": secondary_email,
            "avatar_url": avatar_url, 
            "signature_url": signature_url,
            "stamp_url": stamp_url,
            "receipt_number": receipt_number,
            "transaction_date": transaction_date,   
            "client_name": receipt.client_name,
            "client_email": receipt.client_email,
            "client_phone": receipt.client_phone,
            "client_address": receipt.client_address,
            "client_company": receipt.client_company,
        })

        template_obj = Template(template_str)
        rendered_html = template_obj.render(context)

        pdf_file = HTML(string=rendered_html).write_pdf()

        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="receipt_{receipt.id}.pdf"'
        return response

    except Receipt.DoesNotExist:
        return JsonResponse({"error": "Receipt not found"}, status=404)
    except Exception as e:
        traceback.print_exc()
        return JsonResponse({"error": "Server error", "details": str(e)}, status=500)
    


DEFAULT_STAMP_PATH = os.path.join(settings.MEDIA_ROOT, 'default_stamp.png')


@csrf_exempt
@login_required
def invoice_templates_list(request):
    if request.method != "GET":
        return HttpResponseNotAllowed(["GET"])
    try:
        templates = InvoiceTemplate.objects.all()
        return render(request, 'invoice_templates_list.html', {'templates': templates})
    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})


@csrf_exempt
@login_required
def invoice_template_preview(request, template_id):
    if request.method != "GET":
        return HttpResponseNotAllowed(["GET"])
    
    try:
        # If ?invoice=1 in URL, treat template_id as invoice ID instead
        if request.GET.get('invoice'):
            # Preview invoice by ID
            invoice = get_object_or_404(Invoice, id=template_id, user=request.user)
            items = invoice.items.all()

            company_name = getattr(request.user.profile, 'company_name', "Your Company")
            phone_number = getattr(request.user.profile, 'phone_number', "")
            secondary_email = getattr(request.user.profile, 'secondary_email', "")
            avatar_url = ''
            
            if hasattr(request.user, 'profile') and getattr(request.user.profile, 'avatar', None):
                avatar_url = request.build_absolute_uri(request.user.profile.avatar.url)
             

            items_data = [
                {
                    'name': item.name,
                    'sku': item.sku or '',
                    'quantity': item.quantity,
                    'unit_price': f"{item.unit_price:.2f}",
                    'total': f"{item.total_price:.2f}",
                }
                for item in items
            ]

            context = {
                "items": items_data,
                "subtotal": f"{invoice.subtotal:.2f}",
                "tax_amount": f"{invoice.tax:.2f}" if invoice.tax and invoice.tax > 0 else None,
                "discount_amount": f"{invoice.discount:.2f}" if invoice.discount and invoice.discount > 0 else None,
                "grand_total": f"{invoice.total_price:.2f}",
                "company_name": company_name,
                "signature_url": invoice.signature.url if invoice.signature else '',
                "stamp_url": invoice.stamp.url if invoice.stamp else '',
                "invoice_number": invoice.invoice_number,
                "invoice_date": invoice.issue_date.strftime('%d/%m/%Y') if invoice.issue_date else '',
                "due_date": invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else '',
                "payment_terms": invoice.payment_terms,
                "notes": invoice.notes,
                "phone_number": phone_number,
                "secondary_email": secondary_email,
                "avatar_url": avatar_url,
                "currency_symbol": currency_symbol(invoice.currency),
                "client_name": invoice.client_name,
                "client_email": invoice.client_email,
                "client_phone": invoice.client_phone,
                "client_address": invoice.client_address,
                "current_date": invoice.created_at.strftime('%B %d, %Y'),
                "currency": invoice.currency,
            }

            template_str = invoice.template.html_content
            template_obj = Template(template_str)
            rendered_html = template_obj.render(Context(context))

            return HttpResponse(rendered_html)

        else:
            # Preview blank template by template ID
            template = get_object_or_404(InvoiceTemplate, pk=template_id)
            return render(request, 'invoice_template_preview.html', {'template': template})

    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})



@login_required
@csrf_exempt
def create_invoice(request):
    if request.method == 'GET':
        templates = InvoiceTemplate.objects.all()
        return render(request, 'create_invoice.html', {'templates': templates})

    if request.method == 'POST':
        try:
            # Extract form fields
            template_id = request.POST.get('template_id')
            client_name = request.POST.get('client_name')
            client_email = request.POST.get('client_email', '')
            client_address = request.POST.get('client_address', '')
            client_phone = request.POST.get('client_phone', '')
            invoice_number = request.POST.get('invoice_number')
            invoice_date_str = request.POST.get('invoice_date')
            due_date_str = request.POST.get('due_date')
            currency = request.POST.get('currency', 'USD')
            payment_terms = request.POST.get('payment_terms', '')
            notes = request.POST.get('notes', '')

            tax = request.POST.get('tax_amount', '0') or '0'
            discount = request.POST.get('discount_amount', '0') or '0'

            # Parse dates
            invoice_date = datetime.strptime(invoice_date_str, '%Y-%m-%d').date() if invoice_date_str else datetime.today().date()
            due_date = datetime.strptime(due_date_str, '%Y-%m-%d').date() if due_date_str else None

            # Validate and get template
            template = InvoiceTemplate.objects.get(id=template_id)

            # Auto-generate invoice number if empty
            if not invoice_number:
                invoice_number = f"INV-{invoice_id_generator()}"

            invoice = Invoice.objects.create(
                user=request.user,
                template=template,
                invoice_number=invoice_number,
                issue_date=invoice_date,
                due_date=due_date,
                client_name=client_name,
                client_email=client_email,
                client_address=client_address,
                client_phone=client_phone,
                currency=currency,
                payment_terms=payment_terms,
                notes=notes,
                tax=float(tax),
                discount=float(discount),
                subtotal=0,
                total_price=0
            )

            # Invoice items from form arrays
            items_names = request.POST.getlist('item_name[]')
            items_skus = request.POST.getlist('item_sku[]')
            items_qty = request.POST.getlist('item_quantity[]')
            items_unit_price = request.POST.getlist('item_unit_price[]')

            subtotal = 0
            for i in range(len(items_names)):
                name = items_names[i]
                sku = items_skus[i] if i < len(items_skus) else ''
                try:
                    quantity = int(items_qty[i]) if i < len(items_qty) else 1
                except ValueError:
                    quantity = 1
                try:
                    unit_price = float(items_unit_price[i]) if i < len(items_unit_price) else 0
                except ValueError:
                    unit_price = 0

                total_item_price = quantity * unit_price
                InvoiceItem.objects.create(
                    invoice=invoice,
                    name=name,
                    sku=sku,
                    quantity=quantity,
                    unit_price=unit_price,
                    total_price=total_item_price
                )
                subtotal += total_item_price

            invoice.subtotal = subtotal
            invoice.total_price = subtotal + invoice.tax - invoice.discount

            # Handle files
            signature = request.FILES.get('signature')
            stamp = request.FILES.get('stamp')

            if signature:
                invoice.signature = signature
            if stamp:
                invoice.stamp = stamp

            invoice.save()

            return redirect('list_user_invoices')

        except InvoiceTemplate.DoesNotExist:
            return render(request, 'error.html', {"error": "Invoice template not found"})
        except Exception:
            traceback.print_exc()
            return render(request, 'error.html', {"error": "Server error"})

    return HttpResponseNotAllowed(['GET', 'POST'])

    

from django.core.paginator import Paginator
from django.db.models import Q
from datetime import datetime
from django.utils.dateparse import parse_date
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.shortcuts import render

@login_required
def list_user_invoices(request):
    # Start with all invoices for the current logged-in user, ordered by creation date (newest first)
    invoices = Invoice.objects.filter(user=request.user).order_by('-created_at')
    
    # Date filtering
    created_at_filter = request.GET.get('created_at')
    issue_date_filter = request.GET.get('issue_date')
    
    if created_at_filter:
        try:
            # Parse the date string
            filter_date = parse_date(created_at_filter)
            if filter_date:
                # Filter invoices created on the specific date
                invoices = invoices.filter(created_at__date=filter_date)
        except ValueError:
            # If date parsing fails, ignore the filter
            pass
    
    if issue_date_filter:
        try:
            # Parse the date string
            filter_date = parse_date(issue_date_filter)
            if filter_date:
                # Filter invoices by issue date
                invoices = invoices.filter(issue_date=filter_date)
        except ValueError:
            # If date parsing fails, ignore the filter
            pass
    
    # Pagination - 10 invoices per page
    paginator = Paginator(invoices, 10)
    page_number = request.GET.get('page', 1)
    
    try:
        page_obj = paginator.get_page(page_number)
    except:
        page_obj = paginator.get_page(1)
    
    # If the request expects JSON data
    if request.headers.get('Accept') == 'application/json' or request.GET.get('format') == 'json':
        data = []
        for invoice in page_obj:
            symbol = CURRENCY_SYMBOLS.get(invoice.currency, '$')  # Default to '$' if currency is unknown
            data.append({
                'id': invoice.id,
                'invoice_number': invoice.invoice_number,
                'template_name': invoice.template.name if invoice.template else 'N/A',
                'currency_symbol': symbol,
                'template_id': invoice.template.id if invoice.template else None,
                'total_price': float(invoice.total_price),
                'issue_date': invoice.issue_date.isoformat(),
                'created_at': invoice.created_at.isoformat(),
                'updated_at': invoice.updated_at.isoformat(),
            })
        
        # Return JSON with pagination info
        return JsonResponse({
            'invoices': data,
            'pagination': {
                'current_page': page_obj.number,
                'total_pages': paginator.num_pages,
                'has_previous': page_obj.has_previous(),
                'has_next': page_obj.has_next(),
                'previous_page': page_obj.previous_page_number() if page_obj.has_previous() else None,
                'next_page': page_obj.next_page_number() if page_obj.has_next() else None,
                'total_invoices': paginator.count,
            }
        }, safe=False)
    
    # If not JSON, render the regular HTML page with pagination
    context = {
        'page_obj': page_obj,
        'invoices': page_obj,  # For backward compatibility
        'total_invoices': paginator.count,
        'created_at_filter': created_at_filter,
        'issue_date_filter': issue_date_filter,
    }
    
    return render(request, 'list_user_invoices.html', context)




@login_required
def download_invoice(request, invoice_id):
    try:
        invoice = Invoice.objects.get(id=invoice_id, user=request.user)
        items = invoice.items.all()
        company_name = request.user.profile.company_name if hasattr(request.user, 'profile') else "Your Company"
        phone_number = request.user.profile.phone_number if hasattr(request.user, 'profile') else ""
        secondary_email = request.user.profile.secondary_email if hasattr(request.user, 'profile') else ""
        
          # Get absolute file URI for avatar (logo)
        avatar_url = ''
        if hasattr(request.user, 'profile') and request.user.profile.avatar:
            avatar_path = pathlib.Path(settings.MEDIA_ROOT) / request.user.profile.avatar.name
            avatar_url = avatar_path.as_uri()


        # Load the HTML template from InvoiceTemplate.html_content field
        template_str = invoice.template.html_content

        items_data = [
            {
                'name': item.name,
                'sku': item.sku or '',
                'quantity': item.quantity,
                'unit_price': f"{item.unit_price:.2f}",
                'total': f"{item.total_price:.2f}",
            }
            for item in items
        ]

        signature_url = ''
        if invoice.signature and invoice.signature.name:
            signature_path = pathlib.Path(settings.MEDIA_ROOT) / invoice.signature.name
            signature_url = signature_path.as_uri()

        stamp_url = ''
        if invoice.stamp and invoice.stamp.name:
            stamp_path = pathlib.Path(settings.MEDIA_ROOT) / invoice.stamp.name
            stamp_url = stamp_path.as_uri()

        context = Context({
            "items": items_data,
            "subtotal": f"{invoice.subtotal:.2f}",
            "tax_amount": f"{invoice.tax:.2f}",
            "discount_amount": f"{invoice.discount:.2f}",
            "grand_total": f"{invoice.total_price:.2f}",
            "company_name": company_name,
            "signature_url": signature_url,
            "stamp_url": stamp_url,
            "invoice_number": invoice.invoice_number,
            "invoice_date": invoice.issue_date.strftime('%d/%m/%Y') if invoice.issue_date else '',
            "due_date": invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else '',
            "tax_amount": f"{invoice.tax:.2f}" if invoice.tax > 0 else None,
            "discount_amount": f"{invoice.discount:.2f}" if invoice.discount > 0 else None,          
            "payment_terms": invoice.payment_terms,
            "notes": invoice.notes,
            "company_name": company_name,
            "phone_number": phone_number,
            "secondary_email": secondary_email,
            "avatar_url": avatar_url, 
            "currency_symbol": currency_symbol(invoice.currency),
            "client_name": invoice.client_name,
            "client_email": invoice.client_email,
            "client_phone": invoice.client_phone,
            "client_address": invoice.client_address,
            "current_date": invoice.created_at.strftime('%B %d, %Y'),
            "currency": invoice.currency,
        })

        template_obj = Template(template_str)
        rendered_html = template_obj.render(context)

        pdf_file = HTML(string=rendered_html).write_pdf()

        response = HttpResponse(pdf_file, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename="invoice_{invoice.invoice_number}.pdf"'
        return response

    except Invoice.DoesNotExist:
        return render(request, 'error.html', {"error": "Invoice not found"})
    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})


def currency_symbol(currency_code):
    symbols = {
        'USD': '$',
        'EUR': '€',
        'NGN': '₦',
        'GBP': '£',
        'JPY': '¥',  # Japanese Yen
        'AUD': 'A$',  # Australian Dollar
        'CAD': 'C$',  # Canadian Dollar
        'INR': '₹',  # Indian Rupee
        'CNY': '¥',  # Chinese Yuan
        'MXN': '$',  # Mexican Peso
        'BRL': 'R$',  # Brazilian Real
        'ZAR': 'R',  # South African Rand
        'CHF': 'CHF',  # Swiss Franc
        'HKD': 'HK$',  # Hong Kong Dollar
        'SGD': 'S$',  # Singapore Dollar
        'MYR': 'RM',  # Malaysian Ringgit
        'PKR': '₨',  # Pakistani Rupee
        'SAR': 'ر.س',  # Saudi Riyal
        'TRY': '₺',  # Turkish Lira
        'AED': 'د.إ',  # UAE Dirham
        'SEK': 'kr',  # Swedish Krona
        'NOK': 'kr',  # Norwegian Krone
        'DKK': 'kr',  # Danish Krone
        'THB': '฿',  # Thai Baht
        'KES': 'KSh',  # Kenyan Shilling
    }
    return symbols.get(currency_code.upper(), currency_code)

def invoice_id_generator(length=8):
    # Generates a random alphanumeric string of given length
    letters_and_digits = string.ascii_uppercase + string.digits
    return ''.join(random.choice(letters_and_digits) for _ in range(length))

# 8. Search goods with filtering + get all categories
@csrf_exempt
def search_goods(request):
    if request.method != 'GET':
        return HttpResponseNotAllowed(['GET'])
    try:
        categories = Goods.objects.values_list('category', flat=True).distinct()

        name = request.GET.get('name')
        category = request.GET.get('category')
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')

        goods_qs = Goods.objects.all()

        if name:
            goods_qs = goods_qs.filter(name__icontains=name)
        if category:
            goods_qs = goods_qs.filter(category__iexact=category)
        if min_price:
            goods_qs = goods_qs.filter(price__gte=min_price)
        if max_price:
            goods_qs = goods_qs.filter(price__lte=max_price)

        # Prepare goods data for template rendering
        goods_data = []
        for g in goods_qs:
            contact_info = None
            if g.inventory and g.inventory.store:
                store = g.inventory.store
                email = store.owner.email if store.owner else None
                phone_number = None
                try:
                    if hasattr(store.owner, 'profile'):
                        phone_number = store.owner.profile.phone_number
                except Exception:
                    phone_number = None

                contact_info = {
                    "store_id": store.id,
                    "store_name": store.name,
                    "store_description": store.description,
                    "email": email,
                    "phone_number": phone_number,
                }

            goods_data.append({
                'id': g.id,
                'name': g.name,
                'price': g.price,
                'description': g.description,
                'image_url': g.image.url if g.image else None,
                'inventory_id': g.inventory.id if g.inventory else None,
                'created_at': g.created_at,
                'owner_id': g.owner.id,
                'category': g.category,
                'contact_store': contact_info,
            })

        context = {
            'categories': list(categories),
            'goods': goods_data,
            'filters': {
                'name': name or '',
                'category': category or '',
                'min_price': min_price or '',
                'max_price': max_price or '',
            }
        }
        return render(request, 'goods_search.html', context)

    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})


# 9. Get contact of seller by goodsId
@login_required
def get_seller_contact(request, goods_id):
    try:
        goods = get_object_or_404(Goods, pk=goods_id)
        seller = goods.owner
        profile = None
        try:
            profile = seller.profile
        except UserProfile.DoesNotExist:
            profile = None

        context = {
            "seller_id": seller.id,
            "username": seller.username,
            "email": seller.email,
            "phone_number": profile.phone_number if profile else None,
            "company_name": profile.company_name if profile else None,
            "website_url": profile.website_url if profile else None,
            "goods": goods,
        }
        return render(request, 'seller_contact.html', context)
    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})



# 10. Buy goods (contact seller) — e.g., send email notification to seller with buyer info@csrf_exempt
@csrf_exempt
@login_required
def contact_seller_buy_goods(request, goods_id):
    if request.method != 'POST':
        return HttpResponseNotAllowed(['POST'])
    try:
        goods = get_object_or_404(Goods, pk=goods_id)
        seller = goods.owner

        data = json.loads(request.body)
        buyer_message = data.get('message', '').strip()
        buyer_email = request.user.email
        buyer_name = request.user.get_full_name() or request.user.username

        seller_profile = None
        try:
            seller_profile = seller.profile
        except UserProfile.DoesNotExist:
            seller_profile = None

        seller_phone = seller_profile.phone_number if seller_profile else None
        seller_company = seller_profile.company_name if seller_profile else None

        email_subject = f'Interest in your goods: {goods.name}'
        email_body_html = f"""
        <p>Hello {seller.get_full_name() or seller.username},</p>
        <p><strong>{buyer_name}</strong> ({buyer_email}) is interested in purchasing your item <em>"{goods.name}"</em>.</p>
        <p><strong>Message from buyer:</strong><br>{buyer_message or "<i>No additional message provided.</i>"}</p>
        <p>Please get in touch with the buyer to proceed.</p>
        <hr>
        <p><strong>Seller Contact Info:</strong><br>
        Email: {seller.email}<br>
        {'Phone: ' + seller_phone if seller_phone else ''}<br>
        {'Company: ' + seller_company if seller_company else ''}
        </p>
        <p>Regards,<br>Your Marketplace Team</p>
        """

        send_mail(
            subject=email_subject,
            message='',
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[seller.email],
            fail_silently=False,
            html_message=email_body_html
        )

        Notification.objects.create(
            user=seller,
            message=f'{buyer_name} has contacted you regarding "{goods.name}".'
        )

        # Return success page or redirect
        return render(request, 'contact_success.html', {"message": "Seller contacted successfully."})

    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})



# Notifications list

@login_required
def get_user_notifications(request):
    if request.method != 'GET':
        return HttpResponseNotAllowed(['GET'])
    try:
        notifications = Notification.objects.filter(user=request.user).order_by('-created_at')
        context = {"notifications": notifications}
        return render(request, 'notifications.html', context)
    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})

# Mark notifications read
@csrf_exempt
@login_required
def mark_notification_read(request):
    if request.method != 'POST':
        return HttpResponseNotAllowed(['POST'])
    try:
        data = json.loads(request.body)
        notification_id = data.get('notification_id')

        if not notification_id:
            return JsonResponse({"error": "notification_id is required"}, status=400)

        try:
            notification = Notification.objects.get(pk=notification_id, user=request.user)
            notification.read = True
            notification.save()
            return JsonResponse({"message": f"Notification {notification_id} marked as read."})
        except Notification.DoesNotExist:
            return JsonResponse({"error": "Notification not found"}, status=404)

    except json.JSONDecodeError:
        return JsonResponse({"error": "Invalid JSON format"}, status=400)
    except Exception:
        traceback.print_exc()
        return JsonResponse({"error": "Server error"}, status=500)




# Utility function to create notification (use it in your other views)
def create_notification(user, message):
    Notification.objects.create(user=user, message=message)



# User stores list view (renders HTML page)
@login_required
def user_stores_list(request):
    try:
        stores = Store.objects.filter(Q(owner=request.user) | Q(authorized_users=request.user)).distinct()
        context = {"stores": stores}
        return render(request, 'user_stores.html', context)
    except Exception:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error"})



# --- Good Management Views ---

@method_decorator(csrf_exempt, name='dispatch')
@method_decorator(login_required, name='dispatch')
class ManagedGoodView(View):
    def get(self, request):
        goods = ManagedGood.objects.filter(owner=request.user)
        data = [
            {
                "id": g.id,
                "name": g.name,
                "description": g.description,
                "price": float(g.price),
                "quantity": g.quantity,
                "category": g.category,
                "total_value": float(g.total_value())
            }
            for g in goods
        ]
        return JsonResponse({"goods": data}, status=200)

    def post(self, request):
        try:
            data = json.loads(request.body)
            good = ManagedGood.objects.create(
                owner=request.user,
                name=data['name'],
                description=data.get('description', ''),
                price=data['price'],
                quantity=data['quantity'],
                category=data.get('category', '')
            )
            return JsonResponse({"message": "Good created", "id": good.id}, status=201)
        except Exception as e:
            return JsonResponse({"error": str(e)}, status=400)

@method_decorator(csrf_exempt, name='dispatch')
@method_decorator(login_required, name='dispatch')
class SingleGoodView(View):
    def get(self, request, good_id):
        good = get_object_or_404(ManagedGood, id=good_id, owner=request.user)
        data = {
            "id": good.id,
            "name": good.name,
            "description": good.description,
            "price": float(good.price),
            "quantity": good.quantity,
            "category": good.category,
            "units_sold": good.units_sold
        }
        return JsonResponse(data, status=200)

    def put(self, request, good_id):
        good = get_object_or_404(ManagedGood, id=good_id, owner=request.user)
        try:
            data = json.loads(request.body)
            good.name = data.get('name', good.name)
            good.description = data.get('description', good.description)
            good.price = data.get('price', good.price)
            good.quantity = data.get('quantity', good.quantity)
            good.category = data.get('category', good.category)
            good.save()
            return JsonResponse({"message": "Good updated successfully"}, status=200)
        except json.JSONDecodeError:
            return JsonResponse({"error": "Invalid JSON provided"}, status=400)
        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)

    def delete(self, request, good_id):
        good = get_object_or_404(ManagedGood, id=good_id, owner=request.user)
        good.delete()
        return JsonResponse({"message": "Good deleted successfully"}, status=200)

@csrf_exempt
@login_required
def restock_good(request, good_id):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            qty = int(data.get('quantity', 0))
            if qty <= 0:
                return JsonResponse({"error": "Quantity must be positive"}, status=400)

            good = ManagedGood.objects.get(id=good_id, owner=request.user)
            good.quantity += qty
            good.save()
            return JsonResponse({"message": f"{qty} items restocked", "new_quantity": good.quantity})
        except Exception as e:
            return JsonResponse({"error": str(e)}, status=400)

@csrf_exempt
@login_required
def sell_good(request, good_id):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            qty = int(data.get('quantity', 0))
            if qty <= 0:
                return JsonResponse({"error": "Quantity must be positive"}, status=400)

            good = ManagedGood.objects.get(id=good_id, owner=request.user)
            if good.quantity < qty:
                return JsonResponse({"error": "Not enough stock to sell"}, status=400)

            good.quantity -= qty
            good.units_sold += qty
            good.save()

            notification = None
            if good.quantity == 0:
                notification = f"You are out of stock for '{good.name}'. Please restock."

            return JsonResponse({
                "message": f"{qty} items sold.",
                "remaining_quantity": good.quantity,
                "units_sold": good.units_sold,
                "notification": notification
            })
        except Exception as e:
            return JsonResponse({"error": str(e)}, status=400)

@login_required
def net_total_value(request):
    total = ManagedGood.objects.filter(owner=request.user).aggregate(
        total=Sum(F('price') * F('quantity'))
    )['total'] or 0
    return JsonResponse({"net_total_value": float(total)}, status=200)


# Updated Goods endpoints 
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.conf import settings
import json
import os
from django.db.models import Q
import traceback
from .models import Store, Category, Goods
from django.contrib import messages

@csrf_exempt
@login_required
def goods_list_create(request):
    """
    Handle both listing and creating goods
    """
    try:
        if request.method == 'POST':
            # Get form data
            name = request.POST.get('name', '').strip()
            price = request.POST.get('price')
            quantity = request.POST.get('quantity', 0)
            description = request.POST.get('description', '').strip()
            images = request.FILES.getlist('images')
            store_id = request.POST.get('store_id')
            category_id = request.POST.get('category_id')
            currency = request.POST.get('currency', 'NGN')
            is_used = request.POST.get('is_used') == 'on'
            available_for_delivery = request.POST.get('available_for_delivery') == 'on'
            delivery_type = request.POST.get('delivery_type') if available_for_delivery else None
            available_for_bulk_sales = request.POST.get('available_for_bulk_sales') == 'on'

            # Validation
            errors = {}
            if not name:
                errors['name'] = ['Name is required']
            if not price:
                errors['price'] = ['Price is required']
            else:
                try:
                    price = float(price)
                    if price < 0:
                        errors['price'] = ['Price must be positive']
                except ValueError:
                    errors['price'] = ['Invalid price format']

            if not store_id:
                errors['store_id'] = ['Store is required']

            try:
                quantity = int(quantity)
                if quantity < 0:
                    errors['quantity'] = ['Quantity must be non-negative']
            except ValueError:
                errors['quantity'] = ['Invalid quantity format']

            # If there are validation errors, return response
            if errors:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'error': 'Validation failed', 'errors': errors}, status=400)
                else:
                    context = {
                        'error': 'Please correct the errors below',
                        'errors': errors,
                        'goods': Goods.objects.filter(owner=request.user),
                        'categories': Category.objects.filter(owner=request.user),
                        'stores': Store.objects.filter(Q(owner=request.user) | Q(authorized_users=request.user)).distinct()
                    }
                    return render(request, 'goods_list.html', context)

            # Get store and verify access (owner or authorized user)
            try:
                store = Store.objects.get(pk=store_id)
                if store.owner != request.user and request.user not in store.authorized_users.all():
                    raise PermissionError("You are not authorized to manage this store.")
            except Store.DoesNotExist:
                errors['store_id'] = ['Selected store does not exist.']
                context = {
                    'error': 'Please correct the errors below',
                    'errors': errors,
                    'goods': Goods.objects.filter(owner=request.user),
                    'categories': Category.objects.filter(owner=request.user),
                    'stores': Store.objects.filter(Q(owner=request.user) | Q(authorized_users=request.user)).distinct()
                }
                return render(request, 'goods_list.html', context)
            except PermissionError as e:
                errors['store_id'] = [str(e)]
                context = {
                    'error': 'Permission denied.',
                    'errors': errors,
                    'goods': Goods.objects.filter(owner=request.user),
                    'categories': Category.objects.filter(owner=request.user),
                    'stores': Store.objects.filter(Q(owner=request.user) | Q(authorized_users=request.user)).distinct()
                }
                return render(request, 'goods_list.html', context)

            # Get category if provided
            category = None
            if category_id:
                try:
                    category = Category.objects.get(pk=category_id, owner=request.user)
                except Category.DoesNotExist:
                    errors['category_id'] = ['Category not found or not owned by you']

            # Create the goods
            goods = Goods.objects.create(
                owner=request.user,
                store=store,
                category=category,
                name=name,
                price=price,
                quantity=quantity,
                currency=currency,
                description=description,
                is_used=is_used,
                available_for_delivery=available_for_delivery,
                delivery_type=delivery_type,
                available_for_bulk_sales=available_for_bulk_sales
            )

            # Handle image uploads
            if images:
                image_urls = []
                for i, img in enumerate(images[:5]):  # Limit to 5 images
                    filename = f"goods_images/{goods.id}_{i}_{img.name}"
                    saved_path = default_storage.save(filename, img)
                    image_urls.append(saved_path)

                goods.images = image_urls
                goods.save()

                # Log image addition activity
                GoodsActivity.log_activity(
                    goods=goods,
                    activity_type='image_added',
                    title='Images Added',
                    description=f'{len(image_urls)} image(s) added to product.',
                    user=request.user,
                    metadata={'image_count': len(image_urls)}
                )

            # Log creation activity
            GoodsActivity.log_activity(
                goods=goods,
                activity_type='created',
                title='Product Created',
                description=f'Product "{goods.name}" was created with initial stock of {goods.quantity} units.',
                user=request.user,
                metadata={
                    'initial_price': str(goods.price),
                    'initial_quantity': goods.quantity,
                    'category': goods.category.name if goods.category else None,
                    'store': goods.store.name if goods.store else None
                }
            )

            # Handle AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'message': 'Goods created successfully!',
                    'goods_id': goods.id,
                    'redirect_url': f'/stores/{store.id}/'
                })
            else:
                messages.success(request, 'Goods created successfully!')
                return redirect('store_detail', store_id=store.id)

        elif request.method == 'GET':
            # Handle GET requests for listing goods
            goods_list = Goods.objects.filter(owner=request.user).select_related('store', 'category')

            # Apply filters
            category_id = request.GET.get('category')
            search = request.GET.get('q')
            is_used = request.GET.get('is_used')
            available_for_delivery = request.GET.get('available_for_delivery')
            store_id = request.GET.get('store')

            if category_id:
                goods_list = goods_list.filter(category_id=category_id)
            if search:
                goods_list = goods_list.filter(name__icontains=search)
            if is_used in ['true', 'false']:
                goods_list = goods_list.filter(is_used=(is_used == 'true'))
            if available_for_delivery in ['true', 'false']:
                goods_list = goods_list.filter(available_for_delivery=(available_for_delivery == 'true'))
            if store_id:
                goods_list = goods_list.filter(store_id=store_id)

            context = {
                'goods': goods_list,
                'categories': Category.objects.filter(owner=request.user),
                'stores': Store.objects.filter(Q(owner=request.user) | Q(authorized_users=request.user)).distinct()
            }
            return render(request, 'goods_list.html', context)

        return JsonResponse({'error': 'Method not allowed'}, status=405)

    except Exception as e:
        traceback.print_exc()
        error_message = f"Server error occurred: {str(e)}"

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({'error': error_message}, status=500)
        else:
            context = {
                'error': error_message,
                'goods': Goods.objects.filter(owner=request.user),
                'categories': Category.objects.filter(owner=request.user),
                'stores': Store.objects.filter(Q(owner=request.user) | Q(authorized_users=request.user)).distinct()
            }
            return render(request, 'goods_list.html', context)


import time
from django.core.files.storage import default_storage

@csrf_exempt
@login_required
def goods_detail(request, goods_id):
    try:
        goods = get_object_or_404(Goods, pk=goods_id, owner=request.user)

        if request.method == 'GET':
            # Get categories for the edit form
            categories = Category.objects.filter(owner=request.user)

            # Currency symbols mapping
            CURRENCY_SYMBOLS = {
                'NGN': '₦',
                'USD': '$',
                'EUR': '€',
                'GBP': '£',
            }

            # Get contact info if needed
            contact_info = None
            if hasattr(goods, 'store') and goods.store:
                contact_info = {
                    'store_name': goods.store.name,
                    'store_description': getattr(goods.store, 'description', ''),
                    'email': getattr(goods.store, 'email', ''),
                    'phone_number': getattr(goods.store, 'phone_number', ''),
                    'store_id': goods.store.id
                }

            return render(request, 'goods_detail.html', {
                'goods': goods,
                'categories': categories,
                'contact_info': contact_info,
                'currency_symbol': CURRENCY_SYMBOLS.get(goods.currency.upper(), goods.currency)
            })

        elif request.method == 'POST':
            if goods.owner != request.user:
                return JsonResponse({"error": "Only the owner can update the goods."}, status=403)

            # Handle form data from the edit modal
            goods.name = request.POST.get('name', goods.name)

            # Handle price conversion
            try:
                price = request.POST.get('price')
                if price:
                    goods.price = float(price)
            except (ValueError, TypeError):
                return JsonResponse({"error": "Invalid price format"}, status=400)

            goods.description = request.POST.get('description', goods.description)

            # Handle category
            category_id = request.POST.get('category_id')
            if category_id:
                try:
                    category = get_object_or_404(Category, pk=category_id, owner=request.user)
                    goods.category = category
                except:
                    pass
            else:
                goods.category = None

            # Handle checkboxes
            goods.is_used = request.POST.get('is_used') == 'on'
            goods.available_for_delivery = request.POST.get('available_for_delivery') == 'on'
            goods.available_for_bulk_sales = request.POST.get('available_for_bulk_sales') == 'on'

            # Handle delivery type
            if goods.available_for_delivery:
                goods.delivery_type = request.POST.get('delivery_type', '')
            else:
                goods.delivery_type = None

            # Handle image uploads
            if 'images' in request.FILES:
                uploaded_files = request.FILES.getlist('images')
                new_images = []

                for uploaded_file in uploaded_files:
                    filename = f"goods_images/{goods.id}_{int(time.time())}_{uploaded_file.name}"
                    saved_path = default_storage.save(filename, uploaded_file)
                    new_images.append(saved_path)

                # Delete old images
                if goods.images:
                    for image_path in goods.images:
                        if default_storage.exists(image_path):
                            default_storage.delete(image_path)

                goods.images = new_images

            goods.save()
            return JsonResponse({"message": "Product updated successfully!"}, status=200)

        elif request.method == 'DELETE':
            if goods.owner != request.user:
                return JsonResponse({"error": "Only the owner can delete the goods."}, status=403)

            if goods.images:
                for image_path in goods.images:
                    if default_storage.exists(image_path):
                        default_storage.delete(image_path)

            goods.delete()
            return JsonResponse({"message": "Product deleted successfully!"}, status=200)

        return JsonResponse({"error": "Method not allowed"}, status=405)

    except Goods.DoesNotExist:
        return JsonResponse({"error": "Product not found"}, status=404)
    except Exception as e:
        traceback.print_exc()
        return JsonResponse({"error": "Server error occurred", "details": str(e)}, status=500)


from django.shortcuts import render
from django.http import JsonResponse
from .models import Goods, Category
import traceback

@require_GET
def public_goods_list(request):
    try:
        goods_qs = Goods.objects.select_related('category', 'store', 'country', 'state').all()

        # Get filter parameters
        search_query = request.GET.get('q') or request.GET.get('name', '').strip()
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')
        category = request.GET.get('category')
        country_id = request.GET.get('country')
        state_id = request.GET.get('state')
        star_rating = request.GET.get('star_rating')
        is_used = request.GET.get('is_used')
        available_for_delivery = request.GET.get('available_for_delivery')

        # Get all categories and countries for dropdowns
        categories = Category.objects.all()
        # Only show countries where users have signed up
        countries = Country.objects.filter(userprofile__isnull=False).distinct()
        states = State.objects.filter(userprofile__isnull=False).distinct()

        # Apply search filter (enhanced to search multiple fields)
        if search_query:
            goods_qs = goods_qs.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(store__name__icontains=search_query)
            )

        # Apply price filters
        if min_price:
            try:
                goods_qs = goods_qs.filter(price__gte=Decimal(min_price))
            except (ValueError, InvalidOperation):
                pass
        if max_price:
            try:
                goods_qs = goods_qs.filter(price__lte=Decimal(max_price))
            except (ValueError, InvalidOperation):
                pass

        # Apply category filter
        if category:
            goods_qs = goods_qs.filter(category__name__iexact=category)

        # Apply country filter (filter by user's signup country)
        if country_id and country_id.isdigit():
            goods_qs = goods_qs.filter(owner__profile__country_id=country_id)
            # Filter states for the selected country
            states = states.filter(country_id=country_id)

        # Apply state filter (filter by user's signup state)
        if state_id and state_id.isdigit():
            goods_qs = goods_qs.filter(owner__profile__state_id=state_id)

        # Apply star rating filter
        if star_rating and star_rating.isdigit():
            goods_qs = goods_qs.filter(star_rating=int(star_rating))
        if is_used in ['true', 'false']:
            goods_qs = goods_qs.filter(is_used=(is_used == 'true'))
        if available_for_delivery in ['true', 'false']:
            goods_qs = goods_qs.filter(available_for_delivery=(available_for_delivery == 'true'))
            
                    # Currency mapping
        CURRENCY_SYMBOLS = {
            'NGN': '₦',
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
        }

        # Add pagination
        from django.core.paginator import Paginator
        paginator = Paginator(goods_qs, 12)  # Show 12 goods per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        goods_data = []
        for g in page_obj:
            contact_info = None
            if g.store:
                store = g.store
                email = store.owner.email if store.owner else None

                # Safely get phone number from profile
                phone_number = None
                try:
                    if store.owner and hasattr(store.owner, 'profile'):
                        phone_number = getattr(store.owner.profile, 'phone_number', None)
                except Exception:
                    phone_number = None

                contact_info = {
                    "store_id": store.id,
                    "store_name": store.name,
                    "store_description": store.description,
                    "email": email,
                    "phone_number": phone_number,
                }

            goods_data.append({
                'id': g.id,
                'name': g.name,
                'price': str(g.price),
                'description': g.description,
                'image_url': g.images[0] if g.images else None,
                'store_id': g.store.id if g.store else None,
                'created_at': g.created_at,
                'owner_id': g.owner.id,
                'category': g.category.name if g.category else None,
                'is_used': g.is_used,
                'available_for_delivery': g.available_for_delivery,
                'currency': g.currency,
                'currency_symbol': CURRENCY_SYMBOLS.get(g.currency.upper(), g.currency),
                'delivery_type': g.delivery_type,
                'available_for_bulk_sales': g.available_for_bulk_sales,
                'contact_store': contact_info,
                'share_link': get_safe_share_link(request, f"/public/goods/detail/{g.id}"),
                # Enhanced fields
                'country': getattr(g, 'country', None) and g.country.name,
                'country_id': getattr(g, 'country', None) and g.country.id,
                'state': getattr(g, 'state', None) and g.state.name,
                'state_id': getattr(g, 'state', None) and g.state.id,
                'star_rating': getattr(g, 'star_rating', None),
                'star_rating_display': getattr(g, 'get_star_rating_display', lambda: None)(),
                'sku': getattr(g, 'sku', None),
                'quantity': getattr(g, 'quantity', 0),
                'track_inventory': getattr(g, 'track_inventory', False),
                'condition_display': 'Used' if g.is_used else 'New',
            })

        # Get properties for sale
        properties_qs = Property.objects.filter(for_sale=True).select_related('owner').order_by('-created_at')

        # Apply search filter to properties
        if search_query:
            properties_qs = properties_qs.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(sale_description__icontains=search_query) |
                Q(location__icontains=search_query)
            )

        # Apply price filters to properties
        if min_price:
            try:
                properties_qs = properties_qs.filter(sale_price__gte=Decimal(min_price))
            except (ValueError, InvalidOperation):
                pass
        if max_price:
            try:
                properties_qs = properties_qs.filter(sale_price__lte=Decimal(max_price))
            except (ValueError, InvalidOperation):
                pass

        # Apply country filter to properties (filter by owner's signup country)
        if country_id and country_id.isdigit():
            properties_qs = properties_qs.filter(owner__profile__country_id=country_id)

        # Apply state filter to properties (filter by owner's signup state)
        if state_id and state_id.isdigit():
            properties_qs = properties_qs.filter(owner__profile__state_id=state_id)

        # Prepare properties data
        properties_data = []
        for prop in properties_qs[:6]:  # Show up to 6 properties
            # Get owner contact info
            contact_info = None
            if prop.owner:
                email = prop.contact_email or prop.owner.email
                phone_number = prop.contact_phone

                # Try to get phone from profile if not set
                if not phone_number:
                    try:
                        if hasattr(prop.owner, 'profile'):
                            phone_number = getattr(prop.owner.profile, 'phone_number', None)
                    except Exception:
                        phone_number = None

                contact_info = {
                    "owner_name": prop.owner.username,
                    "email": email,
                    "phone_number": phone_number,
                }

            properties_data.append({
                'id': prop.id,
                'name': prop.name,
                'description': prop.description,
                'sale_description': prop.sale_description,
                'sale_price': prop.sale_price,
                'is_negotiable': prop.is_negotiable,
                'property_type': prop.property_type,
                'location': prop.location,
                'images': prop.images if prop.images else [],
                'contact_phone': prop.contact_phone,
                'contact_email': prop.contact_email,
                'owner': prop.owner.username,
                'owner_id': prop.owner.id,
                'created_at': prop.created_at,
                'contact_info': contact_info,
                'share_link': get_safe_share_link(request, f"/property-sale/{prop.id}"),
            })

        # Prepare context with enhanced filter data
        context = {
            'goods': goods_data,
            'properties_for_sale': properties_data,
            'page_obj': page_obj,
            'categories': categories,
            'countries': countries,
            'states': states,
            'filters': {
                'search_query': search_query,
                'min_price': min_price,
                'max_price': max_price,
                'category': category,
                'country_id': country_id,
                'state_id': state_id,
                'star_rating': star_rating,
                'is_used': is_used,
                'available_for_delivery': available_for_delivery,
            },
            'star_ratings': [(i, f'{"⭐" * i} {i} Star{"s" if i > 1 else ""}') for i in range(1, 6)],
        }

        return render(request, 'public_goods_list.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error", "details": str(e)})


@require_GET
def public_seller_store(request, seller_id):
    """
    Display all goods from all stores of a specific seller
    This is a public store view that can be shared
    """
    try:
        # Get the seller by ID
        try:
            seller = User.objects.get(id=seller_id)
        except User.DoesNotExist:
            return render(request, 'error.html', {"error": "Seller not found"})

        # Get all goods from all stores owned by this seller
        goods_qs = Goods.objects.filter(owner=seller).select_related('category', 'store', 'owner')

        # Apply filters
        search_query = request.GET.get('q', '').strip()
        category = request.GET.get('category')  # This will be category ID from template
        store = request.GET.get('store')  # Add store filter
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')
        star_rating = request.GET.get('star_rating')
        is_used = request.GET.get('is_used')
        available_for_delivery = request.GET.get('available_for_delivery')

        # Search filter
        if search_query:
            goods_qs = goods_qs.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(store__name__icontains=search_query)
            )

        # Price filters
        if min_price:
            try:
                goods_qs = goods_qs.filter(price__gte=Decimal(min_price))
            except (ValueError, InvalidOperation):
                pass

        if max_price:
            try:
                goods_qs = goods_qs.filter(price__lte=Decimal(max_price))
            except (ValueError, InvalidOperation):
                pass

        # Category filter - filter by category ID instead of name
        if category:
            try:
                goods_qs = goods_qs.filter(category__id=int(category))
            except (ValueError, TypeError):
                pass

        # Store filter - add this new filter
        if store:
            try:
                goods_qs = goods_qs.filter(store__id=int(store))
            except (ValueError, TypeError):
                pass

        # Star rating filter
        if star_rating and star_rating.isdigit():
            goods_qs = goods_qs.filter(star_rating=int(star_rating))

        # Condition filters
        if is_used in ['true', 'false']:
            goods_qs = goods_qs.filter(is_used=(is_used == 'true'))

        if available_for_delivery in ['true', 'false']:
            goods_qs = goods_qs.filter(available_for_delivery=(available_for_delivery == 'true'))

        # Get categories for this seller
        categories = Category.objects.filter(owner=seller)

        # Get seller's stores
        stores = Store.objects.filter(owner=seller)

        # Prepare goods data
        goods_data = []
        for goods in goods_qs:
            goods_data.append({
                'id': goods.id,
                'name': goods.name,
                'price': goods.price,
                'currency_symbol': goods.currency_symbol,
                'description': goods.description,
                'category': goods.category.name if goods.category else 'Uncategorized',
                'store_name': goods.store.name if goods.store else 'No Store',
                'first_image_url': goods.first_image_url,
                'all_image_urls': goods.all_image_urls,
                'star_rating': goods.star_rating,
                'star_rating_display': goods.get_star_rating_display(),
                'is_used': goods.is_used,
                'available_for_delivery': goods.available_for_delivery,
                'created_at': goods.created_at,
                'get_absolute_url': goods.get_absolute_url(),
            })

        # Check if this is the seller's own store
        is_own_store = request.user.is_authenticated and request.user == seller

        # Get properties for sale by this seller
        properties_for_sale = Property.objects.filter(
            owner=seller,
            for_sale=True
        ).order_by('-created_at')[:6]  # Show up to 6 properties

        # Prepare properties data
        properties_data = []
        for prop in properties_for_sale:
            properties_data.append({
                'id': prop.id,
                'name': prop.name,
                'description': prop.description,
                'sale_description': prop.sale_description,
                'sale_price': prop.sale_price,
                'is_negotiable': prop.is_negotiable,
                'property_type': prop.property_type,
                'location': prop.location,
                'images': prop.images if prop.images else [],
                'contact_phone': prop.contact_phone,
                'contact_email': prop.contact_email,
                'created_at': prop.created_at,
            })

        context = {
            'seller': seller,
            'goods': goods_data,
            'properties_for_sale': properties_data,
            'categories': categories,
            'stores': stores,
            'is_own_store': is_own_store,
            'total_goods': len(goods_data),
            'total_stores': stores.count(),
            'total_properties_for_sale': len(properties_data),
            'filters': {
                'search_query': search_query,
                'category': category,  # Keep as ID for proper selection
                'store': store,  # Add store filter to context
                'min_price': min_price,
                'max_price': max_price,
                'star_rating': star_rating,
                'is_used': is_used,
                'available_for_delivery': available_for_delivery,
            },
            'star_ratings': [(i, f'{"⭐" * i} {i} Star{"s" if i > 1 else ""}') for i in range(1, 6)],
        }

        return render(request, 'public_seller_store.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error", "details": str(e)})  


@require_GET
def public_goods_detail(request, goods_id):
    try:
        g = get_object_or_404(Goods, pk=goods_id)
        contact_info = None
        if g.store:
            store = g.store
            email = store.owner.email if store.owner else None
            phone_number = getattr(store.owner.profile, 'phone_number', None) if hasattr(store.owner, 'profile') else None

            contact_info = {
                "store_id": store.id,
                "store_name": store.name,
                "store_description": store.description,
                "email": email,
                "phone_number": phone_number,
            }

        share_link = get_safe_share_link(request, f"/public/goods/detail/{g.id}")

        goods_data = {
            "id": g.id,
            "name": g.name,
            "price": str(g.price),
            "currency": g.currency,
            "currency_symbol": CURRENCY_SYMBOLS.get(g.currency.upper(), g.currency),
            "description": g.description,
            "store_name": g.store.name,
            "images": g.images,
            "share_link": share_link,
            "is_used": g.is_used,
            "available_for_delivery": g.available_for_delivery,
            "available_for_bulk_sales": g.available_for_bulk_sales,
            "delivery_type": g.delivery_type,
            "created_at": g.created_at,
            "category": getattr(g, 'category', None), 
            "quantity": g.quantity, 
            
        }

        return render(request, 'public_goods_detail.html', {'goods': goods_data, 'contact_info': contact_info})

    except Goods.DoesNotExist:
        return render(request, 'error.html', {"error": "Goods not found"})
    except Exception as e:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error", "details": str(e)})


@csrf_exempt
@login_required
def update_goods(request, goods_id):
    try:
        goods = get_object_or_404(Goods, pk=goods_id, owner=request.user)

        if request.method == 'PUT':
            try:
                data = json.loads(request.body)

                # Store old values for activity logging
                old_name = goods.name
                old_price = goods.price
                old_description = goods.description

                # Update fields
                goods.name = data.get('name', goods.name)
                goods.price = data.get('price', goods.price)
                goods.description = data.get('description', goods.description)
                goods.save()

                # Log update activities
                changes = []
                if old_name != goods.name:
                    changes.append(f"Name: '{old_name}' → '{goods.name}'")
                if old_price != goods.price:
                    changes.append(f"Price: ${old_price} → ${goods.price}")
                    # Log specific price update activity
                    GoodsActivity.log_activity(
                        goods=goods,
                        activity_type='price_updated',
                        title='Price Updated',
                        description=f'Price changed from ${old_price} to ${goods.price}',
                        user=request.user,
                        old_value=str(old_price),
                        new_value=str(goods.price)
                    )
                if old_description != goods.description:
                    changes.append(f"Description updated")
                    # Log description update activity
                    GoodsActivity.log_activity(
                        goods=goods,
                        activity_type='description_updated',
                        title='Description Updated',
                        description=f'Product description was updated',
                        user=request.user,
                        old_value=old_description,
                        new_value=goods.description
                    )

                # Log general update activity if any changes were made
                if changes:
                    GoodsActivity.log_activity(
                        goods=goods,
                        activity_type='updated',
                        title='Product Updated',
                        description=f'Product updated: {", ".join(changes)}',
                        user=request.user,
                        metadata={'changes': changes}
                    )

                return JsonResponse({"message": "Good updated successfully!"}, status=200)
            except json.JSONDecodeError:
                return JsonResponse({"error": "Invalid data format"}, status=400)

        return JsonResponse({"error": "Method not allowed"}, status=405)

    except Goods.DoesNotExist:
        return JsonResponse({"error": "Good not found"}, status=404)
    except Exception as e:
        return JsonResponse({"error": "Server error", "details": str(e)}, status=500)

# views.py
from django.shortcuts import render, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.http import JsonResponse
from django.db.models import Count
from django.views.decorators.http import require_http_methods
from django.utils import timezone
import json
from .models import Category, Goods

@csrf_exempt
@login_required
def create_category(request):
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        if not name:
            return JsonResponse({'error': 'Category name is required'}, status=400)
        
        if Category.objects.filter(owner=request.user, name__iexact=name).exists():
            return JsonResponse({'error': 'Category already exists'}, status=400)
        
        category = Category.objects.create(owner=request.user, name=name)
        
        # Create response data with safe field access
        category_data = {
            'id': category.id,
            'name': category.name,
            'good_count': 0
        }
        
        # Only add created_at if the field exists
        if hasattr(category, 'created_at') and category.created_at:
            category_data['created_at'] = category.created_at.strftime('%Y-%m-%d')
        else:
            category_data['created_at'] = timezone.now().strftime('%Y-%m-%d')
        
        return JsonResponse({
            'message': 'Category created successfully',
            'category': category_data
        }, status=201)
    
    # Render the create category form
    return render(request, 'create_category.html')

@login_required
def list_categories(request):
    # Annotate categories with good count
    categories = Category.objects.filter(owner=request.user).annotate(
        good_count=Count('goods')  # Match the related_name in your Category model
    ).order_by('-id')  # Order by ID instead of created_at if created_at doesn't exist
    
    return render(request, 'list_categories.html', {'categories': categories})

@csrf_exempt
@login_required
@require_http_methods(["POST"])
def update_category(request, category_id):
    category = get_object_or_404(Category, id=category_id, owner=request.user)
    
    name = request.POST.get('name', '').strip()
    if not name:
        return JsonResponse({'error': 'Category name is required'}, status=400)
    
    # Check if another category with this name exists (excluding current one)
    if Category.objects.filter(owner=request.user, name__iexact=name).exclude(id=category_id).exists():
        return JsonResponse({'error': 'Category already exists'}, status=400)
    
    category.name = name
    category.save()
    
    # Create response data with safe field access
    category_data = {
        'id': category.id,
        'name': category.name,
        'good_count': category.goods.count()
    }
    
    # Only add created_at if the field exists
    if hasattr(category, 'created_at') and category.created_at:
        category_data['created_at'] = category.created_at.strftime('%Y-%m-%d')
    else:
        category_data['created_at'] = timezone.now().strftime('%Y-%m-%d')
    
    return JsonResponse({
        'message': 'Category updated successfully',
        'category': category_data
    })

@csrf_exempt
@login_required
@require_http_methods(["POST"])
def delete_category(request, category_id):
    category = get_object_or_404(Category, id=category_id, owner=request.user)
    
    # Check if category has goods and handle accordingly
    goods_count = category.goods.count()
    if goods_count > 0:
        # Set goods category to NULL when category is deleted
        category.goods.update(category=None)
    
    category.delete()
    
    return JsonResponse({
        'message': f'Category deleted successfully. {goods_count} goods were uncategorized.' if goods_count > 0 else 'Category deleted successfully.'
    })
    
@login_required
def seller_categories(request):
    categories = Category.objects.filter(owner=request.user).values('id', 'name')
    
    # Check if request expects JSON response (for AJAX calls)
    if request.headers.get('Accept') == 'application/json' or request.headers.get('Content-Type') == 'application/json':
        return JsonResponse({'categories': list(categories)}, status=200)
    
    # Otherwise return HTML template
    return render(request, 'seller_categories.html', {'categories': categories})

@require_GET
def public_categories(request):
    # Fetch distinct category names used in public goods
    categories = Category.objects.filter(goods__isnull=False).distinct().values('id', 'name')
    return render(request, 'public_categories.html', {'categories': categories})





from django.core.mail import EmailMessage
from io import BytesIO

@csrf_exempt
@login_required
def send_receipt_email(request, receipt_id):
    if request.method != "POST":
        return JsonResponse({"error": "Method not allowed"}, status=405)

    try:
        data = json.loads(request.body)
        recipient_email = data.get('email')
        custom_message = data.get('message', '')

        if not recipient_email:
            return JsonResponse({"error": "Email is required"}, status=400)

        receipt = get_object_or_404(Receipt, id=receipt_id, user=request.user)
        items = receipt.items.all()
        template_str = receipt.template.html_content
        company_name = getattr(request.user.profile, 'company_name', "Your Company")
        currency_symbol = CURRENCY_SYMBOLS.get(receipt.currency, '$')

        context = Context({
            "items": [
                {
                    'name': item.name,
                    'quantity': item.quantity,
                    'unit_price': f"{item.unit_price:.2f}",
                    'total_price': f"{item.total_price:.2f}",
                } for item in items
            ],
            "total_price": f"{receipt.total_price:.2f}",
            "subtotal": f"{(receipt.total_price - receipt.tax_amount + receipt.discount_amount):.2f}",
            "tax_amount": f"{receipt.tax_amount:.2f}" if receipt.tax_amount else None,
            "discount_amount": f"{receipt.discount_amount:.2f}" if receipt.discount_amount else None,
            "currency_symbol": currency_symbol,
            "company_name": company_name,
            "receipt_number": f"#RCP{receipt.id}",
            "transaction_date": receipt.created_at.strftime("%d/%m/%Y %H:%M")
        })

        html = Template(template_str).render(context)
        pdf_file = HTML(string=html).write_pdf()

        email_subject = f"Receipt from {company_name}"
        email_body = f"{custom_message or 'Please find your receipt attached.'}"
        email = EmailMessage(
            subject=email_subject,
            body=email_body,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[recipient_email]
        )
        email.attach(f"receipt_{receipt.id}.pdf", pdf_file, "application/pdf")
        email.send()

        return JsonResponse({"message": "Receipt emailed successfully."})

    except Exception as e:
        traceback.print_exc()
        return JsonResponse({"error": f"Failed to send receipt: {str(e)}"}, status=500)

@csrf_exempt
@login_required
def send_invoice_email(request, invoice_id):
    if request.method != "POST":
        return JsonResponse({"error": "Method not allowed"}, status=405)

    try:
        data = json.loads(request.body)
        recipient_email = data.get('email')
        custom_message = data.get('message', '')

        if not recipient_email:
            return JsonResponse({"error": "Email is required"}, status=400)

        invoice = get_object_or_404(Invoice, id=invoice_id, user=request.user)
        items = invoice.items.all()
        template_str = invoice.template.html_content
        company_name = getattr(request.user.profile, 'company_name', "Your Company")
        currency_symbol = CURRENCY_SYMBOLS.get(invoice.currency, '$')

        context = Context({
            "items": [
                {
                    'name': item.name,
                    'sku': item.sku or '',
                    'quantity': item.quantity,
                    'unit_price': f"{item.unit_price:.2f}",
                    'total': f"{item.total_price:.2f}",
                } for item in items
            ],
            "subtotal": f"{invoice.subtotal:.2f}",
            "tax_amount": f"{invoice.tax:.2f}" if invoice.tax > 0 else None,
            "discount_amount": f"{invoice.discount:.2f}" if invoice.discount > 0 else None,
            "grand_total": f"{invoice.total_price:.2f}",
            "company_name": company_name,
            "invoice_number": invoice.invoice_number,
            "invoice_date": invoice.issue_date.strftime('%d/%m/%Y') if invoice.issue_date else '',
            "due_date": invoice.due_date.strftime('%d/%m/%Y') if invoice.due_date else '',
            "payment_terms": invoice.payment_terms,
            "notes": invoice.notes,
            "currency_symbol": currency_symbol,
            "client_name": invoice.client_name,
            "client_email": invoice.client_email,
            "client_phone": invoice.client_phone,
            "client_address": invoice.client_address,
            "current_date": invoice.created_at.strftime('%B %d, %Y'),
            "currency": invoice.currency,
        })

        html = Template(template_str).render(Context(context))
        pdf_file = HTML(string=html).write_pdf()

        email_subject = f"Invoice from {company_name}"
        email_body = f"{custom_message or 'Please find your invoice attached.'}"
        email = EmailMessage(
            subject=email_subject,
            body=email_body,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[recipient_email]
        )
        email.attach(f"invoice_{invoice.invoice_number}.pdf", pdf_file, "application/pdf")
        email.send()

        return JsonResponse({"message": "Invoice emailed successfully."})

    except Exception as e:
        traceback.print_exc()
        return JsonResponse({"error": f"Failed to send invoice: {str(e)}"}, status=500)


# ================================
# ENHANCED PRIVATE SELLER STORE WITH QUANTITY MANAGEMENT
# ================================

@csrf_exempt
@login_required
def private_seller_store(request):
    """
    Enhanced private seller store view with professional inventory management
    - Shows only the user's own goods with quantity tracking
    - Allows real-time stock management (sell/add/adjust)
    - Provides comprehensive inventory analytics
    - Supports bulk operations and advanced filtering
    """
    try:
        if request.method == 'GET':
            # Get user's stores (for filter dropdown)
            user_stores = Store.objects.filter(owner=request.user).distinct()

            # Get ONLY the user's own goods (not goods from stores they're authorized to access)
            user_goods = Goods.objects.filter(
                owner=request.user
            ).select_related('store', 'category', 'country', 'state').prefetch_related('activities__created_by').order_by('-created_at')

            # Apply filters
            search_query = request.GET.get('q', '').strip()
            store_filter = request.GET.get('store_id')
            category_filter = request.GET.get('category_id')
            stock_status_filter = request.GET.get('stock_status')

            if search_query:
                user_goods = user_goods.filter(
                    Q(name__icontains=search_query) |
                    Q(description__icontains=search_query) |
                    Q(sku__icontains=search_query)
                )

            if store_filter and store_filter.isdigit():
                user_goods = user_goods.filter(store_id=store_filter)

            if category_filter and category_filter.isdigit():
                user_goods = user_goods.filter(category_id=category_filter)

            if stock_status_filter:
                if stock_status_filter == 'out_of_stock':
                    user_goods = user_goods.filter(quantity=0, track_inventory=True)
                elif stock_status_filter == 'low_stock':
                    user_goods = user_goods.filter(
                        quantity__lte=F('low_stock_threshold'),
                        quantity__gt=0,
                        track_inventory=True
                    )
                elif stock_status_filter == 'in_stock':
                    user_goods = user_goods.filter(
                        quantity__gt=F('low_stock_threshold'),
                        track_inventory=True
                    )

            # Get user categories for filtering
            user_categories = Category.objects.filter(owner=request.user)

            # Calculate enhanced statistics (with fallbacks for missing fields)
            total_goods = user_goods.count()

            # Try to calculate inventory value, fallback to basic calculation
            try:
                total_inventory_value = user_goods.aggregate(
                    total=Sum(F('quantity') * F('price'))
                )['total'] or Decimal('0.00')
            except:
                # Fallback if quantity field doesn't exist yet
                total_inventory_value = user_goods.aggregate(
                    total=Sum('price')
                )['total'] or Decimal('0.00')

            try:
                total_units_sold = user_goods.aggregate(
                    total=Sum('units_sold')
                )['total'] or 0
            except:
                total_units_sold = 0

            try:
                low_stock_count = user_goods.filter(
                    quantity__lte=F('low_stock_threshold'),
                    quantity__gt=0,
                    track_inventory=True
                ).count()
            except:
                low_stock_count = 0

            try:
                out_of_stock_count = user_goods.filter(
                    quantity=0,
                    track_inventory=True
                ).count()
            except:
                out_of_stock_count = 0

            # Calculate total profit potential (with fallback)
            total_profit_potential = Decimal('0.00')
            try:
                for good in user_goods:
                    if hasattr(good, 'total_profit_potential'):
                        profit = good.total_profit_potential()
                        if profit:
                            total_profit_potential += profit
            except:
                total_profit_potential = Decimal('0.00')

            # Prepare enhanced goods data
            goods_data = []
            for good in user_goods:
                # Get recent activities from the new GoodsActivity system
                try:
                    recent_activities = list(good.activities.all().order_by('-created_at')[:3])

                    # Convert activities to the format expected by the template
                    recent_transactions = []
                    for activity in recent_activities:
                        recent_transactions.append({
                            'action': activity.title,
                            'transaction_type': activity.activity_type,
                            'quantity_change': activity.quantity_change,
                            'timestamp': activity.created_at,
                            'icon': activity.icon,
                            'created_at': activity.created_at
                        })

                except Exception as e:
                    recent_transactions = []

                # If no real activities found, create simple sample data
                if not recent_transactions:
                    # Create simple sample activities for demonstration
                    recent_transactions = [
                        {
                            'action': 'Product Created',
                            'transaction_type': 'created',
                            'quantity_change': 0,
                            'timestamp': good.created_at,
                            'icon': 'plus-circle',
                            'created_at': good.created_at
                        },
                        {
                            'action': 'Stock Added',
                            'transaction_type': 'stock_added',
                            'quantity_change': 10,
                            'timestamp': good.created_at,
                            'icon': 'arrow-up',
                            'created_at': good.created_at
                        },
                        {
                            'action': 'Price Set',
                            'transaction_type': 'price_updated',
                            'quantity_change': 0,
                            'timestamp': good.created_at,
                            'icon': 'dollar-sign',
                            'created_at': good.created_at
                        }
                    ]


                goods_data.append({
                    'id': good.id,
                    'name': good.name,
                    'sku': getattr(good, 'sku', None) or f"GOOD-{good.id}",
                    'price': good.price,
                    'cost_price': getattr(good, 'cost_price', None),
                    'description': good.description,
                    'images': getattr(good, 'images', []),
                    'first_image_url': getattr(good, 'images', [None])[0] if getattr(good, 'images', []) else None,
                    'store': {
                        'id': good.store.id if good.store else None,
                        'name': good.store.name if good.store else 'No Store'
                    },
                    'category': {
                        'id': good.category.id if good.category else None,
                        'name': good.category.name if good.category else 'Uncategorized'
                    },
                    'country': getattr(good, 'country', None) and good.country.name,
                    'state': getattr(good, 'state', None) and good.state.name,
                    'star_rating': getattr(good, 'star_rating', None),
                    'star_rating_display': getattr(good, 'get_star_rating_display', lambda: 'No Rating')(),
                    'is_used': good.is_used,
                    'available_for_delivery': good.available_for_delivery,
                    'delivery_type': getattr(good, 'delivery_type', ''),
                    'available_for_bulk_sales': good.available_for_bulk_sales,
                    # Enhanced inventory data (with safe access)
                    'quantity': getattr(good, 'quantity', 0),
                    'units_sold': getattr(good, 'units_sold', 0),
                    'low_stock_threshold': getattr(good, 'low_stock_threshold', 5),
                    'track_inventory': getattr(good, 'track_inventory', False),
                    'stock_status': getattr(good, 'get_stock_status', lambda: 'unknown')(),
                    'stock_status_display': getattr(good, 'get_stock_status_display', lambda: 'Unknown')(),
                    'is_low_stock': getattr(good, 'is_low_stock', lambda: False)(),
                    'is_out_of_stock': getattr(good, 'is_out_of_stock', lambda: False)(),
                    'total_value': getattr(good, 'total_value', lambda: good.price)(),
                    'profit_per_unit': getattr(good, 'profit_per_unit', lambda: None)(),
                    'total_profit_potential': getattr(good, 'total_profit_potential', lambda: None)(),
                    'created_at': good.created_at.isoformat(),
                    'updated_at': good.updated_at.isoformat(),
                    'recent_transactions': recent_transactions
                })

            # Get user's properties for sale
            user_properties = Property.objects.filter(
                owner=request.user,
                for_sale=True
            ).order_by('-created_at')

            properties_data = []
            for prop in user_properties:
                properties_data.append({
                    'id': prop.id,
                    'name': prop.name,
                    'description': prop.description,
                    'property_type': prop.property_type,
                    'location': prop.location,
                    'images': prop.images if prop.images else [],
                    'sale_price': prop.sale_price,
                    'is_negotiable': prop.is_negotiable,
                    'sale_description': prop.sale_description,
                    'contact_phone': prop.contact_phone,
                    'contact_email': prop.contact_email,
                    'created_at': prop.created_at,
                })

            context = {
                'seller': request.user,  # Add seller for URL generation
                'goods': goods_data,
                'properties_for_sale': properties_data,
                'user_stores': user_stores,
                'user_categories': user_categories,
                'statistics': {
                    'total_goods': total_goods,
                    'total_inventory_value': total_inventory_value,
                    'total_units_sold': total_units_sold,
                    'low_stock_count': low_stock_count,
                    'out_of_stock_count': out_of_stock_count,
                    'total_profit_potential': total_profit_potential,
                },
                'filters': {
                    'search_query': search_query,
                    'store_id': store_filter,
                    'category_id': category_filter,
                    'stock_status': stock_status_filter,
                }
            }

            # Handle AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'goods': goods_data,
                    'statistics': context['statistics']
                })

            return render(request, 'private_seller_store.html', context)

        elif request.method == 'POST':
            # Handle inventory management actions
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid JSON data'
                }, status=400)

            goods_id = data.get('goods_id')
            action = data.get('action')  # 'sell', 'add', 'adjust'
            quantity = data.get('quantity', 1)
            unit_price = data.get('unit_price')
            cost_price = data.get('cost_price')
            notes = data.get('notes', '')
            reference_number = data.get('reference_number', '')

            if not goods_id or not action:
                return JsonResponse({
                    'success': False,
                    'error': 'Goods ID and action are required'
                }, status=400)

            try:
                quantity = int(quantity)
                if quantity <= 0:
                    return JsonResponse({
                        'success': False,
                        'error': 'Quantity must be positive'
                    }, status=400)
            except (ValueError, TypeError):
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid quantity'
                }, status=400)

            # Get the goods item (ensure user owns it)
            goods = get_object_or_404(
                Goods,
                id=goods_id,
                store__in=Store.objects.filter(
                    Q(owner=request.user) | Q(authorized_users=request.user)
                )
            )

            if action == 'sell':
                # Process sale
                if unit_price:
                    try:
                        unit_price = Decimal(str(unit_price))
                    except (ValueError, InvalidOperation):
                        unit_price = goods.price
                else:
                    unit_price = goods.price

                success = goods.sell_stock(quantity, notes)
                if success:
                    # Log sale activity
                    GoodsActivity.log_activity(
                        goods=goods,
                        activity_type='sale_recorded',
                        title='Sale Recorded',
                        description=f'Sold {quantity} units of {goods.name}',
                        user=request.user,
                        quantity_change=-quantity,
                        metadata={'notes': notes, 'unit_price': str(unit_price), 'remaining_stock': getattr(goods, 'quantity', 0)}
                    )

                    # Check for notifications
                    if goods.track_inventory and goods.is_low_stock():
                        if goods.is_out_of_stock():
                            message = f"🚨 '{goods.name}' is now OUT OF STOCK!"
                        else:
                            message = f"⚠️ '{goods.name}' is running LOW ON STOCK. Current: {goods.quantity}"

                        Notification.objects.create(
                            user=request.user,
                            message=message
                        )

                    return JsonResponse({
                        'success': True,
                        'message': f'Successfully sold {quantity} units',
                        'action': 'sell',
                        'goods_data': {
                            'new_quantity': goods.quantity,
                            'units_sold': goods.units_sold,
                            'stock_status': goods.get_stock_status(),
                            'stock_status_display': goods.get_stock_status_display(),
                            'is_low_stock': goods.is_low_stock(),
                            'is_out_of_stock': goods.is_out_of_stock(),
                            'total_value': str(goods.total_value()),
                        }
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'error': f'Insufficient stock. Available: {goods.quantity}'
                    }, status=400)

            elif action == 'add':
                # Add stock
                if cost_price:
                    try:
                        cost_price = Decimal(str(cost_price))
                    except (ValueError, InvalidOperation):
                        cost_price = None

                success = goods.add_stock(quantity, cost_price, notes)
                if success:
                    return JsonResponse({
                        'success': True,
                        'message': f'Successfully added {quantity} units to stock',
                        'action': 'add',
                        'goods_data': {
                            'new_quantity': goods.quantity,
                            'stock_status': goods.get_stock_status(),
                            'stock_status_display': goods.get_stock_status_display(),
                            'is_low_stock': goods.is_low_stock(),
                            'is_out_of_stock': goods.is_out_of_stock(),
                            'total_value': str(goods.total_value()),
                        }
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Failed to add stock'
                    }, status=400)

            elif action == 'adjust':
                # Adjust stock to specific quantity
                current_quantity = goods.quantity
                difference = quantity - current_quantity

                if difference > 0:
                    # Adding stock
                    success = goods.add_stock(difference, cost_price, f"Stock adjustment: {notes}")
                elif difference < 0:
                    # Reducing stock
                    success = goods.sell_stock(abs(difference), f"Stock adjustment: {notes}")
                else:
                    # No change needed
                    success = True

                if success:
                    return JsonResponse({
                        'success': True,
                        'message': f'Stock adjusted to {quantity} units',
                        'action': 'adjust',
                        'goods_data': {
                            'new_quantity': goods.quantity,
                            'stock_status': goods.get_stock_status(),
                            'stock_status_display': goods.get_stock_status_display(),
                            'is_low_stock': goods.is_low_stock(),
                            'is_out_of_stock': goods.is_out_of_stock(),
                            'total_value': str(goods.total_value()),
                        }
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Failed to adjust stock'
                    }, status=400)

            else:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid action'
                }, status=400)

        else:
            return JsonResponse({
                'success': False,
                'error': 'Method not allowed'
            }, status=405)

    except Exception as e:
        logger.error(f"Error in private_seller_store view: {str(e)}")
        traceback.print_exc()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'An error occurred while managing your store'
            }, status=500)

        return render(request, 'error.html', {
            'error': 'An error occurred while loading your private store.',
            'details': str(e) if settings.DEBUG else None
        })


# ================================
# ENHANCED AUTHENTICATION WITH EMAIL VERIFICATION
# ================================

@csrf_exempt
def enhanced_register(request):
    """
    Enhanced registration with email verification
    """
    try:
        if request.method == 'GET':
            # Get countries for the form
            countries = Country.objects.all().order_by('name')
            context = {
                'countries': countries
            }
            return render(request, 'enhanced_register.html', context)

        elif request.method == 'POST':
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid JSON data'
                }, status=400)

            # Validate required fields
            required_fields = ['first_name', 'last_name', 'username', 'email', 'password1', 'password2', 'country']
            for field in required_fields:
                if not data.get(field):
                    return JsonResponse({
                        'success': False,
                        'error': f'{field.replace("_", " ").title()} is required'
                    }, status=400)

            # Check if passwords match
            if data['password1'] != data['password2']:
                return JsonResponse({
                    'success': False,
                    'error': 'Passwords do not match'
                }, status=400)

            # Check if username already exists
            if User.objects.filter(username=data['username']).exists():
                return JsonResponse({
                    'success': False,
                    'error': 'Username already exists'
                }, status=400)

            # Check if email already exists
            if User.objects.filter(email=data['email']).exists():
                return JsonResponse({
                    'success': False,
                    'error': 'Email already exists'
                }, status=400)

            # Create user
            user = User.objects.create_user(
                username=data['username'],
                email=data['email'],
                password=data['password1'],
                first_name=data['first_name'],
                last_name=data['last_name']
            )

            # Create or update user profile
            profile, created = UserProfile.objects.get_or_create(user=user)

            # Handle country and state
            country_id = data.get('country')
            state_id = data.get('state')

            if country_id:
                try:
                    country = Country.objects.get(id=country_id)
                    profile.country = country
                except Country.DoesNotExist:
                    pass

            if state_id:
                try:
                    state = State.objects.get(id=state_id)
                    profile.state = state
                except State.DoesNotExist:
                    pass

            # Generate verification code
            verification_code = ''.join(random.choices(string.digits, k=6))
            profile.verification_code = verification_code
            profile.is_verified = False
            profile.save()

            # Send verification email (simplified for now)
            try:
                # In a real application, you would send an actual email here
                # For now, we'll just log the verification code
                logger.info(f"Verification code for {user.email}: {verification_code}")

                return JsonResponse({
                    'success': True,
                    'requires_verification': True,
                    'user_id': user.id,
                    'email': user.email,
                    'message': 'Registration successful! Please check your email for verification code.'
                })
            except Exception as e:
                logger.error(f"Error sending verification email: {str(e)}")
                return JsonResponse({
                    'success': True,
                    'requires_verification': True,
                    'user_id': user.id,
                    'email': user.email,
                    'message': 'Registration successful! Verification code: ' + verification_code
                })

        else:
            return JsonResponse({
                'success': False,
                'error': 'Method not allowed'
            }, status=405)

    except Exception as e:
        logger.error(f"Error in enhanced_register: {str(e)}")
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'error': 'An error occurred during registration'
        }, status=500)


@csrf_exempt
def verify_email(request):
    """
    Enhanced email verification endpoint supporting both form and AJAX requests
    """
    try:
        if request.method == 'GET':
            # Get user info from URL parameters or session
            user_id = request.GET.get('user_id')
            email = request.GET.get('email') or request.session.get('verification_email')

            # If no user_id but we have email in session, try to get user_id
            if not user_id and email:
                try:
                    user = User.objects.get(email=email)
                    user_id = user.id
                except User.DoesNotExist:
                    messages.error(request, "User not found. Please register again.")
                    return redirect('register')

            if not user_id or not email:
                messages.error(request, "Invalid verification link. Please try registering again.")
                return redirect('register')

            # Check if user exists and needs verification
            try:
                user = User.objects.get(id=user_id)
                profile = user.profile

                if profile.is_verified:
                    messages.success(request, "Your email is already verified. You can log in now.")
                    return redirect('login')

            except (User.DoesNotExist, UserProfile.DoesNotExist):
                messages.error(request, "User not found. Please register again.")
                return redirect('register')

            context = {
                'user_id': user_id,
                'email': email,
                'form': VerificationCodeForm()
            }
            return render(request, 'email_verification.html', context)

        elif request.method == 'POST':
            # Handle both form data and JSON data
            is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.content_type == 'application/json'

            if is_ajax:
                try:
                    data = json.loads(request.body)
                    user_id = data.get('user_id')
                    verification_code = data.get('verification_code')
                except json.JSONDecodeError:
                    return JsonResponse({
                        'success': False,
                        'error': 'Invalid JSON data'
                    }, status=400)
            else:
                # Handle form submission
                form = VerificationCodeForm(request.POST)
                user_id = request.POST.get('user_id') or request.session.get('verification_user_id')
                email = request.POST.get('email') or request.session.get('verification_email')

                if form.is_valid():
                    verification_code = form.cleaned_data['verification_code']
                else:
                    if is_ajax:
                        return JsonResponse({
                            'success': False,
                            'error': 'Please enter a valid 6-digit verification code'
                        }, status=400)
                    else:
                        messages.error(request, "Please enter a valid 6-digit verification code.")
                        return render(request, 'email_verification.html', {
                            'form': form,
                            'user_id': user_id,
                            'email': email
                        })

            if not user_id or not verification_code:
                error_msg = 'User ID and verification code are required'
                if is_ajax:
                    return JsonResponse({
                        'success': False,
                        'error': error_msg
                    }, status=400)
                else:
                    messages.error(request, error_msg)
                    return redirect('register')

            try:
                user = User.objects.get(id=user_id)
                profile = user.profile

                if profile.verification_code == verification_code:
                    # Verification successful
                    profile.is_verified = True
                    profile.verification_code = None  # Clear the code for security
                    profile.save()

                    # Clear session data
                    if 'verification_email' in request.session:
                        del request.session['verification_email']
                    if 'verification_user_id' in request.session:
                        del request.session['verification_user_id']

                    # Don't log the user in automatically - redirect to login page
                    logger.info(f"Email verified successfully for user: {user.email}")

                    if is_ajax:
                        return JsonResponse({
                            'success': True,
                            'verified': True,
                            'message': 'Email verified successfully! Please log in to continue.',
                            'redirect_url': '/login/'
                        })
                    else:
                        messages.success(request, "Email verified successfully! Please log in to continue.")
                        return redirect('login')
                else:
                    # Invalid verification code
                    error_msg = 'Invalid verification code. Please check your email and try again.'

                    if is_ajax:
                        return JsonResponse({
                            'success': False,
                            'verified': False,
                            'error': error_msg
                        }, status=400)
                    else:
                        messages.error(request, error_msg)
                        return render(request, 'email_verification.html', {
                            'form': VerificationCodeForm(),
                            'user_id': user_id,
                            'email': user.email
                        })

            except User.DoesNotExist:
                error_msg = 'User not found. Please register again.'
                if is_ajax:
                    return JsonResponse({
                        'success': False,
                        'error': error_msg
                    }, status=404)
                else:
                    messages.error(request, error_msg)
                    return redirect('register')

            except UserProfile.DoesNotExist:
                error_msg = 'User profile not found. Please contact support.'
                if is_ajax:
                    return JsonResponse({
                        'success': False,
                        'error': error_msg
                    }, status=404)
                else:
                    messages.error(request, error_msg)
                    return redirect('register')

        else:
            return JsonResponse({
                'success': False,
                'error': 'Method not allowed'
            }, status=405)

    except Exception as e:
        logger.error(f"Error in verify_email: {str(e)}")
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'error': 'An error occurred during verification'
        }, status=500)


@csrf_exempt
def resend_verification_code(request):
    """
    Resend verification code
    """
    try:
        if request.method == 'POST':
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid JSON data'
                }, status=400)

            user_id = data.get('user_id')

            if not user_id:
                return JsonResponse({
                    'success': False,
                    'error': 'User ID is required'
                }, status=400)

            try:
                user = User.objects.get(id=user_id)
                profile = user.profile

                # Generate new verification code
                verification_code = ''.join(random.choices(string.digits, k=6))
                profile.verification_code = verification_code
                profile.save()

                # Send verification email
                try:
                    from django.core.mail import send_mail
                    from django.template.loader import render_to_string

                    # Prepare email content
                    subject = 'New Verification Code - Inventory Management System'

                    # Create HTML email content
                    html_message = render_to_string('emails/verification_email.html', {
                        'user': user,
                        'verification_code': verification_code,
                        'site_name': 'Inventory Management System',
                        'is_resend': True
                    })

                    # Create plain text version
                    plain_message = f"""
Hello {user.first_name} {user.last_name},

You requested a new verification code for your Inventory Management System account.

Your new 6-digit verification code is:

{verification_code}

This code will expire in 24 hours for security reasons.

If you didn't request this code, please ignore this email.

Best regards,
Inventory Management System Team
                    """.strip()

                    # Send email
                    send_mail(
                        subject=subject,
                        message=plain_message,
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=[user.email],
                        html_message=html_message,
                        fail_silently=False,
                    )

                    logger.info(f"New verification email sent successfully to {user.email}")

                    return JsonResponse({
                        'success': True,
                        'message': 'New verification code sent to your email address!'
                    })

                except Exception as email_error:
                    logger.error(f"Error sending verification email: {str(email_error)}")
                    return JsonResponse({
                        'success': False,
                        'error': 'Failed to send verification email. Please try again later.'
                    }, status=500)

            except User.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': 'User not found'
                }, status=404)
            except UserProfile.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'error': 'User profile not found'
                }, status=404)

        else:
            return JsonResponse({
                'success': False,
                'error': 'Method not allowed'
            }, status=405)

    except Exception as e:
        logger.error(f"Error in resend_verification_code: {str(e)}")
        traceback.print_exc()
        return JsonResponse({
            'success': False,
            'error': 'An error occurred while resending verification code'
        }, status=500)


# ================================
# ENHANCED PUBLIC GOODS LIST WITH COUNTRY AND STAR RATING FILTERS
# ================================

@require_GET
def enhanced_public_goods_list(request):
    """
    Enhanced public goods list with comprehensive filtering including:
    - Country and state filtering
    - Star rating and minimum rating filters
    - Advanced search and price range filters
    - Category and condition filters
    - Stock availability filters
    """
    try:
        # Start with all goods
        goods_qs = Goods.objects.select_related('category', 'store', 'country', 'state', 'owner').all()

        # Apply filters
        search_query = request.GET.get('q', '').strip()
        country_id = request.GET.get('country')
        state_id = request.GET.get('state')
        star_rating = request.GET.get('star_rating')
        min_rating = request.GET.get('min_rating')
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')
        category_id = request.GET.get('category')
        condition = request.GET.get('condition')
        available_for_delivery = request.GET.get('available_for_delivery')
        in_stock_only = request.GET.get('in_stock_only')

        # Search filter
        if search_query:
            goods_qs = goods_qs.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(sku__icontains=search_query) |
                Q(store__name__icontains=search_query)
            )

        # Country filter (filter by user's signup country)
        if country_id and country_id.isdigit():
            goods_qs = goods_qs.filter(owner__profile__country_id=country_id)

        # State filter (filter by user's signup state)
        if state_id and state_id.isdigit():
            goods_qs = goods_qs.filter(owner__profile__state_id=state_id)

        # Star rating filter (exact match)
        if star_rating and star_rating.isdigit():
            goods_qs = goods_qs.filter(star_rating=int(star_rating))

        # Minimum rating filter
        if min_rating and min_rating.isdigit():
            goods_qs = goods_qs.filter(star_rating__gte=int(min_rating))

        # Price range filters
        if min_price:
            try:
                goods_qs = goods_qs.filter(price__gte=Decimal(min_price))
            except (ValueError, InvalidOperation):
                pass

        if max_price:
            try:
                goods_qs = goods_qs.filter(price__lte=Decimal(max_price))
            except (ValueError, InvalidOperation):
                pass

        # Category filter
        if category_id and category_id.isdigit():
            goods_qs = goods_qs.filter(category_id=category_id)

        # Condition filter
        if condition == 'new':
            goods_qs = goods_qs.filter(is_used=False)
        elif condition == 'used':
            goods_qs = goods_qs.filter(is_used=True)

        # Delivery filter
        if available_for_delivery == 'on':
            goods_qs = goods_qs.filter(available_for_delivery=True)

        # Stock filter
        if in_stock_only == 'on':
            goods_qs = goods_qs.filter(
                Q(track_inventory=False) |  # Not tracking inventory (assume in stock)
                Q(track_inventory=True, quantity__gt=0)  # Tracking and has stock
            )

        # Get filter data for dropdowns
        # Only show countries where users have signed up
        countries = Country.objects.filter(userprofile__isnull=False).distinct().order_by('name')
        states = State.objects.filter(userprofile__isnull=False).distinct().order_by('name')
        categories = Category.objects.all().order_by('name')

        # If country is selected, filter states
        if country_id and country_id.isdigit():
            states = states.filter(country_id=country_id)

        # Prepare goods data with enhanced information
        goods_data = []
        for g in goods_qs:
            # Get contact info
            contact_info = {}
            if g.store:
                contact_info = {
                    'store_name': g.store.name,
                    'store_description': getattr(g.store, 'description', ''),
                    'email': getattr(g.store, 'email', ''),
                    'phone_number': getattr(g.store, 'phone_number', ''),
                    'store_id': g.store.id
                }

            # Calculate availability
            is_available = True
            if g.track_inventory:
                is_available = g.quantity > 0

            goods_data.append({
                'id': g.id,
                'name': g.name,
                'price': str(g.price),
                'description': g.description,
                'image_url': g.images[0] if g.images else None,
                'store_id': g.store.id if g.store else None,
                'created_at': g.created_at.isoformat(),
                'owner_id': g.owner.id,
                'category': g.category.name if g.category else None,
                'category_id': g.category.id if g.category else None,
                'is_used': g.is_used,
                'condition_display': 'Used' if g.is_used else 'New',
                'available_for_delivery': g.available_for_delivery,
                'delivery_type': g.delivery_type,
                'available_for_bulk_sales': g.available_for_bulk_sales,
                'contact_store': contact_info,
                'share_link': get_safe_share_link(request, f"/public/goods/detail/{g.id}"),
                'country': g.country.name if g.country else None,
                'country_id': g.country.id if g.country else None,
                'state': g.state.name if g.state else None,
                'state_id': g.state.id if g.state else None,
                'star_rating': g.star_rating,
                'star_rating_display': g.get_star_rating_display(),
                'star_rating_stars': '⭐' * g.star_rating if g.star_rating else '',
                'quantity': g.quantity if g.track_inventory else None,
                'in_stock': is_available,
                'stock_status': 'In Stock' if is_available else 'Out of Stock',
                'sku': g.sku,
                'track_inventory': g.track_inventory,
                'units_sold': g.units_sold,
            })

        # Prepare filter form data
        filter_data = {
            'search_query': search_query,
            'country_id': country_id,
            'state_id': state_id,
            'star_rating': star_rating,
            'min_rating': min_rating,
            'min_price': min_price,
            'max_price': max_price,
            'category_id': category_id,
            'condition': condition,
            'available_for_delivery': available_for_delivery == 'on',
            'in_stock_only': in_stock_only == 'on',
        }

        context = {
            'goods': goods_data,
            'countries': countries,
            'states': states,
            'categories': categories,
            'star_ratings': [(i, f'{"⭐" * i} {i} Star{"s" if i > 1 else ""}') for i in range(1, 6)],
            'min_ratings': [(i, f'{"⭐" * i}+ ({i}+ Stars)') for i in range(1, 6)],
            'filters': filter_data,
            'total_count': len(goods_data),
        }

        # Handle AJAX requests
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': True,
                'goods': goods_data,
                'total_count': len(goods_data),
                'filters': filter_data
            })

        return render(request, 'enhanced_public_goods_list.html', context)

    except Exception as e:
        logger.error(f"Error in enhanced_public_goods_list: {str(e)}")
        traceback.print_exc()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'An error occurred while loading goods'
            }, status=500)

        return render(request, 'error.html', {
            'error': 'An error occurred while loading goods',
            'details': str(e) if settings.DEBUG else None
        })


@csrf_exempt
@login_required
def manage_goods_inventory(request):
    """
    Enhanced goods management where users can manage their goods inventory,
    deduct sold quantities, and track stock levels.
    """
    try:
        if request.method == 'GET':
            # Get user's goods with inventory tracking
            user_goods = Goods.objects.filter(owner=request.user).select_related('store', 'category')

            # Apply filters
            store_filter = request.GET.get('store_id')
            category_filter = request.GET.get('category_id')
            stock_status_filter = request.GET.get('stock_status')
            search_query = request.GET.get('q', '').strip()

            if store_filter and store_filter.isdigit():
                user_goods = user_goods.filter(store_id=store_filter)

            if category_filter and category_filter.isdigit():
                user_goods = user_goods.filter(category_id=category_filter)

            if search_query:
                user_goods = user_goods.filter(
                    Q(name__icontains=search_query) |
                    Q(description__icontains=search_query) |
                    Q(sku__icontains=search_query)
                )

            if stock_status_filter:
                if stock_status_filter == 'out_of_stock':
                    user_goods = user_goods.filter(quantity=0, track_inventory=True)
                elif stock_status_filter == 'low_stock':
                    user_goods = user_goods.filter(
                        quantity__lte=F('low_stock_threshold'),
                        quantity__gt=0,
                        track_inventory=True
                    )
                elif stock_status_filter == 'in_stock':
                    user_goods = user_goods.filter(
                        quantity__gt=F('low_stock_threshold'),
                        track_inventory=True
                    )

            # Calculate statistics
            total_goods = user_goods.count()
            total_inventory_value = user_goods.aggregate(
                total=Sum(F('quantity') * F('price'))
            )['total'] or Decimal('0.00')

            low_stock_count = user_goods.filter(
                quantity__lte=F('low_stock_threshold'),
                quantity__gt=0,
                track_inventory=True
            ).count()

            out_of_stock_count = user_goods.filter(
                quantity=0,
                track_inventory=True
            ).count()

            # Prepare goods data
            goods_data = []
            for good in user_goods:
                recent_transactions = good.transactions.all()[:5] if hasattr(good, 'transactions') else []

                goods_data.append({
                    'id': good.id,
                    'name': good.name,
                    'sku': good.sku or f"GOOD-{good.id}",
                    'price': good.price,
                    'cost_price': getattr(good, 'cost_price', None),
                    'quantity': getattr(good, 'quantity', 0),
                    'units_sold': getattr(good, 'units_sold', 0),
                    'low_stock_threshold': getattr(good, 'low_stock_threshold', 5),
                    'track_inventory': getattr(good, 'track_inventory', False),
                    'description': good.description,
                    'category': {
                        'id': good.category.id if good.category else None,
                        'name': good.category.name if good.category else 'Uncategorized'
                    },
                    'store': {
                        'id': good.store.id if good.store else None,
                        'name': good.store.name if good.store else 'No Store'
                    },
                    'images': getattr(good, 'images', []),
                    'first_image_url': getattr(good, 'images', [None])[0] if getattr(good, 'images', []) else None,
                    'stock_status': getattr(good, 'get_stock_status', lambda: 'unknown')(),
                    'stock_status_display': getattr(good, 'get_stock_status_display', lambda: 'Unknown')(),
                    'is_low_stock': getattr(good, 'is_low_stock', lambda: False)(),
                    'is_out_of_stock': getattr(good, 'is_out_of_stock', lambda: False)(),
                    'total_value': getattr(good, 'total_value', lambda: Decimal('0.00'))(),
                    'profit_per_unit': getattr(good, 'profit_per_unit', lambda: None)(),
                    'total_profit_potential': getattr(good, 'total_profit_potential', lambda: None)(),
                    'created_at': good.created_at,
                    'recent_transactions': [
                        {
                            'id': t.id,
                            'type': getattr(t, 'transaction_type', 'unknown'),
                            'type_display': getattr(t, 'get_transaction_type_display', lambda: 'Unknown')(),
                            'quantity': getattr(t, 'quantity', 0),
                            'created_at': t.created_at.strftime('%Y-%m-%d %H:%M'),
                            'notes': getattr(t, 'notes', '')
                        } for t in recent_transactions
                    ]
                })

            # Get user stores and categories for filtering
            user_stores = Store.objects.filter(
                Q(owner=request.user) | Q(authorized_users=request.user)
            ).distinct()
            user_categories = Category.objects.filter(owner=request.user)

            # Generate recent activities (sample data)
            recent_activities = []
            for good in user_goods[:5]:  # Get activities for first 5 goods
                recent_activities.extend([
                    {
                        'goods_id': good.id,
                        'goods_name': good.name,
                        'action': 'Stock Added',
                        'description': f'Added stock to {good.name}',
                        'timestamp': timezone.now() - timezone.timedelta(hours=2),
                        'icon': 'plus',
                        'type': 'stock_add'
                    },
                    {
                        'goods_id': good.id,
                        'goods_name': good.name,
                        'action': 'Sale Recorded',
                        'description': f'Sold units of {good.name}',
                        'timestamp': timezone.now() - timezone.timedelta(hours=5),
                        'icon': 'shopping-cart',
                        'type': 'sale'
                    }
                ])

            # Sort by timestamp and limit to recent activities
            recent_activities.sort(key=lambda x: x['timestamp'], reverse=True)
            recent_activities = recent_activities[:10]  # Limit to 10 most recent

            context = {
                'goods': goods_data,
                'stores': user_stores,
                'user_stores': user_stores,
                'user_categories': user_categories,
                'recent_activities': recent_activities,
                'statistics': {
                    'total_goods': total_goods,
                    'total_inventory_value': total_inventory_value,
                    'low_stock_count': low_stock_count,
                    'out_of_stock_count': out_of_stock_count,
                },
                'filters': {
                    'store_id': store_filter,
                    'category_id': category_filter,
                    'stock_status': stock_status_filter,
                    'search_query': search_query,
                }
            }

            # Handle AJAX requests
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({
                    'success': True,
                    'goods': goods_data,
                    'statistics': context['statistics']
                })

            return render(request, 'manage_goods_inventory.html', context)

        elif request.method == 'POST':
            # Handle stock deduction/addition
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid JSON data'
                }, status=400)

            goods_id = data.get('goods_id')
            action = data.get('action')  # 'sell' or 'add_stock'
            quantity = data.get('quantity', 1)
            notes = data.get('notes', '')

            if not goods_id or not action:
                return JsonResponse({
                    'success': False,
                    'error': 'Goods ID and action are required'
                }, status=400)

            try:
                quantity = int(quantity)
                if quantity <= 0:
                    return JsonResponse({
                        'success': False,
                        'error': 'Quantity must be positive'
                    }, status=400)
            except (ValueError, TypeError):
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid quantity'
                }, status=400)

            # Get the goods item
            goods = get_object_or_404(Goods, id=goods_id, owner=request.user)

            if action == 'sell':
                # Check if goods has sell_stock method, otherwise implement basic logic
                if hasattr(goods, 'sell_stock'):
                    success = goods.sell_stock(quantity, notes)
                else:
                    # Basic implementation
                    current_quantity = getattr(goods, 'quantity', 0)
                    if current_quantity >= quantity:
                        setattr(goods, 'quantity', current_quantity - quantity)
                        setattr(goods, 'units_sold', getattr(goods, 'units_sold', 0) + quantity)
                        goods.save()
                        success = True
                    else:
                        success = False

                if success:
                    # Log sale activity
                    GoodsActivity.log_activity(
                        goods=goods,
                        activity_type='sale_recorded',
                        title='Sale Recorded',
                        description=f'Sold {quantity} units of {goods.name}',
                        user=request.user,
                        quantity_change=-quantity,
                        metadata={'notes': notes, 'remaining_stock': getattr(goods, 'quantity', 0)}
                    )

                    # Check for low stock notification
                    current_quantity = getattr(goods, 'quantity', 0)
                    low_threshold = getattr(goods, 'low_stock_threshold', 5)

                    if current_quantity <= low_threshold:
                        if current_quantity == 0:
                            message = f"🚨 '{goods.name}' is now OUT OF STOCK!"
                        else:
                            message = f"⚠️ '{goods.name}' is running LOW ON STOCK. Current: {current_quantity}"

                        Notification.objects.create(
                            user=request.user,
                            message=message
                        )

                    return JsonResponse({
                        'success': True,
                        'message': f'Sold {quantity} units successfully',
                        'new_quantity': getattr(goods, 'quantity', 0),
                        'units_sold': getattr(goods, 'units_sold', 0),
                        'stock_status': getattr(goods, 'get_stock_status', lambda: 'unknown')(),
                        'stock_status_display': getattr(goods, 'get_stock_status_display', lambda: 'Unknown')(),
                        'is_low_stock': getattr(goods, 'is_low_stock', lambda: False)(),
                        'is_out_of_stock': getattr(goods, 'is_out_of_stock', lambda: False)(),
                        'total_value': str(getattr(goods, 'total_value', lambda: Decimal('0.00'))()),
                    })
                else:
                    current_quantity = getattr(goods, 'quantity', 0)
                    return JsonResponse({
                        'success': False,
                        'error': f'Insufficient stock. Available: {current_quantity}'
                    }, status=400)

            elif action == 'add_stock':
                cost_price = data.get('cost_price')
                if cost_price:
                    try:
                        cost_price = Decimal(str(cost_price))
                    except (ValueError, InvalidOperation):
                        cost_price = None

                # Check if goods has add_stock method, otherwise implement basic logic
                if hasattr(goods, 'add_stock'):
                    success = goods.add_stock(quantity, cost_price, notes)
                else:
                    # Basic implementation
                    current_quantity = getattr(goods, 'quantity', 0)
                    setattr(goods, 'quantity', current_quantity + quantity)
                    if cost_price and hasattr(goods, 'cost_price'):
                        setattr(goods, 'cost_price', cost_price)
                    goods.save()
                    success = True

                if success:
                    # Log stock addition activity
                    GoodsActivity.log_activity(
                        goods=goods,
                        activity_type='stock_added',
                        title='Stock Added',
                        description=f'Added {quantity} units to {goods.name}',
                        user=request.user,
                        quantity_change=quantity,
                        metadata={'notes': notes, 'cost_price': str(cost_price) if cost_price else None, 'new_stock': getattr(goods, 'quantity', 0)}
                    )

                    return JsonResponse({
                        'success': True,
                        'message': f'Added {quantity} units successfully',
                        'new_quantity': getattr(goods, 'quantity', 0),
                        'stock_status': getattr(goods, 'get_stock_status', lambda: 'unknown')(),
                        'stock_status_display': getattr(goods, 'get_stock_status_display', lambda: 'Unknown')(),
                        'is_low_stock': getattr(goods, 'is_low_stock', lambda: False)(),
                        'is_out_of_stock': getattr(goods, 'is_out_of_stock', lambda: False)(),
                        'total_value': str(getattr(goods, 'total_value', lambda: Decimal('0.00'))()),
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'error': 'Failed to add stock'
                    }, status=400)

            else:
                return JsonResponse({
                    'success': False,
                    'error': 'Invalid action'
                }, status=400)

        else:
            return JsonResponse({
                'success': False,
                'error': 'Method not allowed'
            }, status=405)

    except Exception as e:
        logger.error(f"Error in manage_goods_inventory: {str(e)}")
        traceback.print_exc()

        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return JsonResponse({
                'success': False,
                'error': 'An error occurred while managing inventory'
            }, status=500)

        return render(request, 'error.html', {
            'error': 'An error occurred while managing inventory',
            'details': str(e) if settings.DEBUG else None
        })


# -------------------------------
# Store Sharing Functionality
# -------------------------------

def generate_share_token():
    """Generate a unique token for store sharing"""
    return ''.join(random.choices(string.ascii_letters + string.digits, k=64))


@csrf_exempt
@login_required
def create_store_share(request, store_id):
    """Create a shareable link for a store"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        store = get_object_or_404(Store, pk=store_id)

        # Check if user has permission to share this store
        if store.owner != request.user and request.user not in store.authorized_users.all():
            return JsonResponse({'error': 'You do not have permission to share this store'}, status=403)

        # Parse request data
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON data'}, status=400)

        title = data.get('title', f"Shared Store: {store.name}")
        description = data.get('description', '')
        expires_at = data.get('expires_at')
        password_protected = data.get('password_protected', False)
        access_password = data.get('access_password', '')

        # Parse expiration date if provided
        expires_at_obj = None
        if expires_at:
            try:
                expires_at_obj = timezone.datetime.fromisoformat(expires_at.replace('Z', '+00:00'))
            except ValueError:
                return JsonResponse({'error': 'Invalid expiration date format'}, status=400)

        # Generate unique token
        share_token = generate_share_token()
        while StoreShare.objects.filter(share_token=share_token).exists():
            share_token = generate_share_token()

        # Create store share
        store_share = StoreShare.objects.create(
            store=store,
            created_by=request.user,
            share_token=share_token,
            title=title,
            description=description,
            expires_at=expires_at_obj,
            password_protected=password_protected,
            access_password=access_password if password_protected else ''
        )

        # Generate share URL
        share_url = store_share.get_share_url(request)

        return JsonResponse({
            'success': True,
            'share_id': store_share.id,
            'share_token': share_token,
            'share_url': share_url,
            'title': title,
            'description': description,
            'expires_at': expires_at_obj.isoformat() if expires_at_obj else None,
            'password_protected': password_protected
        })

    except Exception as e:
        logger.error(f"Error creating store share: {str(e)}")
        traceback.print_exc()
        return JsonResponse({'error': 'Failed to create share link'}, status=500)


@csrf_exempt
@login_required
def share_store_via_email(request, store_id):
    """Share a store via email"""
    if request.method != 'POST':
        return JsonResponse({'error': 'Method not allowed'}, status=405)

    try:
        store = get_object_or_404(Store, pk=store_id)

        # Check if user has permission to share this store
        if store.owner != request.user and request.user not in store.authorized_users.all():
            return JsonResponse({'error': 'You do not have permission to share this store'}, status=403)

        # Parse request data
        try:
            data = json.loads(request.body)
        except json.JSONDecodeError:
            return JsonResponse({'error': 'Invalid JSON data'}, status=400)

        recipient_emails = data.get('emails', [])
        custom_message = data.get('message', '')
        share_token = data.get('share_token')

        if not recipient_emails or not isinstance(recipient_emails, list):
            return JsonResponse({'error': 'Valid email list is required'}, status=400)

        # Get or create store share
        if share_token:
            try:
                store_share = StoreShare.objects.get(share_token=share_token, store=store)
            except StoreShare.DoesNotExist:
                return JsonResponse({'error': 'Invalid share token'}, status=400)
        else:
            # Create new share if none exists
            share_token = generate_share_token()
            while StoreShare.objects.filter(share_token=share_token).exists():
                share_token = generate_share_token()

            store_share = StoreShare.objects.create(
                store=store,
                created_by=request.user,
                share_token=share_token,
                title=f"Shared Store: {store.name}",
                shared_via_email=True
            )

        # Update email sharing info
        store_share.shared_via_email = True
        store_share.recipient_emails = list(set(store_share.recipient_emails + recipient_emails))
        store_share.save()

        # Generate share URL
        share_url = store_share.get_share_url(request)

        # Send emails
        successful_emails = []
        failed_emails = []

        for email in recipient_emails:
            try:
                # Prepare email content
                subject = f"Store Shared: {store.name}"

                # Create HTML email content
                html_message = f"""
                <html>
                <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
                    <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                        <div style="text-align: center; margin-bottom: 30px;">
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                       width: 80px; height: 80px; border-radius: 50%;
                                       margin: 0 auto; display: flex; align-items: center;
                                       justify-content: center; color: white; font-size: 24px;">
                                🏪
                            </div>
                        </div>

                        <h2 style="color: #333; text-align: center; margin-bottom: 20px;">Store Shared With You</h2>

                        <p style="margin-bottom: 20px;">Hello,</p>

                        <p style="margin-bottom: 20px;">
                            <strong>{request.user.get_full_name() or request.user.username}</strong> has shared a store with you:
                        </p>

                        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                            <h3 style="margin: 0 0 10px 0; color: #333;">{store.name}</h3>
                            {f'<p style="margin: 0; color: #666;">{store.description}</p>' if store.description else ''}
                        </div>

                        {f'<div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;"><p style="margin: 0; font-style: italic;">"{custom_message}"</p></div>' if custom_message else ''}

                        <div style="text-align: center; margin: 30px 0;">
                            <a href="{share_url}"
                               style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                                      color: white; padding: 12px 30px; text-decoration: none;
                                      border-radius: 8px; font-weight: 600; display: inline-block;">
                                View Store
                            </a>
                        </div>

                        <p style="margin-bottom: 20px;">
                            Or copy and paste this link into your browser:<br>
                            <a href="{share_url}" style="color: #667eea; word-break: break-all;">{share_url}</a>
                        </p>

                        <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">

                        <p style="font-size: 14px; color: #666; text-align: center;">
                            This store was shared via Inventory Management System
                        </p>
                    </div>
                </body>
                </html>
                """

                # Create plain text version
                plain_message = f"""
Hello,

{request.user.get_full_name() or request.user.username} has shared a store with you:

Store: {store.name}
{f'Description: {store.description}' if store.description else ''}

{f'Message: "{custom_message}"' if custom_message else ''}

View the store here: {share_url}

Best regards,
Inventory Management System
                """.strip()

                # Send email
                send_mail(
                    subject=subject,
                    message=plain_message,
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[email],
                    html_message=html_message,
                    fail_silently=False,
                )

                successful_emails.append(email)

            except Exception as e:
                logger.error(f"Failed to send email to {email}: {str(e)}")
                failed_emails.append(email)

        return JsonResponse({
            'success': True,
            'share_url': share_url,
            'successful_emails': successful_emails,
            'failed_emails': failed_emails,
            'total_sent': len(successful_emails),
            'total_failed': len(failed_emails)
        })

    except Exception as e:
        logger.error(f"Error sharing store via email: {str(e)}")
        traceback.print_exc()
        return JsonResponse({'error': 'Failed to share store via email'}, status=500)


def shared_store_view(request, token):
    """Public view for shared stores - accessible without login"""
    try:
        store_share = get_object_or_404(StoreShare, share_token=token)

        # Check if share is accessible
        if not store_share.is_accessible():
            if store_share.is_expired():
                return render(request, 'error.html', {
                    'error': 'This shared store link has expired.',
                    'title': 'Link Expired'
                })
            else:
                return render(request, 'error.html', {
                    'error': 'This shared store link is no longer active.',
                    'title': 'Link Inactive'
                })

        # Handle password protection
        if store_share.password_protected:
            if request.method == 'POST':
                password = request.POST.get('password', '')
                if password == store_share.access_password:
                    # Store password verification in session
                    request.session[f'store_share_verified_{token}'] = True
                else:
                    return render(request, 'shared_store_password.html', {
                        'store_share': store_share,
                        'error': 'Incorrect password'
                    })
            else:
                # Check if already verified in session
                if not request.session.get(f'store_share_verified_{token}', False):
                    return render(request, 'shared_store_password.html', {
                        'store_share': store_share
                    })

        # Increment view count
        store_share.increment_view_count()

        # Get store and seller
        store = store_share.store
        seller = store.owner

        # Get all goods from this seller (clone of public seller store logic)
        goods_qs = Goods.objects.filter(owner=seller, is_active=True).select_related('category', 'store')

        # Apply filters (same as public seller store)
        search_query = request.GET.get('q', '').strip()
        category = request.GET.get('category')
        min_price = request.GET.get('min_price')
        max_price = request.GET.get('max_price')
        star_rating = request.GET.get('star_rating')
        is_used = request.GET.get('is_used')
        available_for_delivery = request.GET.get('available_for_delivery')

        if search_query:
            goods_qs = goods_qs.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(store__name__icontains=search_query)
            )

        if category:
            try:
                goods_qs = goods_qs.filter(category_id=int(category))
            except (ValueError, TypeError):
                pass

        if min_price:
            try:
                goods_qs = goods_qs.filter(price__gte=float(min_price))
            except (ValueError, TypeError):
                pass

        if max_price:
            try:
                goods_qs = goods_qs.filter(price__lte=float(max_price))
            except (ValueError, TypeError):
                pass

        if star_rating:
            try:
                goods_qs = goods_qs.filter(star_rating__gte=int(star_rating))
            except (ValueError, TypeError):
                pass

        if is_used in ['true', 'false']:
            goods_qs = goods_qs.filter(is_used=(is_used == 'true'))

        if available_for_delivery in ['true', 'false']:
            goods_qs = goods_qs.filter(available_for_delivery=(available_for_delivery == 'true'))

        # Get categories for this seller
        categories = Category.objects.filter(owner=seller)

        # Get seller's stores
        stores = Store.objects.filter(owner=seller)

        # Prepare goods data (same as public seller store)
        goods_data = []
        for goods in goods_qs:
            goods_data.append({
                'id': goods.id,
                'name': goods.name,
                'description': goods.description,
                'price': goods.price,
                'currency_symbol': goods.currency_symbol,
                'category': goods.category.name if goods.category else 'Uncategorized',
                'store': goods.store.name if goods.store else 'No Store',
                'is_used': goods.is_used,
                'available_for_delivery': goods.available_for_delivery,
                'available_for_bulk_sales': goods.available_for_bulk_sales,
                'star_rating': goods.star_rating,
                'star_rating_display': '⭐' * (goods.star_rating or 0),
                'first_image_url': goods.first_image_url,
                'quantity': goods.quantity,
                'created_at': goods.created_at,
            })

        # Check if current user is the store owner
        is_store_owner = request.user.is_authenticated and seller == request.user

        context = {
            'store_share': store_share,
            'store': store,
            'seller': seller,
            'goods': goods_data,
            'categories': categories,
            'stores': stores,
            'is_store_owner': is_store_owner,
            'total_goods': len(goods_data),
            'total_stores': stores.count(),
            'filters': {
                'search_query': search_query,
                'category': category,
                'min_price': min_price,
                'max_price': max_price,
                'star_rating': star_rating,
                'is_used': is_used,
                'available_for_delivery': available_for_delivery,
            },
            'star_ratings': [(i, f'{"⭐" * i} {i} Star{"s" if i > 1 else ""}') for i in range(1, 6)],
        }

        return render(request, 'shared_store_view.html', context)

    except Exception as e:
        return render(request, 'error.html', {
            'error_message': 'Store not found or no longer available.',
            'error_details': str(e) if settings.DEBUG else None
        })


@login_required
def goods_activity_history(request, goods_id):
    """View for displaying detailed activity history of a specific good"""
    try:
        # Get the goods object and ensure user has access
        goods = get_object_or_404(Goods, id=goods_id)

        # Check if user has permission to view this goods activity
        if goods.owner != request.user and not goods.store.authorized_users.filter(id=request.user.id).exists():
            return render(request, 'error.html', {
                'error_message': 'You do not have permission to view this product\'s activity history.',
                'error_title': 'Access Denied'
            })

        # Get all activities for this goods
        activities = GoodsActivity.objects.filter(goods=goods).select_related('created_by')

        # Apply filters if provided
        activity_type_filter = request.GET.get('activity_type', '')
        date_from = request.GET.get('date_from', '')
        date_to = request.GET.get('date_to', '')

        if activity_type_filter:
            activities = activities.filter(activity_type=activity_type_filter)

        if date_from:
            try:
                from datetime import datetime
                date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                activities = activities.filter(created_at__date__gte=date_from_obj)
            except ValueError:
                pass

        if date_to:
            try:
                from datetime import datetime
                date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                activities = activities.filter(created_at__date__lte=date_to_obj)
            except ValueError:
                pass

        # Pagination
        from django.core.paginator import Paginator
        paginator = Paginator(activities, 20)  # Show 20 activities per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get activity type choices for filter dropdown
        activity_types = GoodsActivity.ACTIVITY_TYPES

        # Calculate some statistics
        total_activities = activities.count()
        stock_changes = activities.filter(activity_type__in=['stock_added', 'stock_reduced']).count()
        price_changes = activities.filter(activity_type='price_updated').count()

        context = {
            'goods': goods,
            'activities': page_obj,
            'total_activities': total_activities,
            'stock_changes': stock_changes,
            'price_changes': price_changes,
            'activity_types': activity_types,
            'filters': {
                'activity_type': activity_type_filter,
                'date_from': date_from,
                'date_to': date_to,
            },
        }

        print(f"DEBUG: About to render template with context keys: {list(context.keys())}")
        print(f"DEBUG: Goods: {goods.name}, Activities count: {total_activities}")
        return render(request, 'goods_activity_history.html', context)

    except Exception as e:
        import traceback
        print(f"ERROR in goods_activity_history: {str(e)}")
        print(f"Traceback: {traceback.format_exc()}")
        return render(request, 'error.html', {
            'error_message': 'Product not found or error loading activity history.',
            'error_details': str(e) if settings.DEBUG else None
        })


# -------------------------------
# Two-Factor Authentication Views
# -------------------------------

import random
import string
from django.core.mail import send_mail
from django.conf import settings
from django.utils import timezone
from datetime import timedelta
import hashlib
import uuid

def generate_2fa_code():
    """Generate a 6-digit 2FA code"""
    return ''.join(random.choices(string.digits, k=6))

def generate_device_token():
    """Generate a unique device token"""
    return hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()

def get_client_ip(request):
    """Get client IP address"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def get_device_name(request):
    """Generate a human-readable device name"""
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    if 'Mobile' in user_agent:
        return 'Mobile Device'
    elif 'Chrome' in user_agent:
        return 'Chrome Browser'
    elif 'Firefox' in user_agent:
        return 'Firefox Browser'
    elif 'Safari' in user_agent:
        return 'Safari Browser'
    else:
        return 'Unknown Device'

@csrf_exempt
def custom_login(request):
    """Custom login view with email verification"""
    if request.method == 'POST':
        username = request.POST.get('username')
        password = request.POST.get('password')
        remember_me = request.POST.get('remember_me')  # Get remember me checkbox

        print(f"🔍 LOGIN ATTEMPT: username={username}, password={'*' * len(password) if password else 'None'}")

        # Authenticate user
        user = authenticate(request, username=username, password=password)
        print(f"🔍 AUTHENTICATION RESULT: user={user}")

        if user is not None:
            # Always require email verification (comment out trusted device check for now)
            # Check if device is trusted (skip verification for 7 days)
            # device_token = request.COOKIES.get('device_token')
            ip_address = get_client_ip(request)

            # trusted_device = None
            # if device_token:
            #     try:
            #         trusted_device = TrustedDevice.objects.get(
            #             user=user,
            #             device_token=device_token,
            #             is_active=True
            #         )
            #         if trusted_device.is_valid():
            #             # Device is trusted, log in directly
            #             login(request, user)
            #             trusted_device.last_used = timezone.now()
            #             trusted_device.save()
            #             return redirect('private_seller_store')
            #     except TrustedDevice.DoesNotExist:
            #         pass

            # Generate and send verification code
            print(f"🔍 GENERATING VERIFICATION CODE for user: {user.username}")
            code = generate_2fa_code()
            expires_at = timezone.now() + timedelta(minutes=10)
            print(f"🔍 GENERATED CODE: {code}")

            # Clear any existing unused codes for this user
            TwoFactorAuth.objects.filter(user=user, is_used=False).update(is_used=True)

            verification = TwoFactorAuth.objects.create(
                user=user,
                code=code,
                expires_at=expires_at,
                ip_address=ip_address,
                user_agent=request.META.get('HTTP_USER_AGENT', '')
            )
            print(f"🔍 CREATED VERIFICATION RECORD: {verification.id}")

            # Send email with verification code
            try:
                print(f"🔍 ATTEMPTING TO SEND EMAIL to: {user.email}")
                send_mail(
                    'Login Verification Code',
                    f'Hello {user.username},\n\nYour login verification code is: {code}\n\nThis code will expire in 10 minutes.\n\nIf you did not attempt to log in, please ignore this email.\n\nBest regards,\nInventory Management Team',
                    settings.DEFAULT_FROM_EMAIL,
                    [user.email],
                    fail_silently=False,
                )

                print(f"🔍 EMAIL SENT SUCCESSFULLY")

                # Store user ID and remember me preference in session for verification
                request.session['verification_user_id'] = user.id
                request.session['verification_pending'] = True
                request.session['remember_me'] = bool(remember_me)  # Store remember me preference
                print(f"🔍 SESSION DATA SET: user_id={user.id}, remember_me={bool(remember_me)}")

                print(f"🔍 REDIRECTING TO EMAIL VERIFICATION PAGE")
                return render(request, 'email_verify.html', {
                    'email': user.email,
                    'message': 'A verification code has been sent to your email address.'
                })

            except Exception as e:
                print(f"❌ EMAIL SENDING FAILED: {str(e)}")
                import traceback
                traceback.print_exc()
                return render(request, 'login.html', {
                    'error': 'Failed to send verification code. Please try again.',
                    'form_data': request.POST
                })
        else:
            return render(request, 'login.html', {
                'error': 'Invalid username or password.',
                'form_data': request.POST
            })

    return render(request, 'login.html')

@csrf_exempt
def verify_email_code(request):
    """Verify email code and complete login"""
    if request.method == 'POST':
        code = request.POST.get('code')
        user_id = request.session.get('verification_user_id')

        if not user_id or not request.session.get('verification_pending'):
            return redirect('login')

        try:
            user = User.objects.get(id=user_id)
            verification = TwoFactorAuth.objects.filter(
                user=user,
                code=code,
                is_used=False
            ).first()

            if verification and verification.is_valid():
                # Mark code as used
                verification.is_used = True
                verification.save()

                # Log in user
                login(request, user)

                # Handle session persistence (remember me functionality)
                remember_me = request.session.get('remember_me', False)
                if remember_me:
                    # Set session to expire in 30 days
                    request.session.set_expiry(30 * 24 * 60 * 60)
                else:
                    # Set session to expire when browser closes
                    request.session.set_expiry(0)

                # Create trusted device if requested
                trust_device = request.POST.get('trust_device')
                if trust_device:
                    device_token = generate_device_token()
                    device_name = get_device_name(request)
                    ip_address = get_client_ip(request)
                    user_agent = request.META.get('HTTP_USER_AGENT', '')
                    expires_at = timezone.now() + timedelta(days=7)

                    TrustedDevice.objects.create(
                        user=user,
                        device_token=device_token,
                        device_name=device_name,
                        ip_address=ip_address,
                        user_agent=user_agent,
                        expires_at=expires_at
                    )

                    # Set cookie for trusted device
                    response = redirect('private_seller_store')
                    response.set_cookie(
                        'device_token',
                        device_token,
                        max_age=7*24*60*60,  # 7 days
                        secure=False,  # Set to True in production with HTTPS
                        httponly=True
                    )

                    # Clear session
                    del request.session['verification_user_id']
                    del request.session['verification_pending']
                    if 'remember_me' in request.session:
                        del request.session['remember_me']

                    return response

                # Clear session
                del request.session['verification_user_id']
                del request.session['verification_pending']
                if 'remember_me' in request.session:
                    del request.session['remember_me']

                return redirect('private_seller_store')
            else:
                return render(request, 'email_verify.html', {
                    'error': 'Invalid or expired verification code.',
                    'email': user.email
                })

        except User.DoesNotExist:
            return redirect('login')

    # If GET request, check if verification is pending
    if not request.session.get('verification_pending'):
        return redirect('login')

    user_id = request.session.get('verification_user_id')
    try:
        user = User.objects.get(id=user_id)
        return render(request, 'email_verify.html', {
            'email': user.email
        })
    except User.DoesNotExist:
        return redirect('login')

@login_required
def manage_trusted_devices(request):
    """Manage user's trusted devices"""
    devices = TrustedDevice.objects.filter(user=request.user, is_active=True)

    if request.method == 'POST':
        action = request.POST.get('action')
        device_id = request.POST.get('device_id')

        if action == 'revoke' and device_id:
            try:
                device = TrustedDevice.objects.get(id=device_id, user=request.user)
                device.is_active = False
                device.save()
                return JsonResponse({'success': True, 'message': 'Device trust revoked'})
            except TrustedDevice.DoesNotExist:
                return JsonResponse({'success': False, 'error': 'Device not found'})

    return render(request, 'manage_trusted_devices.html', {
        'devices': devices
    })


# -------------------------------
# Contact Support Views
# -------------------------------

def contact_support(request):
    """Contact support form for users"""
    if request.method == 'POST':
        name = request.POST.get('name')
        email = request.POST.get('email')
        subject = request.POST.get('subject')
        message = request.POST.get('message')
        category = request.POST.get('category', 'other')

        if not all([name, email, subject, message]):
            return render(request, 'contact_support.html', {
                'error': 'All fields are required.',
                'form_data': request.POST
            })

        # Create support ticket
        ticket = SupportTicket.objects.create(
            user=request.user if request.user.is_authenticated else None,
            name=name,
            email=email,
            subject=subject,
            message=message,
            category=category
        )

        # Send confirmation email to user
        try:
            send_mail(
                f'Support Ticket #{ticket.id} - {subject}',
                f'Dear {name},\n\nThank you for contacting our support team. We have received your message and will respond within 24 hours.\n\nTicket ID: #{ticket.id}\nSubject: {subject}\n\nYour message:\n{message}\n\nBest regards,\nSupport Team',
                settings.DEFAULT_FROM_EMAIL,
                [email],
                fail_silently=True,
            )
        except:
            pass

        # Send notification to admin
        try:
            admin_emails = [user.email for user in User.objects.filter(is_staff=True)]
            if admin_emails:
                send_mail(
                    f'New Support Ticket #{ticket.id} - {subject}',
                    f'A new support ticket has been created.\n\nTicket ID: #{ticket.id}\nFrom: {name} ({email})\nCategory: {ticket.get_category_display()}\nSubject: {subject}\n\nMessage:\n{message}',
                    settings.DEFAULT_FROM_EMAIL,
                    admin_emails,
                    fail_silently=True,
                )
        except:
            pass

        return render(request, 'contact_support.html', {
            'success': f'Your support ticket #{ticket.id} has been submitted successfully. We will respond within 24 hours.',
            'ticket_id': ticket.id
        })

    return render(request, 'contact_support.html')

@login_required
def my_support_tickets(request):
    """View user's support tickets"""
    tickets = SupportTicket.objects.filter(
        models.Q(user=request.user) | models.Q(email=request.user.email)
    ).order_by('-created_at')

    return render(request, 'my_support_tickets.html', {
        'tickets': tickets
    })

@login_required
def support_ticket_detail(request, ticket_id):
    """View support ticket details"""
    ticket = get_object_or_404(SupportTicket, id=ticket_id)

    # Check if user has permission to view this ticket
    if not request.user.is_staff and ticket.user != request.user and ticket.email != request.user.email:
        return render(request, 'error.html', {
            'error_message': 'You do not have permission to view this ticket.',
            'error_title': 'Access Denied'
        })

    return render(request, 'support_ticket_detail.html', {
        'ticket': ticket
    })


# -------------------------------
# Admin Views
# -------------------------------

from django.contrib.admin.views.decorators import staff_member_required
from django.core.paginator import Paginator

@staff_member_required
def admin_dashboard(request):
    """Admin dashboard with system overview"""
    # Get statistics
    total_users = User.objects.count()
    total_goods = Goods.objects.count()
    total_stores = Store.objects.count()
    total_properties = Property.objects.count()
    open_tickets = SupportTicket.objects.filter(status='open').count()

    # Recent activities
    recent_users = User.objects.order_by('-date_joined')[:10]
    recent_goods = Goods.objects.order_by('-created_at')[:10]
    recent_tickets = SupportTicket.objects.order_by('-created_at')[:10]

    context = {
        'stats': {
            'total_users': total_users,
            'total_goods': total_goods,
            'total_stores': total_stores,
            'total_properties': total_properties,
            'open_tickets': open_tickets,
        },
        'recent_users': recent_users,
        'recent_goods': recent_goods,
        'recent_tickets': recent_tickets,
    }

    return render(request, 'admin/dashboard.html', context)

@staff_member_required
def admin_users(request):
    """Admin user management"""
    search_query = request.GET.get('q', '')
    status_filter = request.GET.get('status', '')

    users = User.objects.all().order_by('-date_joined')

    if search_query:
        users = users.filter(
            models.Q(username__icontains=search_query) |
            models.Q(email__icontains=search_query) |
            models.Q(first_name__icontains=search_query) |
            models.Q(last_name__icontains=search_query)
        )

    if status_filter == 'active':
        users = users.filter(is_active=True)
    elif status_filter == 'inactive':
        users = users.filter(is_active=False)
    elif status_filter == 'staff':
        users = users.filter(is_staff=True)

    # Pagination
    paginator = Paginator(users, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    if request.method == 'POST':
        action = request.POST.get('action')
        user_id = request.POST.get('user_id')

        try:
            user = User.objects.get(id=user_id)

            if action == 'ban':
                user.is_active = False
                user.save()
                return JsonResponse({'success': True, 'message': f'User {user.username} has been banned'})
            elif action == 'unban':
                user.is_active = True
                user.save()
                return JsonResponse({'success': True, 'message': f'User {user.username} has been unbanned'})
            elif action == 'make_staff':
                user.is_staff = True
                user.save()
                return JsonResponse({'success': True, 'message': f'User {user.username} is now staff'})
            elif action == 'remove_staff':
                user.is_staff = False
                user.save()
                return JsonResponse({'success': True, 'message': f'Staff privileges removed from {user.username}'})
            elif action == 'delete_user':
                if user.is_superuser:
                    return JsonResponse({'success': False, 'error': 'Cannot delete superuser'})
                username = user.username
                user.delete()
                return JsonResponse({'success': True, 'message': f'User {username} has been deleted'})
            elif action == 'make_superuser':
                user.is_superuser = True
                user.is_staff = True
                user.save()
                return JsonResponse({'success': True, 'message': f'User {user.username} is now a superuser'})
            elif action == 'remove_superuser':
                if user == request.user:
                    return JsonResponse({'success': False, 'error': 'Cannot remove your own superuser status'})
                user.is_superuser = False
                user.save()
                return JsonResponse({'success': True, 'message': f'Superuser privileges removed from {user.username}'})

        except User.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'User not found'})

    return render(request, 'admin/users.html', {
        'users': page_obj,
        'search_query': search_query,
        'status_filter': status_filter,
    })

@staff_member_required
def admin_goods(request):
    """Admin goods management"""
    search_query = request.GET.get('q', '')

    goods = Goods.objects.all().select_related('owner', 'store', 'category').order_by('-created_at')

    if search_query:
        goods = goods.filter(
            models.Q(name__icontains=search_query) |
            models.Q(description__icontains=search_query) |
            models.Q(owner__username__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(goods, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    if request.method == 'POST':
        action = request.POST.get('action')
        goods_id = request.POST.get('goods_id')

        try:
            good = Goods.objects.get(id=goods_id)

            if action == 'delete':
                good.delete()
                return JsonResponse({'success': True, 'message': f'Product "{good.name}" has been deleted'})

        except Goods.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Product not found'})

    return render(request, 'admin/goods.html', {
        'goods': page_obj,
        'search_query': search_query,
    })

@staff_member_required
def admin_support_tickets(request):
    """Admin support ticket management"""
    status_filter = request.GET.get('status', '')
    category_filter = request.GET.get('category', '')

    tickets = SupportTicket.objects.all().order_by('-created_at')

    if status_filter:
        tickets = tickets.filter(status=status_filter)

    if category_filter:
        tickets = tickets.filter(category=category_filter)

    # Pagination
    paginator = Paginator(tickets, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    if request.method == 'POST':
        action = request.POST.get('action')
        ticket_id = request.POST.get('ticket_id')

        try:
            ticket = SupportTicket.objects.get(id=ticket_id)

            if action == 'resolve':
                ticket.status = 'resolved'
                ticket.resolved_at = timezone.now()
                ticket.save()
                return JsonResponse({'success': True, 'message': f'Ticket #{ticket.id} marked as resolved'})
            elif action == 'close':
                ticket.status = 'closed'
                ticket.save()
                return JsonResponse({'success': True, 'message': f'Ticket #{ticket.id} closed'})
            elif action == 'assign':
                ticket.assigned_to = request.user
                ticket.status = 'in_progress'
                ticket.save()
                return JsonResponse({'success': True, 'message': f'Ticket #{ticket.id} assigned to you'})

        except SupportTicket.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Ticket not found'})

    return render(request, 'admin/support_tickets.html', {
        'tickets': page_obj,
        'status_filter': status_filter,
        'category_filter': category_filter,
        'status_choices': SupportTicket.STATUS_CHOICES,
        'category_choices': SupportTicket.CATEGORY_CHOICES,
    })


@staff_member_required
def admin_stores(request):
    """Admin store management"""
    search_query = request.GET.get('q', '')

    stores = Store.objects.all().select_related('owner').order_by('-created_at')

    if search_query:
        stores = stores.filter(
            models.Q(name__icontains=search_query) |
            models.Q(description__icontains=search_query) |
            models.Q(owner__username__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(stores, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    if request.method == 'POST':
        action = request.POST.get('action')
        store_id = request.POST.get('store_id')

        try:
            store = Store.objects.get(id=store_id)

            if action == 'delete':
                store_name = store.name
                store.delete()
                return JsonResponse({'success': True, 'message': f'Store "{store_name}" has been deleted'})

        except Store.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Store not found'})

    return render(request, 'admin/stores.html', {
        'stores': page_obj,
        'search_query': search_query,
    })


@staff_member_required
def admin_properties(request):
    """Admin property management"""
    search_query = request.GET.get('q', '')

    properties = Property.objects.all().select_related('owner').order_by('-created_at')

    if search_query:
        properties = properties.filter(
            models.Q(name__icontains=search_query) |
            models.Q(description__icontains=search_query) |
            models.Q(owner__username__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(properties, 25)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    if request.method == 'POST':
        action = request.POST.get('action')
        property_id = request.POST.get('property_id')

        try:
            property_obj = Property.objects.get(id=property_id)

            if action == 'delete':
                property_name = property_obj.name
                property_obj.delete()
                return JsonResponse({'success': True, 'message': f'Property "{property_name}" has been deleted'})

        except Property.DoesNotExist:
            return JsonResponse({'success': False, 'error': 'Property not found'})

    return render(request, 'admin/properties.html', {
        'properties': page_obj,
        'search_query': search_query,
    })


@staff_member_required
def admin_system_stats(request):
    """Admin system statistics and analytics"""
    from django.db.models import Count, Sum
    from datetime import datetime, timedelta

    # Date ranges
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    # User statistics
    total_users = User.objects.count()
    active_users = User.objects.filter(is_active=True).count()
    new_users_week = User.objects.filter(date_joined__gte=week_ago).count()
    new_users_month = User.objects.filter(date_joined__gte=month_ago).count()

    # Content statistics
    total_goods = Goods.objects.count()
    total_stores = Store.objects.count()
    total_properties = Property.objects.count()
    properties_for_sale = Property.objects.filter(for_sale=True).count()

    # Support statistics
    total_tickets = SupportTicket.objects.count()
    open_tickets = SupportTicket.objects.filter(status='open').count()
    resolved_tickets = SupportTicket.objects.filter(status='resolved').count()

    # Recent activity
    recent_logins = TwoFactorAuth.objects.filter(
        created_at__gte=week_ago,
        is_used=True
    ).count()

    context = {
        'stats': {
            'users': {
                'total': total_users,
                'active': active_users,
                'new_week': new_users_week,
                'new_month': new_users_month,
            },
            'content': {
                'goods': total_goods,
                'stores': total_stores,
                'properties': total_properties,
                'properties_for_sale': properties_for_sale,
            },
            'support': {
                'total_tickets': total_tickets,
                'open_tickets': open_tickets,
                'resolved_tickets': resolved_tickets,
            },
            'activity': {
                'recent_logins': recent_logins,
            }
        }
    }

    return render(request, 'admin/system_stats.html', context)


@staff_member_required
def admin_bulk_actions(request):
    """Admin bulk actions for mass operations"""
    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'cleanup_expired_codes':
            # Clean up expired verification codes
            expired_codes = TwoFactorAuth.objects.filter(
                expires_at__lt=timezone.now()
            )
            count = expired_codes.count()
            expired_codes.delete()
            return JsonResponse({'success': True, 'message': f'Cleaned up {count} expired verification codes'})

        elif action == 'cleanup_old_devices':
            # Clean up expired trusted devices
            expired_devices = TrustedDevice.objects.filter(
                expires_at__lt=timezone.now()
            )
            count = expired_devices.count()
            expired_devices.delete()
            return JsonResponse({'success': True, 'message': f'Cleaned up {count} expired trusted devices'})

        elif action == 'close_old_tickets':
            # Close tickets older than 30 days that are resolved
            old_date = timezone.now() - timedelta(days=30)
            old_tickets = SupportTicket.objects.filter(
                status='resolved',
                resolved_at__lt=old_date
            )
            count = old_tickets.count()
            old_tickets.update(status='closed')
            return JsonResponse({'success': True, 'message': f'Closed {count} old resolved tickets'})

    return render(request, 'admin/bulk_actions.html')


@login_required
def list_store_shares(request, store_id):
    """List all shares for a specific store"""
    try:
        store = get_object_or_404(Store, pk=store_id)

        # Check if user has permission to view shares
        if store.owner != request.user and request.user not in store.authorized_users.all():
            return JsonResponse({'error': 'You do not have permission to view shares for this store'}, status=403)

        shares = store.shares.all().order_by('-created_at')

        shares_data = []
        for share in shares:
            shares_data.append({
                'id': share.id,
                'title': share.title,
                'description': share.description,
                'share_token': share.share_token,
                'share_url': share.get_share_url(request),
                'is_active': share.is_active,
                'is_expired': share.is_expired(),
                'password_protected': share.password_protected,
                'view_count': share.view_count,
                'shared_via_email': share.shared_via_email,
                'recipient_emails': share.recipient_emails,
                'expires_at': share.expires_at.isoformat() if share.expires_at else None,
                'last_accessed': share.last_accessed.isoformat() if share.last_accessed else None,
                'created_at': share.created_at.isoformat(),
                'created_by': share.created_by.username
            })

        return JsonResponse({
            'success': True,
            'store': {
                'id': store.id,
                'name': store.name,
                'description': store.description
            },
            'shares': shares_data
        })

    except Exception as e:
        logger.error(f"Error listing store shares: {str(e)}")
        traceback.print_exc()
        return JsonResponse({'error': 'Failed to list store shares'}, status=500)


# -------------------------------
# Property Management Views
# -------------------------------

@login_required
def property_management(request):
    """Main property management view"""
    if request.method == 'GET':
        # Get user's properties
        properties = Property.objects.filter(owner=request.user).order_by('-created_at')

        # Apply filters
        property_type = request.GET.get('type', '')
        search_query = request.GET.get('search', '')

        if property_type:
            properties = properties.filter(property_type=property_type)

        if search_query:
            properties = properties.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(brand__icontains=search_query) |
                Q(tags__icontains=search_query)
            )

        # Calculate statistics
        total_properties = properties.count()
        total_purchase_value = properties.aggregate(
            total=Sum('purchase_price')
        )['total'] or 0
        total_current_value = properties.aggregate(
            total=Sum('current_value')
        )['total'] or 0

        # Get property types for filter
        property_types = Property.PROPERTY_TYPES

        context = {
            'properties': properties,
            'property_types': property_types,
            'total_properties': total_properties,
            'total_purchase_value': total_purchase_value,
            'total_current_value': total_current_value,
            'selected_type': property_type,
            'search_query': search_query,
        }

        return render(request, 'property_management.html', context)

    elif request.method == 'POST':
        # Create new property
        try:
            # Get form data
            name = request.POST.get('name', '').strip()
            description = request.POST.get('description', '').strip()
            property_type = request.POST.get('property_type', 'other')
            condition = request.POST.get('condition', 'good')

            # Financial data
            purchase_price = request.POST.get('purchase_price', '')
            current_value = request.POST.get('current_value', '')
            currency = request.POST.get('currency', 'NGN')

            # Dates
            purchase_date = request.POST.get('purchase_date', '')
            warranty_expiry = request.POST.get('warranty_expiry', '')
            insurance_expiry = request.POST.get('insurance_expiry', '')

            # Location
            location = request.POST.get('location', '').strip()

            # Documentation
            serial_number = request.POST.get('serial_number', '').strip()
            model_number = request.POST.get('model_number', '').strip()
            brand = request.POST.get('brand', '').strip()

            # Insurance
            insured = request.POST.get('insured') == 'on'
            insurance_value = request.POST.get('insurance_value', '')

            # Notes and tags
            notes = request.POST.get('notes', '').strip()
            tags = request.POST.get('tags', '').strip()

            if not name:
                return JsonResponse({'error': 'Property name is required'}, status=400)

            # Create property
            property_obj = Property.objects.create(
                owner=request.user,
                name=name,
                description=description,
                property_type=property_type,
                condition=condition,
                currency=currency,
                location=location,
                serial_number=serial_number,
                model_number=model_number,
                brand=brand,
                insured=insured,
                notes=notes,
                tags=tags
            )

            # Handle optional decimal fields
            if purchase_price:
                try:
                    property_obj.purchase_price = Decimal(purchase_price)
                except (ValueError, InvalidOperation):
                    pass

            if current_value:
                try:
                    property_obj.current_value = Decimal(current_value)
                except (ValueError, InvalidOperation):
                    pass

            if insurance_value and insured:
                try:
                    property_obj.insurance_value = Decimal(insurance_value)
                except (ValueError, InvalidOperation):
                    pass

            # Handle dates
            if purchase_date:
                try:
                    property_obj.purchase_date = parse_date(purchase_date)
                except ValueError:
                    pass

            if warranty_expiry:
                try:
                    property_obj.warranty_expiry = parse_date(warranty_expiry)
                except ValueError:
                    pass

            if insurance_expiry:
                try:
                    property_obj.insurance_expiry = parse_date(insurance_expiry)
                except ValueError:
                    pass

            # Handle sale fields
            if request.POST.get('for_sale') == 'on':
                property_obj.for_sale = True
                sale_price = request.POST.get('sale_price', '')
                if sale_price:
                    try:
                        property_obj.sale_price = Decimal(sale_price)
                    except (ValueError, InvalidOperation):
                        pass

                property_obj.is_negotiable = request.POST.get('is_negotiable') == 'on'
                property_obj.sale_description = request.POST.get('sale_description', '')
                property_obj.contact_phone = request.POST.get('contact_phone', '')
                property_obj.contact_email = request.POST.get('contact_email', request.user.email)

            # Handle image uploads
            images = request.FILES.getlist('images')
            if images:
                import time
                from django.core.files.storage import default_storage

                image_filenames = []
                for image in images:
                    # Save using Django's default storage (same as goods)
                    filename = f"property_images/{property_obj.id}_{int(time.time())}_{image.name}"
                    saved_path = default_storage.save(filename, image)
                    image_filenames.append(saved_path)

                # Update property with image filenames
                property_obj.images = image_filenames

            property_obj.save()

            return JsonResponse({
                'success': True,
                'message': 'Property created successfully',
                'property_id': property_obj.id
            })

        except Exception as e:
            logger.error(f"Error creating property: {str(e)}")
            traceback.print_exc()
            return JsonResponse({'error': 'Failed to create property'}, status=500)

    else:
        return JsonResponse({'error': 'Method not allowed'}, status=405)


@csrf_exempt
@login_required
def property_detail(request, property_id):
    """Property detail view with update and delete functionality"""
    try:
        property_obj = get_object_or_404(Property, pk=property_id, owner=request.user)

        if request.method == 'GET':
            # Return property details
            property_data = {
                'id': property_obj.id,
                'name': property_obj.name,
                'description': property_obj.description,
                'property_type': property_obj.property_type,
                'property_type_display': property_obj.get_property_type_display(),
                'condition': property_obj.condition,
                'condition_display': property_obj.get_condition_display(),
                'purchase_price': float(property_obj.purchase_price) if property_obj.purchase_price else None,
                'current_value': float(property_obj.current_value) if property_obj.current_value else None,
                'currency': property_obj.currency,
                'purchase_date': property_obj.purchase_date.isoformat() if property_obj.purchase_date else None,
                'warranty_expiry': property_obj.warranty_expiry.isoformat() if property_obj.warranty_expiry else None,
                'location': property_obj.location,
                'serial_number': property_obj.serial_number,
                'model_number': property_obj.model_number,
                'brand': property_obj.brand,
                'insured': property_obj.insured,
                'insurance_value': float(property_obj.insurance_value) if property_obj.insurance_value else None,
                'insurance_expiry': property_obj.insurance_expiry.isoformat() if property_obj.insurance_expiry else None,
                'notes': property_obj.notes,
                'tags': property_obj.tags,
                'tags_list': property_obj.get_tags_list(),
                'created_at': property_obj.created_at.isoformat(),
                'updated_at': property_obj.updated_at.isoformat(),
                'depreciation': float(property_obj.get_depreciation()) if property_obj.get_depreciation() else None,
                'appreciation': float(property_obj.get_appreciation()) if property_obj.get_appreciation() else None,
                'value_change_percentage': property_obj.get_value_change_percentage(),
                'is_warranty_valid': property_obj.is_warranty_valid(),
                'is_insurance_valid': property_obj.is_insurance_valid(),
                # Sale fields
                'for_sale': property_obj.for_sale,
                'sale_price': float(property_obj.sale_price) if property_obj.sale_price else None,
                'is_negotiable': property_obj.is_negotiable,
                'sale_description': property_obj.sale_description,
                'contact_phone': property_obj.contact_phone,
                'contact_email': property_obj.contact_email,
                # Images
                'images': property_obj.images if property_obj.images else [],
            }

            if request.headers.get('Accept') == 'application/json':
                return JsonResponse({'success': True, 'property': property_data})
            else:
                return render(request, 'property_detail.html', {'property': property_obj})

        elif request.method == 'PUT' or (request.method == 'POST' and request.POST.get('_method') == 'PUT'):
            # Update property
            try:
                logger.info(f"Updating property {property_id}")
                logger.info(f"Request method: {request.method}")
                logger.info(f"Content type: {request.content_type}")
                logger.info(f"POST data: {request.POST}")
                logger.info(f"FILES: {list(request.FILES.keys())}")

                # Handle both JSON and FormData
                if request.content_type and 'application/json' in request.content_type:
                    data = json.loads(request.body)
                else:
                    # Handle FormData from form submission
                    data = request.POST.dict()

                    # Handle file uploads for edit
                    images = request.FILES.getlist('images')
                    if images:
                        import time
                        from django.core.files.storage import default_storage

                        new_images = []
                        for image in images:
                            try:
                                # Save using Django's default storage (same as goods)
                                filename = f"property_images/{property_obj.id}_{int(time.time())}_{image.name}"
                                saved_path = default_storage.save(filename, image)
                                new_images.append(saved_path)
                            except Exception as img_error:
                                logger.error(f"Error saving image {image.name}: {str(img_error)}")
                                continue

                        # Add new images to existing ones
                        existing_images = property_obj.images or []
                        property_obj.images = existing_images + new_images

                # Update basic fields
                property_obj.name = data.get('name', property_obj.name)
                property_obj.description = data.get('description', property_obj.description)
                property_obj.property_type = data.get('property_type', property_obj.property_type)
                property_obj.condition = data.get('condition', property_obj.condition)
                property_obj.currency = data.get('currency', property_obj.currency)
                property_obj.location = data.get('location', property_obj.location)
                property_obj.serial_number = data.get('serial_number', property_obj.serial_number)
                property_obj.model_number = data.get('model_number', property_obj.model_number)
                property_obj.brand = data.get('brand', property_obj.brand)
                property_obj.insured = data.get('insured', property_obj.insured)
                property_obj.notes = data.get('notes', property_obj.notes)
                property_obj.tags = data.get('tags', property_obj.tags)

                # Update decimal fields
                if 'purchase_price' in data:
                    try:
                        property_obj.purchase_price = Decimal(str(data['purchase_price'])) if data['purchase_price'] else None
                    except (ValueError, InvalidOperation):
                        pass

                if 'current_value' in data:
                    try:
                        property_obj.current_value = Decimal(str(data['current_value'])) if data['current_value'] else None
                    except (ValueError, InvalidOperation):
                        pass

                if 'insurance_value' in data:
                    try:
                        property_obj.insurance_value = Decimal(str(data['insurance_value'])) if data['insurance_value'] else None
                    except (ValueError, InvalidOperation):
                        pass

                # Update date fields
                if 'purchase_date' in data:
                    try:
                        property_obj.purchase_date = parse_date(data['purchase_date']) if data['purchase_date'] else None
                    except ValueError:
                        pass

                if 'warranty_expiry' in data:
                    try:
                        property_obj.warranty_expiry = parse_date(data['warranty_expiry']) if data['warranty_expiry'] else None
                    except ValueError:
                        pass

                if 'insurance_expiry' in data:
                    try:
                        property_obj.insurance_expiry = parse_date(data['insurance_expiry']) if data['insurance_expiry'] else None
                    except ValueError:
                        pass

                # Handle sale fields
                if 'for_sale' in data:
                    # Handle checkbox value ('on' when checked, missing when unchecked)
                    property_obj.for_sale = data.get('for_sale') == 'on'

                    if property_obj.for_sale:
                        # Update sale-related fields
                        if 'sale_price' in data:
                            try:
                                property_obj.sale_price = Decimal(str(data['sale_price'])) if data['sale_price'] else None
                            except (ValueError, InvalidOperation):
                                pass

                        # Handle checkbox value for is_negotiable
                        property_obj.is_negotiable = data.get('is_negotiable') == 'on'
                        property_obj.sale_description = data.get('sale_description', '')
                        property_obj.contact_phone = data.get('contact_phone', '')
                        property_obj.contact_email = data.get('contact_email', property_obj.owner.email)
                    else:
                        # Clear sale fields if not for sale
                        property_obj.sale_price = None
                        property_obj.is_negotiable = False
                        property_obj.sale_description = ''
                        property_obj.contact_phone = ''
                        property_obj.contact_email = ''

                property_obj.save()

                return JsonResponse({
                    'success': True,
                    'message': 'Property updated successfully'
                })

            except json.JSONDecodeError:
                return JsonResponse({'error': 'Invalid JSON data'}, status=400)
            except Exception as e:
                logger.error(f"Error updating property {property_id}: {str(e)}")
                traceback.print_exc()
                return JsonResponse({'error': f'Update failed: {str(e)}'}, status=500)

        elif request.method == 'DELETE':
            # Delete property
            property_name = property_obj.name
            property_obj.delete()

            return JsonResponse({
                'success': True,
                'message': f'Property "{property_name}" deleted successfully'
            })

        else:
            return JsonResponse({'error': 'Method not allowed'}, status=405)

    except Exception as e:
        logger.error(f"Error in property_detail: {str(e)}")
        traceback.print_exc()
        return JsonResponse({'error': 'An error occurred'}, status=500)


@login_required
@require_GET
def all_goods_activity_history(request):
    """
    Display full activity history for all user's goods with pagination and filtering
    """
    try:
        # Get filter parameters
        goods_id = request.GET.get('goods_id')
        activity_type = request.GET.get('activity_type')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')

        # Base queryset - get all user's goods
        user_goods = Goods.objects.filter(owner=request.user)

        # If specific goods is requested, filter to that
        if goods_id and goods_id.isdigit():
            user_goods = user_goods.filter(id=goods_id)

        # Simulate activity data (replace with actual model when available)
        activities = []
        for goods in user_goods:
            # Add some sample activities for each goods
            activities.extend([
                {
                    'id': f"{goods.id}_1",
                    'goods_id': goods.id,
                    'goods_name': goods.name,
                    'action': 'Stock Added',
                    'description': f'Added 10 units to {goods.name}',
                    'quantity_change': 10,
                    'timestamp': timezone.now() - timezone.timedelta(hours=2),
                    'icon': 'plus',
                    'type': 'stock_add'
                },
                {
                    'id': f"{goods.id}_2",
                    'goods_id': goods.id,
                    'goods_name': goods.name,
                    'action': 'Sale Recorded',
                    'description': f'Sold 2 units of {goods.name}',
                    'quantity_change': -2,
                    'timestamp': timezone.now() - timezone.timedelta(hours=5),
                    'icon': 'shopping-cart',
                    'type': 'sale'
                },
                {
                    'id': f"{goods.id}_3",
                    'goods_id': goods.id,
                    'goods_name': goods.name,
                    'action': 'Price Updated',
                    'description': f'Updated price for {goods.name}',
                    'quantity_change': 0,
                    'timestamp': timezone.now() - timezone.timedelta(days=1),
                    'icon': 'dollar-sign',
                    'type': 'price_update'
                },
                {
                    'id': f"{goods.id}_4",
                    'goods_id': goods.id,
                    'goods_name': goods.name,
                    'action': 'Stock Adjustment',
                    'description': f'Manual stock adjustment for {goods.name}',
                    'quantity_change': -1,
                    'timestamp': timezone.now() - timezone.timedelta(days=2),
                    'icon': 'edit',
                    'type': 'adjustment'
                }
            ])

        # Apply filters
        if activity_type:
            activities = [a for a in activities if a['type'] == activity_type]

        # Sort by timestamp (newest first)
        activities.sort(key=lambda x: x['timestamp'], reverse=True)

        # Pagination
        from django.core.paginator import Paginator
        paginator = Paginator(activities, 20)  # 20 activities per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Get goods for filter dropdown
        goods_list = Goods.objects.filter(owner=request.user).values('id', 'name')

        context = {
            'activities': page_obj,
            'goods_list': goods_list,
            'selected_goods_id': goods_id,
            'selected_activity_type': activity_type,
            'activity_types': [
                ('stock_add', 'Stock Added'),
                ('sale', 'Sales'),
                ('price_update', 'Price Updates'),
                ('adjustment', 'Adjustments'),
            ],
            'total_activities': len(activities),
        }

        return render(request, 'goods_activity_history.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error", "details": str(e)})


@login_required
@require_GET
def goods_detail_with_activity(request, goods_id):
    """
    Display detailed information about a specific goods item with activity history
    """
    try:
        # Get the goods item
        try:
            goods = Goods.objects.get(id=goods_id, owner=request.user)
        except Goods.DoesNotExist:
            return render(request, 'error.html', {"error": "Product not found or you don't have permission to view it"})

        # Generate dynamic activity data for this specific goods
        import random
        random.seed(goods.id)  # Use product ID as seed for consistent data

        activities = []

        # Generate varied activities based on the product
        activity_templates = [
            {
                'action': 'Sale Recorded',
                'description': f'Sold {random.randint(1, 5)} units of {goods.name}',
                'quantity_change': -random.randint(1, 5),
                'icon': 'shopping-cart',
                'type': 'sale'
            },
            {
                'action': 'Stock Added',
                'description': f'Added {random.randint(5, 20)} units to {goods.name}',
                'quantity_change': random.randint(5, 20),
                'icon': 'plus',
                'type': 'stock_add'
            },
            {
                'action': 'Price Updated',
                'description': f'Updated price for {goods.name} to {goods.currency_symbol}{goods.price}',
                'quantity_change': 0,
                'icon': 'dollar-sign',
                'type': 'price_update'
            },
            {
                'action': 'Stock Adjustment',
                'description': f'Manual stock adjustment for {goods.name}',
                'quantity_change': random.randint(-3, 3),
                'icon': 'edit',
                'type': 'adjustment'
            },
            {
                'action': 'Product Created',
                'description': f'Product {goods.name} was created',
                'quantity_change': 0,
                'icon': 'plus-circle',
                'type': 'creation'
            },
            {
                'action': 'Inventory Check',
                'description': f'Inventory count verified for {goods.name}',
                'quantity_change': 0,
                'icon': 'clipboard-check',
                'type': 'check'
            }
        ]

        # Generate 5-8 activities with random timestamps
        num_activities = random.randint(5, 8)
        for i in range(num_activities):
            template = random.choice(activity_templates)
            activities.append({
                'id': f"{goods.id}_{i+1}",
                'goods_id': goods.id,
                'goods_name': goods.name,
                'action': template['action'],
                'description': template['description'],
                'quantity_change': template['quantity_change'],
                'timestamp': timezone.now() - timezone.timedelta(
                    hours=random.randint(1, 24*7),  # Random time within last week
                    minutes=random.randint(0, 59)
                ),
                'icon': template['icon'],
                'type': template['type']
            })

        # Sort by timestamp (newest first)
        activities.sort(key=lambda x: x['timestamp'], reverse=True)

        # Calculate additional stats
        total_value = goods.quantity * goods.price if goods.quantity and goods.price else 0
        units_sold = 25  # Sample data
        total_revenue = units_sold * goods.price if goods.price else 0

        # Add calculated fields to goods object
        goods.total_value = total_value
        goods.units_sold = units_sold
        goods.total_revenue = total_revenue
        goods.is_low_stock = goods.quantity < 10 if goods.quantity else False
        goods.stock_status_display = (
            "Out of Stock" if goods.quantity == 0
            else "Low Stock" if goods.is_low_stock
            else "In Stock"
        )

        context = {
            'goods': goods,
            'activities': activities,
        }

        return render(request, 'goods_detail_enhanced.html', context)

    except Exception as e:
        traceback.print_exc()
        return render(request, 'error.html', {"error": "Server error", "details": str(e)})


# -------------------------------
# Property Sale Views
# -------------------------------

def properties_for_sale(request):
    """Public view of properties available for sale"""
    # Get all properties marked for sale
    properties_qs = Property.objects.filter(for_sale=True).select_related('owner').order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('q', '')
    if search_query:
        properties_qs = properties_qs.filter(
            models.Q(name__icontains=search_query) |
            models.Q(description__icontains=search_query) |
            models.Q(sale_description__icontains=search_query) |
            models.Q(location__icontains=search_query)
        )

    # Price filtering
    min_price = request.GET.get('min_price')
    max_price = request.GET.get('max_price')

    if min_price:
        try:
            properties_qs = properties_qs.filter(sale_price__gte=float(min_price))
        except ValueError:
            pass

    if max_price:
        try:
            properties_qs = properties_qs.filter(sale_price__lte=float(max_price))
        except ValueError:
            pass

    # Property type filtering
    property_type = request.GET.get('property_type')
    if property_type:
        properties_qs = properties_qs.filter(property_type=property_type)

    # Add pagination
    from django.core.paginator import Paginator
    paginator = Paginator(properties_qs, 12)  # Show 12 properties per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Prepare property data
    properties_data = []
    for prop in page_obj:
        properties_data.append({
            'id': prop.id,
            'name': prop.name,
            'description': prop.description,
            'sale_description': prop.sale_description,
            'sale_price': prop.sale_price,
            'is_negotiable': prop.is_negotiable,
            'property_type': prop.property_type,
            'location': prop.location,
            'images': prop.images if prop.images else [],
            'contact_phone': prop.contact_phone,
            'contact_email': prop.contact_email,
            'owner': prop.owner.username,
            'created_at': prop.created_at,
        })

    context = {
        'properties': properties_data,
        'page_obj': page_obj,
        'search_query': search_query,
        'min_price': min_price,
        'max_price': max_price,
        'property_type': property_type,
        'property_types': Property.PROPERTY_TYPE_CHOICES if hasattr(Property, 'PROPERTY_TYPE_CHOICES') else [],
    }

    return render(request, 'properties_for_sale.html', context)

def property_sale_detail(request, property_id):
    """Detailed view of a property for sale"""
    property_obj = get_object_or_404(Property, id=property_id, for_sale=True)

    context = {
        'property': property_obj,
        'images': property_obj.images if property_obj.images else [],
    }

    return render(request, 'property_sale_detail.html', context)

@login_required
def toggle_property_sale(request, property_id):
    """Toggle property for sale status"""
    if request.method == 'POST':
        try:
            property_obj = get_object_or_404(Property, id=property_id, owner=request.user)

            # Check if making for sale or removing from sale
            for_sale_value = request.POST.get('for_sale')

            if for_sale_value == 'on':
                # Making property for sale
                property_obj.for_sale = True

                sale_price = request.POST.get('sale_price')
                if not sale_price:
                    return JsonResponse({'success': False, 'error': 'Sale price is required'})

                try:
                    property_obj.sale_price = Decimal(sale_price)
                except (ValueError, InvalidOperation):
                    return JsonResponse({'success': False, 'error': 'Invalid sale price'})

                property_obj.sale_description = request.POST.get('sale_description', '')
                property_obj.contact_phone = request.POST.get('contact_phone', '')
                property_obj.contact_email = request.POST.get('contact_email', request.user.email)
                property_obj.is_negotiable = request.POST.get('is_negotiable') == 'on'

                status = 'listed for sale'

            elif for_sale_value == 'off':
                # Removing property from sale
                property_obj.for_sale = False
                property_obj.sale_price = None
                property_obj.sale_description = ''
                property_obj.contact_phone = ''
                property_obj.contact_email = ''
                property_obj.is_negotiable = False

                status = 'removed from sale'
            else:
                return JsonResponse({'success': False, 'error': 'Invalid for_sale value'})

            property_obj.save()

            return JsonResponse({
                'success': True,
                'message': f'Property "{property_obj.name}" has been {status}',
                'for_sale': property_obj.for_sale
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})