{% extends "base.html" %}

{% block title %}Add Users to Store{% endblock %}

{% block content %}
<div class="container mt-5">
    <h2>Add Authorized Users to Store #{{ store_id }}</h2>

    {% if error %}
    <div class="alert alert-danger">{{ error }}</div>
    {% endif %}

    {% if success %}
    <div class="alert alert-success">{{ success }}</div>
    {% endif %}

    <form id="addUserForm" method="post">
        {% csrf_token %}
        <div class="mb-3">
            <label for="email" class="form-label">User Email to Add</label>
            <input type="email" class="form-control" id="email" name="email" placeholder="<EMAIL>" required>
        </div>
        <button type="submit" class="btn btn-primary">Add User</button>
        <a href="{% url 'stores_list_create' %}" class="btn btn-secondary">Back to Stores</a>
    </form>
</div>

<script>
document.getElementById('addUserForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const email = document.getElementById('email').value.trim();
    if (!email) {
        alert('Please enter an email address.');
        return;
    }

    const storeId = {{ store_id }};
    const url = `/stores/${storeId}/add-users/`;

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ email: email })
        });

        const data = await response.json();

        if (response.ok) {
            alert(data.message);
            document.getElementById('email').value = '';
        } else {
            alert(data.error || 'Failed to add user');
        }
    } catch (error) {
        alert('An error occurred: ' + error.message);
    }
});
</script>
{% endblock %}
