{% extends 'base.html' %}

{% block title %}Notifications{% endblock %}

{% block content %}
<div class="container my-4">
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-bell me-2"></i>Notifications</h5>
        </div>
        <div class="card-body">
            <div id="notifications">
                {% if notifications %}
                    <ul class="list-group">
                        {% for notification in notifications %}
                            <li class="list-group-item {% if notification.read %}bg-light{% else %}bg-warning{% endif %}" data-id="{{ notification.id }}">
                                <p class="mb-1">{{ notification.message }}</p>
                                <small class="text-muted">Posted on: {{ notification.created_at|date:"M d, Y H:i" }}</small>
                            </li>
                        {% endfor %}
                    </ul>
                {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-bell-slash fa-2x mb-2 opacity-50"></i>
                        <p>No new notifications</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Mark notification as read
    document.querySelectorAll('.list-group-item').forEach(item => {
        item.addEventListener('click', function() {
            const notificationId = this.dataset.id;
            fetch('/notifications/mark-read/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    notification_id: notificationId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.message) {
                    this.classList.remove('bg-warning');
                    this.classList.add('bg-light');
                }
            })
            .catch(error => console.error('Error:', error));
        });
    });
</script>
{% endblock %}
