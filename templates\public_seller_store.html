{% extends 'base.html' %}

{% block title %}{{ seller.first_name }} {{ seller.last_name }}'s Store{% endblock %}

{% block content %}
<style>
    :root {
        --primary-color: #2563eb;
        --secondary-color: #1e40af;
        --accent-color: #3b82f6;
        --dark-color: #1f2937;
        --light-bg: #f8fafc;
        --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --card-shadow-hover: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
        line-height: 1.6;
        background-color: var(--light-bg);
    }

    /* Container constraints to prevent overflow */
    .store-container {
        max-width: 1500px;
        margin: 0 auto;
        padding: 0 15px;
    }

    /* Hero Banner - Compact and Centered */
    .hero-banner {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        padding: 40px 0;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
    }

    .hero-banner::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 300"><polygon fill="rgba(255,255,255,0.08)" points="0,0 1000,150 1000,300 0,100"/></svg>');
        background-size: cover;
    }

    .hero-content {
        position: relative;
        z-index: 2;
        text-align: center;
        color: white;
    }

    .hero-title {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .hero-subtitle {
        font-size: 1rem;
        opacity: 0.9;
        margin-bottom: 1.5rem;
    }

    .hero-actions {
        display: flex;
        gap: 1rem;
        justify-content: center;
        flex-wrap: wrap;
    }

    .btn-hero {
        background: rgba(255,255,255,0.15);
        border: 2px solid rgba(255,255,255,0.25);
        color: white;
        padding: 8px 20px;
        border-radius: 25px;
        font-weight: 500;
        font-size: 0.9rem;
        backdrop-filter: blur(10px);
        transition: all 0.3s ease;
        text-decoration: none;
    }

    .btn-hero:hover {
        background: rgba(255,255,255,0.25);
        border-color: rgba(255,255,255,0.4);
        color: white;
        transform: translateY(-1px);
    }

    /* Filter Card */
    .filter-card {
        background: white;
        border-radius: 12px;
        box-shadow: var(--card-shadow);
        border: none;
        margin-bottom: 2rem;
    }

    .filter-card .card-body {
        padding: 1.5rem;
    }

    /* Product Grid */
    .products-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
        margin-bottom: 2rem;
    }

    .product-card {
        background: white;
        border-radius: 12px;
        box-shadow: var(--card-shadow);
        transition: all 0.3s ease;
        border: none;
        overflow: hidden;
        height: fit-content;
    }

    .product-card:hover {
        transform: translateY(-2px);
        box-shadow: var(--card-shadow-hover);
    }

    .product-image {
        height: 200px;
        object-fit: cover;
        width: 100%;
    }

    .product-placeholder {
        height: 200px;
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        color: #9ca3af;
    }

    .price-tag {
        font-size: 1.25rem;
        font-weight: 700;
        color: var(--accent-color);
    }

    .store-badge {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        color: white;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 500;
    }

    .condition-badge {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 2;
        font-size: 0.75rem;
    }

    .btn-view-details {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        color: white;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .btn-view-details:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
        color: white;
    }

    .btn-contact {
        border-radius: 8px;
        padding: 6px 12px;
        font-weight: 500;
        transition: all 0.3s ease;
        font-size: 0.85rem;
    }

    .btn-contact:hover {
        transform: translateY(-1px);
    }

    .filter-btn {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        border: none;
        border-radius: 8px;
        padding: 8px 20px;
        font-weight: 500;
        color: white;
    }

    /* No Results State */
    .no-results {
        text-align: center;
        padding: 3rem 2rem;
        background: white;
        border-radius: 12px;
        box-shadow: var(--card-shadow);
    }

    .no-results i {
        font-size: 3rem;
        color: #d1d5db;
        margin-bottom: 1rem;
    }

    /* Modal Enhancements */
    .modal-content {
        border-radius: 12px;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        border: none;
    }

    .modal-header {
        background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
        color: white;
        border-radius: 12px 12px 0 0;
        border-bottom: none;
        padding: 1rem 1.5rem;
    }

    .modal-header .btn-close {
        filter: brightness(0) invert(1);
    }

    /* Form Styles */
    .form-control:focus {
        border-color: var(--accent-color);
        box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
    }

    .form-control {
        border-radius: 8px;
        border: 2px solid #e5e7eb;
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .form-label {
        font-weight: 500;
        color: var(--dark-color);
        margin-bottom: 4px;
        font-size: 0.9rem;
    }

    .card-footer {
        background: transparent;
        border-top: 1px solid rgba(0,0,0,0.05);
        padding: 1rem;
    }

    /* Pagination */
    .pagination {
        margin-top: 2rem;
    }

    .page-link {
        border-radius: 8px;
        margin: 0 2px;
        border: 1px solid #e5e7eb;
        color: var(--primary-color);
    }

    .page-item.active .page-link {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .store-container {
            padding: 0 10px;
        }

        .hero-title {
            font-size: 1.5rem;
        }

        .hero-subtitle {
            font-size: 0.9rem;
        }

        .products-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
        }

        .filter-card .card-body {
            padding: 1rem;
        }

        .hero-actions {
            flex-direction: column;
            align-items: center;
        }

        .btn-hero {
            width: 200px;
        }
    }
</style>

<div class="store-container">
    <!-- Hero Banner -->
    <section class="hero-banner">
        <div class="hero-content">
            <h1 class="hero-title">{{ seller.first_name }} {{ seller.last_name }}'s Store</h1>
            <p class="hero-subtitle">@{{ seller.username }} • {{ total_goods }} Product{{ total_goods|pluralize }} • {{ total_stores }} Store{{ total_stores|pluralize }}</p>
            <div class="hero-actions">
                <button class="btn btn-hero" onclick="shareStore()">
                    <i class="fas fa-share-alt me-1"></i>Share Store
                </button>
                <button class="btn btn-hero" onclick="contactSeller()">
                    <i class="fas fa-envelope me-1"></i>Contact Seller
                </button>
            </div>
        </div>
    </section>

    <!-- Filters Section -->
    <div class="filter-card">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Search & Filter</h5>
                <button class="btn btn-outline-primary btn-sm" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
            
            <div class="collapse show" id="filterCollapse">
                <form method="GET" id="filterForm">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="nameFilter" class="form-label">Search Products</label>
                            <input type="text" class="form-control" id="nameFilter" name="q"
                                   value="{{ filters.search_query }}" placeholder="Search products...">
                        </div>
                        <div class="col-md-2">
                            <label for="minPriceFilter" class="form-label">Min Price</label>
                            <input type="number" class="form-control" id="minPriceFilter" name="min_price"
                                   value="{{ filters.min_price }}" step="0.01" min="0" placeholder="0.00">
                        </div>
                        <div class="col-md-2">
                            <label for="maxPriceFilter" class="form-label">Max Price</label>
                            <input type="number" class="form-control" id="maxPriceFilter" name="max_price"
                                   value="{{ filters.max_price }}" step="0.01" min="0" placeholder="999.99">
                        </div>
                        <div class="col-md-2">
                            <label for="categoryFilter" class="form-label">Category</label>
                            <select class="form-control" id="categoryFilter" name="category">
                                <option value="">All Categories</option>
                                {% for category in categories %}
                                    <option value="{{ category.id }}" {% if filters.category == category.id|stringformat:"s" %}selected{% endif %}>
                                        {{ category.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% if stores.count > 1 %}
                        <div class="col-md-2">
                            <label for="storeFilter" class="form-label">Store</label>
                            <select class="form-control" id="storeFilter" name="store">
                                <option value="">All Stores</option>
                                {% for store in stores %}
                                    <option value="{{ store.id }}" {% if filters.store == store.id|stringformat:"s" %}selected{% endif %}>
                                        {{ store.name }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                        {% endif %}
                    </div>
                    <div class="row g-3 mt-1">
                        <div class="col-md-2">
                            <label for="conditionFilter" class="form-label">Condition</label>
                            <select class="form-control" id="conditionFilter" name="condition">
                                <option value="">All Conditions</option>
                                <option value="new" {% if filters.condition == "new" %}selected{% endif %}>New</option>
                                <option value="used" {% if filters.condition == "used" %}selected{% endif %}>Used</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="deliveryFilter" class="form-label">Delivery</label>
                            <select class="form-control" id="deliveryFilter" name="available_for_delivery">
                                <option value="">All Items</option>
                                <option value="true" {% if filters.available_for_delivery %}selected{% endif %}>Available for Delivery</option>
                                <option value="false" {% if filters.available_for_delivery == false %}selected{% endif %}>Pickup Only</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="bulkFilter" class="form-label">Bulk Sales</label>
                            <select class="form-control" id="bulkFilter" name="available_for_bulk_sales">
                                <option value="">All Items</option>
                                <option value="true" {% if filters.available_for_bulk_sales %}selected{% endif %}>Bulk Available</option>
                                <option value="false" {% if filters.available_for_bulk_sales == false %}selected{% endif %}>No Bulk</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="stockFilter" class="form-label">Stock</label>
                            <select class="form-control" id="stockFilter" name="in_stock_only">
                                <option value="">All Items</option>
                                <option value="true" {% if filters.in_stock_only %}selected{% endif %}>In Stock Only</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="sortFilter" class="form-label">Sort By</label>
                            <select class="form-control" id="sortFilter" name="sort">
                                <option value="">Default</option>
                                <option value="price_low" {% if filters.sort == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                                <option value="price_high" {% if filters.sort == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                                <option value="name" {% if filters.sort == 'name' %}selected{% endif %}>Name A-Z</option>
                                <option value="newest" {% if filters.sort == 'newest' %}selected{% endif %}>Newest First</option>
                            </select>
                        </div>
                        <div class="col-md-2 d-flex align-items-end">
                            <div class="btn-group w-100">
                                <button type="submit" class="btn filter-btn">
                                    <i class="fas fa-search me-1"></i>Filter
                                </button>
                                <a href="{% url 'private_seller_store_public' seller.id %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

                <!-- Properties for Sale Section -->
                {% if properties_for_sale %}
                <div class="mb-5">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h4 class="mb-0">
                            <i class="fas fa-home me-2 text-success"></i>
                            Properties for Sale
                        </h4>
                        <a href="{% url 'properties_for_sale' %}" class="btn btn-outline-success btn-sm">
                            View All Properties
                        </a>
                    </div>

                    <div class="row g-3">
                        {% for property in properties_for_sale %}
                        <div class="col-lg-4 col-md-6">
                            <div class="card h-100 border-success">
                                <div class="position-relative">
                                    {% if property.images and property.images.0 %}
                                        <img src="/media/{{ property.images.0 }}" class="card-img-top" style="height: 150px; object-fit: cover;" alt="{{ property.name }}" onerror="this.src='/static/images/no-image.png'; this.onerror=null;">
                                    {% else %}
                                        <div class="card-img-top d-flex align-items-center justify-content-center bg-light" style="height: 150px;">
                                            <i class="fas fa-home fa-3x text-muted"></i>
                                        </div>
                                    {% endif %}

                                    <div class="position-absolute top-0 end-0 m-2">
                                        <span class="badge bg-success">
                                            ${{ property.sale_price|floatformat:0 }}
                                        </span>
                                    </div>

                                    {% if property.is_negotiable %}
                                        <div class="position-absolute top-0 start-0 m-2">
                                            <span class="badge bg-warning text-dark">Negotiable</span>
                                        </div>
                                    {% endif %}
                                </div>

                                <div class="card-body">
                                    <h6 class="card-title">{{ property.name }}</h6>
                                    <p class="card-text text-muted small">
                                        {{ property.sale_description|default:property.description|truncatewords:10 }}
                                    </p>

                                    {% if property.location %}
                                    <p class="text-muted small mb-2">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ property.location }}
                                    </p>
                                    {% endif %}

                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">{{ property.created_at|date:"M d, Y" }}</small>
                                        <a href="{% url 'property_sale_detail' property.id %}" class="btn btn-success btn-sm">
                                            <i class="fas fa-eye me-1"></i>View
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                {% endif %}

    <!-- Products Grid -->
    {% if goods %}
        <div class="products-grid">
            {% for item in goods %}
            <div class="product-card">
                <div class="position-relative">
                    {% if item.first_image_url %}
                        <img src="{{ item.first_image_url }}" 
                             class="product-image" 
                             alt="{{ item.name }}"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="product-placeholder" style="display: none;">
                            <i class="fas fa-image fa-2x"></i>
                            <p class="mt-2 mb-0">No image available</p>
                        </div>
                    {% else %}
                        <div class="product-placeholder">
                            <i class="fas fa-image fa-2x"></i>
                            <p class="mt-2 mb-0">No image available</p>
                        </div>
                    {% endif %}
                    
                    {% if item.is_used %}
                        <span class="badge bg-warning condition-badge">Used</span>
                    {% else %}
                        <span class="badge bg-success condition-badge">New</span>
                    {% endif %}
                </div>
                
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-start mb-2">
                        <h6 class="card-title mb-0 fw-bold">{{ item.name }}</h6>
                        <span class="store-badge">
                            <i class="fas fa-store"></i>
                        </span>
                    </div>
                    
                    <p class="card-text text-muted small mb-2">
                        {{ item.description|default:"No description available"|truncatewords:12 }}
                    </p>
                    
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="price-tag">{{ item.currency_symbol }}{{ item.price|floatformat:2 }}</span>
                        <small class="text-muted">
                            <i class="fas fa-calendar"></i> {{ item.created_at|date:"M d" }}
                        </small>
                    </div>

                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-store me-1"></i>{{ item.store.name }}
                        </small>
                        {% if item.category.name %}
                        <br><small class="text-muted">
                            <i class="fas fa-tag me-1"></i>{{ item.category.name }}
                        </small>
                        {% endif %}
                    </div>

                    {% if item.available_for_delivery %}
                    <div class="mb-2">
                        <small class="text-success">
                            <i class="fas fa-truck me-1"></i>Delivery Available
                        </small>
                    </div>
                    {% endif %}
                </div>
                
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <button class="btn btn-view-details btn-sm" onclick="inquireAboutProduct('{{ item.id }}', '{{ item.name|escapejs }}', '{{ seller.username|escapejs }}')">
                            <i class="fas fa-envelope me-1"></i>Inquire Now
                        </button>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-info btn-contact flex-fill btn-sm" onclick="viewProductDetails('{{ item.id }}')">
                                <i class="fas fa-eye me-1"></i>Details
                            </button>
                            <button class="btn btn-outline-secondary btn-contact flex-fill btn-sm" onclick="shareProduct('{{ item.id }}', '{{ item.name|escapejs }}')">
                                <i class="fas fa-share-alt me-1"></i>Share
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Pagination -->
        {% if goods.has_other_pages %}
        <nav aria-label="Products pagination">
            <ul class="pagination justify-content-center">
                {% if goods.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ goods.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Previous</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">{{ goods.number }} of {{ goods.paginator.num_pages }}</span>
                </li>

                {% if goods.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ goods.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ goods.paginator.num_pages }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.category %}&category={{ request.GET.category }}{% endif %}{% if request.GET.min_price %}&min_price={{ request.GET.min_price }}{% endif %}{% if request.GET.max_price %}&max_price={{ request.GET.max_price }}{% endif %}">Last</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="no-results">
            <i class="fas fa-search-minus"></i>
            <h4 class="text-muted mb-3">No products found</h4>
            <p class="text-muted mb-4">
                {% if filters.search_query or filters.category or filters.min_price or filters.max_price %}
                    Try adjusting your filters to see more products.
                {% else %}
                    This seller hasn't added any products yet.
                {% endif %}
            </p>
            {% if filters.search_query or filters.category or filters.min_price or filters.max_price %}
            <a href="{% url 'private_seller_store_public' seller.id %}" class="btn btn-view-details">
                <i class="fas fa-times me-2"></i>Clear Filters
            </a>
            {% endif %}
        </div>
    {% endif %}
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form enhancements
    const form = document.getElementById('filterForm');
    if (form) {
        // Auto-submit on filter changes for select elements
        const autoSubmitElements = form.querySelectorAll('select');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', () => {
                setTimeout(() => form.submit(), 300);
            });
        });

        // Submit on Enter for text inputs
        const textInputs = form.querySelectorAll('input[type="text"], input[type="number"]');
        textInputs.forEach(input => {
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    form.submit();
                }
            });
        });

        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Filtering...';
            submitBtn.disabled = true;

            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    }
});

function shareStore() {
    const storeURL = window.location.href;
    const storeName = `{{ seller.first_name }} {{ seller.last_name }}'s Store`;
    
    if (navigator.share) {
        navigator.share({
            title: storeName,
            text: 'Check out this amazing store!',
            url: storeURL
        }).catch(console.error);
    } else {
        navigator.clipboard.writeText(storeURL).then(() => {
            showAlert('success', 'Store link copied to clipboard!');
        }).catch(() => {
            showAlert('info', `Store link: ${storeURL}`);
        });
    }
}

function contactSeller() {
    const sellerEmail = '{{ seller.email|default:"" }}';
    const sellerName = '{{ seller.first_name }} {{ seller.last_name }}';

    if (sellerEmail) {
        const subject = encodeURIComponent(`Inquiry about your store`);
        const body = encodeURIComponent(`Hi ${sellerName},

I'm interested in your products and would like to know more about your store.

Best regards`);

        const emailUrl = `mailto:${sellerEmail}?subject=${subject}&body=${body}`;
        window.open(emailUrl);
    } else {
        showAlert('info', 'Contact information not available for this seller.');
    }
}

function inquireAboutProduct(productId, productName, sellerUsername) {
    const modalHTML = `
        <div class="modal fade" id="inquiryModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-envelope me-2"></i>Inquire About: ${productName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        <form id="inquiryForm">
                            <div class="mb-3">
                                <label for="inquirerName" class="form-label">Your Name</label>
                                <input type="text" class="form-control" id="inquirerName" required>
                            </div>
                            <div class="mb-3">
                                <label for="inquirerEmail" class="form-label">Your Email</label>
                                <input type="email" class="form-control" id="inquirerEmail" required>
                            </div>
                            <div class="mb-3">
                                <label for="inquirerPhone" class="form-label">Your Phone (Optional)</label>
                                <input type="tel" class="form-control" id="inquirerPhone">
                            </div>
                            <div class="mb-3">
                                <label for="inquiryMessage" class="form-label">Message</label>
                                <textarea class="form-control" id="inquiryMessage" rows="4"
                                          placeholder="I'm interested in this product. Please provide more details...">${'I am interested in "' + productName + '". Could you please provide more details about availability, pricing, and delivery options?'}</textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Cancel
                        </button>
                        <button type="button" class="btn btn-view-details" onclick="submitInquiry('${productId}', '${productName}', '${sellerUsername}')">
                            <i class="fas fa-paper-plane me-2"></i>Send Inquiry
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    const existingModal = document.getElementById('inquiryModal');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('inquiryModal'));
    modal.show();
}

function submitInquiry(productId, productName, sellerUsername) {
    const form = document.getElementById('inquiryForm');

    const inquiryData = {
        product_id: productId,
        product_name: productName,
        seller_username: sellerUsername,
        inquirer_name: document.getElementById('inquirerName').value,
        inquirer_email: document.getElementById('inquirerEmail').value,
        inquirer_phone: document.getElementById('inquirerPhone').value,
        message: document.getElementById('inquiryMessage').value
    };

    // Validate required fields
    if (!inquiryData.inquirer_name || !inquiryData.inquirer_email || !inquiryData.message) {
        showAlert('danger', 'Please fill in all required fields.');
        return;
    }

    // Show loading state
    const submitBtn = document.querySelector('#inquiryModal .btn-view-details');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';
    submitBtn.disabled = true;

    // Submit inquiry (you can implement actual backend submission here)
    setTimeout(() => {
        showAlert('success', `Your inquiry about "${productName}" has been sent to ${sellerUsername}!`);

        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('inquiryModal'));
        modal.hide();

        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        console.log('Inquiry submitted:', inquiryData);
    }, 1500);
}

function shareProduct(productId, productName) {
    const productURL = `${window.location.origin}/public/goods/detail/${productId}/`;
    showShareModal(productURL, productName, 'product');
}

function showShareModal(url, name, type) {
    const modalHTML = `
        <div class="modal fade" id="shareModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-share-alt me-2"></i>Share ${type === 'product' ? 'Product' : 'Store'}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body p-4">
                        <div class="mb-3">
                            <h6>${name}</h6>
                            <p class="text-muted small">${url}</p>
                        </div>

                        <div class="row g-3">
                            <div class="col-12">
                                <label class="form-label">Share URL</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="shareUrl" value="${url}" readonly>
                                    <button class="btn btn-outline-secondary" onclick="copyToClipboard('shareUrl')">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                </div>
                            </div>

                            <div class="col-12">
                                <label class="form-label">Share via Email</label>
                                <button class="btn btn-primary w-100" onclick="shareViaEmail('${url}', '${name}', '${type}')">
                                    <i class="fas fa-envelope me-1"></i>Open Email Client
                                </button>
                            </div>

                            <div class="col-12">
                                <label class="form-label">Social Media</label>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button class="btn btn-outline-primary btn-sm" onclick="shareOnFacebook('${url}', '${name}')">
                                        <i class="fab fa-facebook-f"></i> Facebook
                                    </button>
                                    <button class="btn btn-outline-info btn-sm" onclick="shareOnTwitter('${url}', '${name}')">
                                        <i class="fab fa-twitter"></i> Twitter
                                    </button>
                                    <button class="btn btn-outline-success btn-sm" onclick="shareOnWhatsApp('${url}', '${name}')">
                                        <i class="fab fa-whatsapp"></i> WhatsApp
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    const existingModal = document.getElementById('shareModal');
    if (existingModal) {
        existingModal.remove();
    }

    document.body.insertAdjacentHTML('beforeend', modalHTML);
    const modal = new bootstrap.Modal(document.getElementById('shareModal'));
    modal.show();
}

function copyToClipboard(elementId) {
    const element = document.getElementById(elementId);
    element.select();
    element.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        showAlert('success', 'URL copied to clipboard!');
    } catch (err) {
        navigator.clipboard.writeText(element.value).then(() => {
            showAlert('success', 'URL copied to clipboard!');
        }).catch(() => {
            showAlert('danger', 'Failed to copy URL. Please copy manually.');
        });
    }
}

function shareViaEmail(url, name, type) {
    const subject = encodeURIComponent(`Check out this ${type}: ${name}`);
    const body = encodeURIComponent(`Hi,

I wanted to share this ${type} with you: ${name}

You can view it here: ${url}

Best regards`);

    const emailUrl = `mailto:?subject=${subject}&body=${body}`;
    window.open(emailUrl);
}

function shareOnFacebook(url, name) {
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(name)}`;
    window.open(facebookUrl, '_blank', 'width=600,height=400');
}

function shareOnTwitter(url, name) {
    const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(`Check out: ${name}`)}`;
    window.open(twitterUrl, '_blank', 'width=600,height=400');
}

function shareOnWhatsApp(url, name) {
    const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(`Check out: ${name} - ${url}`)}`;
    window.open(whatsappUrl, '_blank');
}

function viewProductDetails(productId) {
    window.location.href = `/public/goods/detail/${productId}/`;
}

function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}ck %}