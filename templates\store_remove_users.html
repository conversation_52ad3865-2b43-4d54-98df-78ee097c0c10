{% extends "base.html" %}

{% block title %}Remove Users from Store{% endblock %}

{% block content %}
<div class="container mt-5">
    <h2>Remove Authorized Users from Store #{{ store_id }}</h2>

    {% if error %}
    <div class="alert alert-danger">{{ error }}</div>
    {% endif %}
    {% if success %}
    <div class="alert alert-success">
        {{ success }}
        {% if removed_users %}
            <p><strong>Removed Users:</strong> {{ removed_users|join:", " }}</p>
        {% endif %}
        {% if not_found_emails %}
            <p><strong>Not Found or Not Authorized:</strong> {{ not_found_emails|join:", " }}</p>
        {% endif %}
    </div>
    {% endif %}

    <form id="removeUsersForm" method="post">
        {% csrf_token %}
        <div class="mb-3">
            <label for="emails" class="form-label">Emails to Remove (comma separated)</label>
            <textarea class="form-control" id="emails" name="emails" rows="4" placeholder="<EMAIL>, <EMAIL>" required></textarea>
        </div>
        <button type="submit" class="btn btn-danger">Remove Users</button>
        <a href="{% url 'stores_list_create' %}" class="btn btn-secondary">Back to Stores</a>
    </form>
</div>

<script>
document.getElementById('removeUsersForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const emailsText = document.getElementById('emails').value.trim();
    if (!emailsText) {
        alert('Please enter one or more email addresses.');
        return;
    }

    // Split emails by comma
    const emails = emailsText.split(',').map(e => e.trim()).filter(e => e.length > 0);

    const storeId = {{ store_id }};
    const url = `/stores/${storeId}/remove-users/`;

    try {
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({ emails: emails })
        });

        const data = await response.json();

        if (response.ok) {
            let msg = data.message + '\nRemoved users: ' + data.removed_users.join(', ');
            if (data.not_found_or_not_authorized.length > 0) {
                msg += '\nNot found or not authorized: ' + data.not_found_or_not_authorized.join(', ');
            }
            alert(msg);
            document.getElementById('emails').value = '';
        } else {
            alert(data.error || 'Failed to remove users');
        }
    } catch (error) {
        alert('An error occurred: ' + error.message);
    }
});
</script>
{% endblock %}
