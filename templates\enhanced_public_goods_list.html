{% extends 'base.html' %}
{% load static %}

{% block title %}Browse Products - Enhanced Marketplace{% endblock %}

{% block extra_css %}
<style>
    .marketplace-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 3rem 0;
        margin-bottom: 2rem;
    }
    
    .filter-sidebar {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        position: sticky;
        top: 20px;
        height: fit-content;
    }
    
    .filter-section {
        margin-bottom: 1.5rem;
        padding-bottom: 1.5rem;
        border-bottom: 1px solid #dee2e6;
    }
    
    .filter-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }
    
    .filter-title {
        font-weight: 600;
        color: #333;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .goods-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;
    }
    
    .goods-card {
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        position: relative;
    }
    
    .goods-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0,0,0,0.15);
    }
    
    .goods-image-container {
        height: 200px;
        overflow: hidden;
        position: relative;
    }
    
    .goods-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .goods-card:hover .goods-image {
        transform: scale(1.05);
    }
    
    .goods-placeholder {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6c757d;
        font-size: 3rem;
    }
    
    .stock-badge {
        position: absolute;
        top: 10px;
        right: 10px;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        color: white;
    }
    
    .stock-badge.in-stock { background: #28a745; }
    .stock-badge.out-of-stock { background: #dc3545; }
    
    .condition-badge {
        position: absolute;
        top: 10px;
        left: 10px;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .condition-badge.new {
        background: #007bff;
        color: white;
    }
    
    .condition-badge.used {
        background: #ffc107;
        color: #212529;
    }
    
    .goods-body {
        padding: 1.5rem;
    }
    
    .goods-title {
        font-size: 1.1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #333;
        line-height: 1.3;
    }
    
    .goods-location {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 0.5rem;
    }
    
    .star-rating {
        color: #ffc107;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }
    
    .goods-price {
        font-size: 1.5rem;
        font-weight: bold;
        color: #28a745;
        margin-bottom: 1rem;
    }
    
    .goods-description {
        font-size: 0.9rem;
        color: #6c757d;
        margin-bottom: 1rem;
        line-height: 1.4;
    }
    
    .store-info {
        background: #f8f9fa;
        border-radius: 8px;
        padding: 0.75rem;
        margin-bottom: 1rem;
    }
    
    .store-name {
        font-weight: 600;
        color: #333;
        font-size: 0.9rem;
    }
    
    .goods-actions {
        display: grid;
        grid-template-columns: 1fr auto;
        gap: 0.5rem;
        align-items: center;
    }
    
    .btn-contact {
        background: #667eea;
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-contact:hover {
        background: #5a6fd8;
        color: white;
    }
    
    .btn-share {
        background: transparent;
        border: 2px solid #dee2e6;
        color: #6c757d;
        padding: 0.5rem;
        border-radius: 8px;
        transition: all 0.3s ease;
    }
    
    .btn-share:hover {
        border-color: #667eea;
        color: #667eea;
    }
    
    .results-header {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }
    
    .price-range-inputs {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 0.5rem;
        align-items: center;
    }
    
    .filter-checkbox {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 0.5rem;
    }
    
    @media (max-width: 768px) {
        .marketplace-header {
            padding: 2rem 0;
        }
        
        .goods-grid {
            grid-template-columns: 1fr;
        }
        
        .filter-sidebar {
            position: static;
            margin-bottom: 2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Marketplace Header -->
<div class="marketplace-header">
    <div class="container">
        <div class="text-center">
            <h1 class="mb-3">
                <i class="fas fa-shopping-bag me-2"></i>
                Enhanced Marketplace
            </h1>
            <p class="mb-0 opacity-75 lead">
                Discover amazing products with advanced filtering
            </p>
        </div>
    </div>
</div>

<div class="container">
    <div class="row">
        <!-- Filter Sidebar -->
        <div class="col-lg-3">
            <div class="filter-sidebar">
                <form method="GET" id="filterForm">
                    <!-- Search -->
                    <div class="filter-section">
                        <div class="filter-title">🔍 Search</div>
                        <input type="text" class="form-control" name="q" 
                               value="{{ filters.search_query }}" 
                               placeholder="Search products, stores...">
                    </div>
                    
                    <!-- Location -->
                    <div class="filter-section">
                        <div class="filter-title">🌍 Location</div>
                        <select class="form-select mb-2" name="country" id="countrySelect">
                            <option value="">All Countries</option>
                            {% for country in countries %}
                                <option value="{{ country.id }}" 
                                        {% if filters.country_id == country.id|stringformat:"s" %}selected{% endif %}>
                                    {{ country.name }}
                                </option>
                            {% endfor %}
                        </select>
                        
                        <select class="form-select" name="state" id="stateSelect">
                            <option value="">All States</option>
                            {% for state in states %}
                                <option value="{{ state.id }}" 
                                        {% if filters.state_id == state.id|stringformat:"s" %}selected{% endif %}>
                                    {{ state.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Star Rating -->
                    <div class="filter-section">
                        <div class="filter-title">⭐ Rating</div>
                        <select class="form-select mb-2" name="star_rating">
                            <option value="">Exact Rating</option>
                            {% for rating_value, rating_display in star_ratings %}
                                <option value="{{ rating_value }}" 
                                        {% if filters.star_rating == rating_value|stringformat:"s" %}selected{% endif %}>
                                    {{ rating_display }}
                                </option>
                            {% endfor %}
                        </select>
                        
                        <select class="form-select" name="min_rating">
                            <option value="">Minimum Rating</option>
                            {% for rating_value, rating_display in min_ratings %}
                                <option value="{{ rating_value }}" 
                                        {% if filters.min_rating == rating_value|stringformat:"s" %}selected{% endif %}>
                                    {{ rating_display }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Price Range -->
                    <div class="filter-section">
                        <div class="filter-title">💰 Price Range</div>
                        <div class="price-range-inputs">
                            <input type="number" class="form-control" name="min_price" 
                                   value="{{ filters.min_price }}" placeholder="Min" step="0.01">
                            <span class="text-muted">to</span>
                            <input type="number" class="form-control" name="max_price" 
                                   value="{{ filters.max_price }}" placeholder="Max" step="0.01">
                        </div>
                    </div>
                    
                    <!-- Category -->
                    <div class="filter-section">
                        <div class="filter-title">📂 Category</div>
                        <select class="form-select" name="category">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                                <option value="{{ category.id }}" 
                                        {% if filters.category_id == category.id|stringformat:"s" %}selected{% endif %}>
                                    {{ category.name }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <!-- Condition -->
                    <div class="filter-section">
                        <div class="filter-title">🏷️ Condition</div>
                        <select class="form-select" name="condition">
                            <option value="">All Conditions</option>
                            <option value="new" {% if filters.condition == 'new' %}selected{% endif %}>🆕 New</option>
                            <option value="used" {% if filters.condition == 'used' %}selected{% endif %}>♻️ Used</option>
                        </select>
                    </div>
                    
                    <!-- Additional Filters -->
                    <div class="filter-section">
                        <div class="filter-title">🚚 Additional</div>
                        <div class="filter-checkbox">
                            <input type="checkbox" class="form-check-input" name="available_for_delivery" 
                                   {% if filters.available_for_delivery %}checked{% endif %}>
                            <label class="form-check-label">Delivery Available</label>
                        </div>
                        <div class="filter-checkbox">
                            <input type="checkbox" class="form-check-input" name="in_stock_only" 
                                   {% if filters.in_stock_only %}checked{% endif %}>
                            <label class="form-check-label">In Stock Only</label>
                        </div>
                    </div>
                    
                    <!-- Filter Actions -->
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Apply Filters
                        </button>
                        <a href="/public/goods/" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Clear All
                        </a>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- Results -->
        <div class="col-lg-9">
            <!-- Results Header -->
            <div class="results-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">Search Results</h5>
                        <p class="text-muted mb-0">Found {{ total_count }} product{{ total_count|pluralize }}</p>
                    </div>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>Sort by: Newest</option>
                            <option>Price: Low to High</option>
                            <option>Price: High to Low</option>
                            <option>Rating: High to Low</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <!-- Goods Grid -->
            <div class="goods-grid">
                {% for good in goods %}
                <div class="goods-card">
                    <div class="goods-image-container">
                        {% if good.image_url %}
                            <img src="/media/{{ good.image_url }}" 
                                 class="goods-image" 
                                 alt="{{ good.name }}">
                        {% else %}
                            <div class="goods-placeholder">
                                <i class="fas fa-image"></i>
                            </div>
                        {% endif %}
                        
                        <div class="condition-badge {{ good.condition_display|lower }}">
                            {{ good.condition_display }}
                        </div>
                        
                        {% if good.track_inventory %}
                            <div class="stock-badge {% if good.in_stock %}in-stock{% else %}out-of-stock{% endif %}">
                                {{ good.stock_status }}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="goods-body">
                        <div class="goods-title">{{ good.name }}</div>
                        
                        {% if good.country or good.state %}
                            <div class="goods-location">
                                <i class="fas fa-map-marker-alt me-1"></i>
                                {% if good.state %}{{ good.state }}, {% endif %}{{ good.country }}
                            </div>
                        {% endif %}
                        
                        {% if good.star_rating %}
                            <div class="star-rating">
                                {{ good.star_rating_stars }} ({{ good.star_rating }}/5)
                            </div>
                        {% endif %}
                        
                        <div class="goods-price">${{ good.price }}</div>
                        
                        <div class="goods-description">
                            {{ good.description|truncatechars:100 }}
                        </div>
                        
                        {% if good.contact_store.store_name %}
                            <div class="store-info">
                                <div class="store-name">
                                    <i class="fas fa-store me-1"></i>
                                    {{ good.contact_store.store_name }}
                                </div>
                            </div>
                        {% endif %}
                        
                        <div class="goods-actions">
                            <button class="btn btn-contact" 
                                    onclick="contactSeller({{ good.id }}, '{{ good.name|escapejs }}')">
                                <i class="fas fa-envelope me-1"></i>Contact Seller
                            </button>
                            <button class="btn btn-share" 
                                    onclick="shareProduct('{{ good.share_link }}', '{{ good.name|escapejs }}')">
                                <i class="fas fa-share-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12 text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h5>No Products Found</h5>
                    <p class="text-muted">Try adjusting your filters or search terms.</p>
                    <a href="/public/goods/" class="btn btn-primary">
                        <i class="fas fa-refresh me-2"></i>Reset Filters
                    </a>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class EnhancedMarketplace {
    constructor() {
        this.initializeEventListeners();
        this.loadStatesForCountry();
    }

    initializeEventListeners() {
        // Country change handler
        document.getElementById('countrySelect').addEventListener('change', () => {
            this.loadStatesForCountry();
        });

        // Form submission
        document.getElementById('filterForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.applyFilters();
        });

        // Auto-submit on filter changes
        const autoSubmitElements = document.querySelectorAll('select[name], input[type="checkbox"]');
        autoSubmitElements.forEach(element => {
            element.addEventListener('change', () => {
                setTimeout(() => this.applyFilters(), 300);
            });
        });
    }

    async loadStatesForCountry() {
        const countrySelect = document.getElementById('countrySelect');
        const stateSelect = document.getElementById('stateSelect');
        const countryId = countrySelect.value;

        // Clear states
        stateSelect.innerHTML = '<option value="">All States</option>';

        if (!countryId) return;

        try {
            const response = await fetch(`/get-states/${countryId}/`);
            const data = await response.json();

            data.states.forEach(state => {
                const option = document.createElement('option');
                option.value = state.id;
                option.textContent = state.name;
                stateSelect.appendChild(option);
            });
        } catch (error) {
            console.error('Error loading states:', error);
        }
    }

    applyFilters() {
        document.getElementById('filterForm').submit();
    }

    showAlert(type, message) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        const container = document.querySelector('.container');
        container.insertAdjacentHTML('afterbegin', alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alert = container.querySelector('.alert');
            if (alert) {
                alert.remove();
            }
        }, 5000);
    }
}

// Global functions
function contactSeller(goodsId, goodsName) {
    // You can implement a contact modal or redirect to contact page
    marketplace.showAlert('info', `Contact functionality for "${goodsName}" will be implemented soon!`);
}

function shareProduct(shareLink, goodsName) {
    if (navigator.share) {
        navigator.share({
            title: goodsName,
            text: `Check out this product: ${goodsName}`,
            url: shareLink
        }).catch(console.error);
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(shareLink).then(() => {
            marketplace.showAlert('success', 'Product link copied to clipboard!');
        }).catch(() => {
            marketplace.showAlert('info', `Share this product: ${shareLink}`);
        });
    }
}

// Initialize marketplace
let marketplace;
document.addEventListener('DOMContentLoaded', () => {
    marketplace = new EnhancedMarketplace();
});
</script>
{% endblock %}
