{% extends 'base.html' %}

{% block title %}Inventories{% endblock %}

{% block content %}
<div class="container mt-4">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'stores_list_create' %}">Stores</a></li>
            <li class="breadcrumb-item active">Inventories</li>
        </ol>
    </nav>

    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>My Inventories</h2>
        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInventoryModal">
            <i class="fas fa-plus"></i> Create New Inventory
        </button>
    </div>

    {% if inventories %}
        <div class="row">
            {% for inventory in inventories %}
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-body">
                            <h5 class="card-title">{{ inventory.name }}</h5>
                            <p class="card-text">
                                <strong>Store:</strong> {{ inventory.store.name|default:"No store" }}<br>
                                <strong>Quantity:</strong> {{ inventory.quantity }}
                            </p>
                            <small class="text-muted">Created: {{ inventory.created_at|date:"M d, Y" }}</small>
                        </div>
                        <div class="card-footer">
                            <div class="btn-group w-100" role="group">
                                <a href="{% url 'inventory_detail' inventory.id %}" class="btn btn-outline-primary btn-sm">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="{% url 'goods_list_create' %}?inventory_id={{ inventory.id }}" class="btn btn-outline-success btn-sm">
                                    <i class="fas fa-box"></i> Goods
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="fas fa-boxes fa-3x text-muted mb-3"></i>
            <h4>No inventories yet</h4>
            <p class="text-muted">Create your first inventory to organize your goods</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createInventoryModal">
                Create Inventory
            </button>
        </div>
    {% endif %}
</div>

<!-- Create Inventory Modal -->
<div class="modal fade" id="createInventoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Inventory</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
              <form method="POST" action="{% url 'inventories_list_create' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="inventoryName" class="form-label">Inventory Name *</label>
                        <input type="text" class="form-control" id="inventoryName" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="inventoryQuantity" class="form-label">Initial Quantity</label>
                        <input type="number" class="form-control" id="inventoryQuantity" name="quantity" min="0" value="0">
                    </div>
  <div class="mb-3">
      <label for="store_id" class="form-label">Select Store</label>
      <select id="store_id" name="store_id" class="form-select" required>
        <option value="">-- Select Store --</option>
        {% for store in stores %}
          <option value="{{ store.id }}" {% if store.id|stringformat:"s" == old_store_id|stringformat:"s" %}selected{% endif %}>{{ store.name }}</option>
        {% endfor %}
      </select>
    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Inventory</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Load stores when modal opens
document.getElementById('createInventoryModal').addEventListener('show.bs.modal', function() {
    loadStores();
});

// Pre-select store if store_id is in URL params
document.addEventListener('DOMContentLoaded', function() {
    const urlParams = new URLSearchParams(window.location.search);
    const storeId = urlParams.get('store_id');
    if (storeId) {
        setTimeout(() => {
            document.getElementById('storeSelect').value = storeId;
        }, 500);
    }
});

function loadStores() {
    fetch('{% url "stores_list_create" %}', {
        method: 'GET',
        headers: {
            'Accept': 'application/json'
        }
    })
    .then(response => response.text())
    .then(html => {
        // Parse the HTML to extract store data
        const parser = new DOMParser();
        const doc = parser.parseFromString(html, 'text/html');
        const storeCards = doc.querySelectorAll('.card');
        const storeSelect = document.getElementById('storeSelect');
        
        // Clear existing options except the first one
        storeSelect.innerHTML = '<option value="">Choose a store...</option>';
        
        // If no stores found, add a message
        if (storeCards.length === 0) {
            storeSelect.innerHTML = '<option value="">No stores available - Create a store first</option>';
            return;
        }
        
        // Extract store information from cards (this is a workaround)
        // In a real application, you'd want a proper JSON endpoint for stores
        storeCards.forEach(card => {
            const titleElement = card.querySelector('.card-title');
            const viewLink = card.querySelector('a[href*="store_detail"]');
            
            if (titleElement && viewLink) {
                const storeName = titleElement.textContent.trim();
                const href = viewLink.getAttribute('href');
                const storeId = href.split('/').filter(part => part).pop();
                
                if (storeName && storeId) {
                    const option = document.createElement('option');
                    option.value = storeId;
                    option.textContent = storeName;
                    storeSelect.appendChild(option);
                }
            }
        });
        
        // Pre-select store if store_id is in URL params
        const urlParams = new URLSearchParams(window.location.search);
        const storeId = urlParams.get('store_id');
        if (storeId) {
            storeSelect.value = storeId;
        }
    })
    .catch(error => {
        console.error('Error loading stores:', error);
        document.getElementById('storeSelect').innerHTML = '<option value="">Error loading stores</option>';
    });
}

document.getElementById('createInventoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        name: document.getElementById('inventoryName').value,
        quantity: parseInt(document.getElementById('inventoryQuantity').value) || 0,
        store_id: document.getElementById('storeSelect').value
    };
    
    if (!formData.store_id) {
        alert('Please select a store');
        return;
    }
    
    fetch('{% url "inventories_list_create" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
        },
        body: JSON.stringify(formData)
    })
    .then(response => {
        if (response.redirected) {
            window.location.href = response.url;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error creating inventory');
    });
});
</script>
{% endblock %}