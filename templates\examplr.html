{% extends 'base.html' %}

{% block title %}{{ store.name }} - Store Details{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'stores_list_create' %}">Stores</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ store.name }}</li>
        </ol>
    </nav>

    <!-- Alert Container -->
    <div id="alert-container"></div>

    <!-- Store Header -->
    <div class="store-header mb-4">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h2 class="store-title">{{ store.name }}</h2>
                <p class="store-description text-muted">{{ store.description|default:"No description" }}</p>
                <small class="text-muted">Created: {{ store.created_at|date:"M d, Y H:i" }}</small>
            </div>
            <div class="btn-group">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editStoreModal">
                    <i class="fas fa-edit"></i> Edit Store
                </button>
                <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteStoreModal">
                    <i class="fas fa-trash"></i> Delete Store
                </button>
            </div>
        </div>
    </div>

    <!-- Goods Management Section -->
    <div class="goods-section">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes text-primary"></i> Store Goods
                        <span class="badge bg-primary ms-2">{{ store.goods.count }}</span>
                    </h5>
                    <div class="header-actions d-flex gap-2 align-items-center">
                        <!-- Search Bar -->
                        <div class="search-container">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchGoods" placeholder="Search goods...">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <!-- Filter Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item filter-option" href="#" data-filter="all">All Items</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="in-stock">In Stock</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="low-stock">Low Stock</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="out-of-stock">Out of Stock</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="used">Used Items</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="new">New Items</a></li>
                            </ul>
                        </div>
                        <!-- Add Goods Button -->
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGoodsModal">
                            <i class="fas fa-plus"></i> Add Goods
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Goods Grid -->
                <div id="goods-container">
                    {% if store.goods.all %}
                        <div class="row g-4" id="goods-grid">
                            {% for good in store.goods.all %}
                                <div class="col-lg-4 col-md-6 goods-item" 
                                     data-goods-id="{{ good.id }}"
                                     data-name="{{ good.name|lower }}"
                                     data-category="{{ good.category|default:'uncategorized'|lower }}"
                                     data-stock-status="{% if good.quantity == 0 %}out-of-stock{% elif good.quantity <= 5 %}low-stock{% else %}in-stock{% endif %}"
                                     data-condition="{% if good.is_used %}used{% else %}new{% endif %}">
                                    <div class="card goods-card h-100">
                                        <div class="card-img-container">
                                            {% if good.images.exists %}
                                                <img src="{{ good.images.first.image.url }}" class="card-img-top goods-image" alt="{{ good.name }}">
                                            {% else %}
                                                <div class="placeholder-image">
                                                    <i class="fas fa-image fa-3x text-muted"></i>
                                                </div>
                                            {% endif %}
                                            
                                            <!-- Price Badge -->
                                            <div class="price-badge">₦{{ good.price|floatformat:2 }}</div>
                                            
                                            <!-- Stock Status Badge -->
                                            {% if good.quantity == 0 %}
                                                <div class="stock-badge out-of-stock">Out of Stock</div>
                                            {% elif good.quantity <= 5 %}
                                                <div class="stock-badge low-stock">Low Stock</div>
                                            {% endif %}
                                        </div>
                                        
                                        <div class="card-body d-flex flex-column">
                                            <h6 class="card-title goods-name">{{ good.name }}</h6>
                                            <p class="card-text text-muted small flex-grow-1">
                                                {{ good.description|default:"No description"|truncatewords:15 }}
                                            </p>
                                            
                                            <!-- Goods Meta Info -->
                                            <div class="goods-meta mb-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <span class="quantity-badge">
                                                        <i class="fas fa-cubes"></i> {{ good.quantity }}
                                                    </span>
                                                    {% if good.category %}
                                                        <span class="badge bg-secondary">{{ good.category }}</span>
                                                    {% endif %}
                                                </div>
                                                
                                                <!-- Status Badges -->
                                                <div class="status-badges">
                                                    {% if good.is_used %}
                                                        <span class="badge bg-warning text-dark">Used</span>
                                                    {% else %}
                                                        <span class="badge bg-success">New</span>
                                                    {% endif %}
                                                    {% if good.available_for_delivery %}
                                                        <span class="badge bg-info">Delivery</span>
                                                    {% endif %}
                                                    {% if good.available_for_bulk_sales %}
                                                        <span class="badge bg-primary">Bulk Sales</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                            
                                            <!-- Action Buttons -->
                                            <div class="btn-group w-100 mt-auto">
                                                <a href="{% url 'goods_detail' good.id %}" class="btn btn-primary btn-sm">
                                                    <i class="fas fa-eye"></i> View
                                                </a>
                                                <button class="btn btn-outline-warning btn-sm" onclick="editGoods({{ good.id }})">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" onclick="deleteGoods({{ good.id }})">
                                                    <i class="fas fa-trash"></i> Delete
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- No Results Message (Hidden by default) -->
                        <div id="no-results-message" class="text-center py-5" style="display: none;">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h6>No goods found</h6>
                            <p class="text-muted">Try adjusting your search or filter criteria</p>
                        </div>
                        
                    {% else %}
                        <!-- Empty State -->
                        <div class="text-center py-5" id="empty-state">
                            <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
                            <h5>No goods in this store yet</h5>
                            <p class="text-muted mb-4">Add your first goods to get started with inventory management</p>
                            <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addGoodsModal">
                                <i class="fas fa-plus"></i> Add Your First Goods
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Goods Modal -->
<div class="modal fade" id="addGoodsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle text-primary"></i> Add New Goods
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            
            <form id="addGoodsForm" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="goodsName" class="form-label">Goods Name *</label>
                                <input type="text" class="form-control" id="goodsName" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="goodsCategory" class="form-label">Category</label>
                                <div class="input-group">
                                    <select class="form-select" id="goodsCategory" name="category_id">
                                        <option value="">Select Category (Optional)</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="goodsDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="goodsDescription" name="description" rows="3" placeholder="Describe your goods..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="goodsQuantity" class="form-label">Quantity *</label>
                                <input type="number" class="form-control" id="goodsQuantity" name="quantity" min="0" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="goodsPrice" class="form-label">Price (₦) *</label>
                                <input type="number" class="form-control" id="goodsPrice" name="price" step="0.01" min="0" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Store</label>
                                <input type="text" class="form-control" value="{{ store.name }}" readonly>
                                <input type="hidden" name="store_id" value="{{ store.id }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="goodsImages" class="form-label">Images</label>
                        <input type="file" class="form-control" id="goodsImages" name="images" multiple accept="image/*">
                        <div class="form-text">Select up to 5 images (JPEG, PNG)</div>
                    </div>
                    
                    <!-- Options -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="isUsed" name="is_used">
                                <label class="form-check-label" for="isUsed">This is a used item</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="availableForDelivery" name="available_for_delivery">
                                <label class="form-check-label" for="availableForDelivery">Available for delivery</label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="deliveryTypeSection" class="mb-3" style="display: none;">
                        <label for="deliveryType" class="form-label">Delivery Type</label>
                        <select class="form-select" id="deliveryType" name="delivery_type">
                            <option value="">Select Delivery Type</option>
                            <option value="within_state">Within State</option>
                            <option value="within_country">Within Country</option>
                            <option value="outside_country">Outside Country</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="availableForBulk" name="available_for_bulk_sales">
                        <label class="form-check-label" for="availableForBulk">Available for bulk sales</label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="addGoodsBtn">
                        <span class="spinner-border spinner-border-sm d-none me-2" id="addGoodsSpinner"></span>
                        Add Goods
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Category Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickCategoryForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required placeholder="Enter category name">
                        <div class="invalid-feedback" id="categoryNameError"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCategoryBtn">
                    <span class="spinner-border spinner-border-sm d-none me-2" id="categorySpinner"></span>
                    Create Category
                </button>
            </div>
        </div>
    </div>
</div>

<script>
class StoreGoodsManager {
    constructor() {
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.init();
    }

    init() {
        this.loadCategories();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Form submissions
        document.getElementById('addGoodsForm').addEventListener('submit', (e) => this.handleAddGoods(e));
        document.getElementById('saveCategoryBtn').addEventListener('click', () => this.createCategory());

        // Search functionality
        const searchInput = document.getElementById('searchGoods');
        searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        
        document.getElementById('clearSearch').addEventListener('click', () => {
            searchInput.value = '';
            this.handleSearch('');
        });

        // Filter functionality
        document.querySelectorAll('.filter-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFilter(e.target.dataset.filter);
            });
        });

        // Modal events
        document.getElementById('addGoodsModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm('addGoodsForm');
        });

        document.getElementById('createCategoryModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm('quickCategoryForm');
        });

        // Delivery checkbox
        document.getElementById('availableForDelivery').addEventListener('change', (e) => {
            document.getElementById('deliveryTypeSection').style.display = e.target.checked ? 'block' : 'none';
        });

        // Category name enter key
        document.getElementById('categoryName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.createCategory();
            }
        });
    }

    handleSearch(query) {
        this.searchQuery = query.toLowerCase();
        this.filterGoods();
    }

    handleFilter(filter) {
        this.currentFilter = filter;
        this.filterGoods();
        
        // Update filter button text
        const filterBtn = document.querySelector('.dropdown-toggle');
        const filterText = filter === 'all' ? 'Filter' : `Filter: ${filter.replace('-', ' ')}`;
        filterBtn.innerHTML = `<i class="fas fa-filter"></i> ${filterText}`;
    }

    filterGoods() {
        const goodsItems = document.querySelectorAll('.goods-item');
        let visibleCount = 0;

        goodsItems.forEach(item => {
            const name = item.dataset.name;
            const category = item.dataset.category;
            const stockStatus = item.dataset.stockStatus;
            const condition = item.dataset.condition;

            // Search filter
            const matchesSearch = !this.searchQuery || 
                name.includes(this.searchQuery) || 
                category.includes(this.searchQuery);

            // Category/Status filter
            let matchesFilter = true;
            switch (this.currentFilter) {
                case 'in-stock':
                    matchesFilter = stockStatus === 'in-stock';
                    break;
                case 'low-stock':
                    matchesFilter = stockStatus === 'low-stock';
                    break;
                case 'out-of-stock':
                    matchesFilter = stockStatus === 'out-of-stock';
                    break;
                case 'used':
                    matchesFilter = condition === 'used';
                    break;
                case 'new':
                    matchesFilter = condition === 'new';
                    break;
                default:
                    matchesFilter = true;
            }

            const shouldShow = matchesSearch && matchesFilter;
            item.style.display = shouldShow ? 'block' : 'none';
            
            if (shouldShow) visibleCount++;
        });

        // Show/hide no results message
        const noResultsMsg = document.getElementById('no-results-message');
        const goodsGrid = document.getElementById('goods-grid');
        
        if (visibleCount === 0 && goodsItems.length > 0) {
            goodsGrid.style.display = 'none';
            noResultsMsg.style.display = 'block';
        } else {
            goodsGrid.style.display = 'flex';
            noResultsMsg.style.display = 'none';
        }
    }

async handleAddGoods(event) {
    event.preventDefault();
    
    const form = document.getElementById('addGoodsForm');
    const formData = new FormData(form);
    const submitBtn = document.getElementById('addGoodsBtn');
    const spinner = document.getElementById('addGoodsSpinner');
    
    this.clearFormErrors();
    this.setLoadingState(submitBtn, spinner, true, 'Adding...');
    
    try {
        // Get CSRF token from the form
        const csrfToken = form.querySelector('[name=csrfmiddlewaretoken]').value;
        
        const response = await fetch('/goods/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        // Since your Django view returns HTML, we need to handle it differently
        const responseText = await response.text();
        
        if (response.ok) {
            // Check if the response contains success message
            if (responseText.includes('Good created successfully!') || 
                responseText.includes('message')) {
                this.showAlert('success', 'Goods added successfully!');
                form.reset();
                bootstrap.Modal.getInstance(document.getElementById('addGoodsModal')).hide();
                
                // Reload page after short delay
                setTimeout(() => window.location.reload(), 1000);
            } else if (responseText.includes('error')) {
                // Try to extract error message from HTML
                const errorMatch = responseText.match(/error['"]\s*:\s*['"]([^'"]+)['"]/);
                const errorMessage = errorMatch ? errorMatch[1] : 'Failed to add goods';
                this.showAlert('danger', errorMessage);
            } else {
                // Fallback - assume success if no error found
                this.showAlert('success', 'Goods added successfully!');
                form.reset();
                bootstrap.Modal.getInstance(document.getElementById('addGoodsModal')).hide();
                setTimeout(() => window.location.reload(), 1000);
            }
        } else {
            // Handle HTTP errors
            if (responseText.includes('Name, Price, and Store ID are required')) {
                this.showAlert('danger', 'Please fill in all required fields (Name, Price)');
            } else {
                this.showAlert('danger', 'Failed to add goods. Please try again.');
            }
        }
    } catch (error) {
        console.error('Error adding goods:', error);
        this.showAlert('danger', 'Network error. Please check your connection and try again.');
    } finally {
        this.setLoadingState(submitBtn, spinner, false, 'Add Goods');
    }
}

    async createCategory() {
        const nameInput = document.getElementById('categoryName');
        const saveBtn = document.getElementById('saveCategoryBtn');
        const spinner = document.getElementById('categorySpinner');
        const errorDiv = document.getElementById('categoryNameError');
        
        const name = nameInput.value.trim();
        
        if (!name) {
            this.showFieldError(nameInput, errorDiv, 'Category name is required');
            return;
        }
        
        this.setLoadingState(saveBtn, spinner, true, 'Creating...');
        this.clearFieldError(nameInput, errorDiv);
        
        const formData = new FormData();
        formData.append('name', name);
        
        try {
            const response = await fetch('/categories/create/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCookie('csrftoken')
                }
            });
            
            const data = await response.json();
            
            if (data.message) {
                bootstrap.Modal.getInstance(document.getElementById('createCategoryModal')).hide();
                this.showAlert('success', data.message);
                this.loadCategories();
                
                setTimeout(() => {
                    const select = document.getElementById('goodsCategory');
                    const newOption = Array.from(select.options).find(option => 
                        option.textContent.toLowerCase() === name.toLowerCase()
                    );
                    if (newOption) newOption.selected = true;
                }, 500);
            } else if (data.error) {
                this.showFieldError(nameInput, errorDiv, data.error);
            }
        } catch (error) {
            console.error('Error creating category:', error);
            this.showFieldError(nameInput, errorDiv, 'An error occurred. Please try again.');
        } finally {
            this.setLoadingState(saveBtn, spinner, false, 'Create Category');
        }
    }

    async loadCategories() {
        const select = document.getElementById('goodsCategory');
        
        try {
            const response = await fetch('/categories/seller/', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                }
            });
            
            if (!response.ok) throw new Error('Network response was not ok');
            
            const data = await response.json();
            
            // Clear existing options except first
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }
            
            if (data.categories && data.categories.length > 0) {
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            this.showAlert('danger', 'Error loading categories. Please try again.');
        }
    }

    // Utility Methods
    setLoadingState(button, spinner, isLoading, text = '') {
        button.disabled = isLoading;
        if (spinner) {
            spinner.classList.toggle('d-none', !isLoading);
        }
        if (text) {
            button.innerHTML = isLoading ? 
                `<span class="spinner-border spinner-border-sm me-2"></span>${text}` : 
                text;
        }
    }

    showAlert(type, message) {
        const alertContainer = document.getElementById('alert-container');
        const alertId = 'alert-' + Date.now();
        
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHTML);
        
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                bootstrap.Alert.getOrCreateInstance(alert).close();
            }
        }, 5000);
    }

    showFieldError(input, errorDiv, message) {
        input.classList.add('is-invalid');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    clearFieldError(input, errorDiv) {
        input.classList.remove('is-invalid');
        errorDiv.textContent = '';
        errorDiv.style.display = 'none';
    }

    clearFormErrors() {
        const inputs = document.querySelectorAll('#addGoodsForm .form-control, #addGoodsForm .form-select');
        const errorDivs = document.querySelectorAll('#addGoodsForm .invalid-feedback');
        
        inputs.forEach(input => input.classList.remove('is-invalid'));
        errorDivs.forEach(div => {
            div.textContent = '';
            div.style.display = 'none';
        });
    }

    displayFormErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            const input = document.querySelector(`[name="${fieldName}"]`);
            if (input) {
                const errorDiv = input.parentNode.querySelector('.invalid-feedback');
                if (errorDiv) {
                    this.showFieldError(input, errorDiv, errors[fieldName][0]);
                }
            }
        });
    }

    resetForm(formId) {
        const form = document.getElementById(formId);
        form.reset();
        this.clearFormErrors();
        
        if (formId === 'quickCategoryForm') {
            const nameInput = document.getElementById('categoryName');
            const errorDiv = document.getElementById('categoryNameError');
            this.clearFieldError(nameInput, errorDiv);
        }
    }

    getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
}

// Global functions for backwards compatibility
function editGoods(goodsId) {
    console.log('Edit goods:', goodsId);
    // Implement edit functionality
}

async function deleteGoods(goodsId) {
    if (!confirm('Are you sure you want to delete this goods item?')) return;
    
    try {
        const response = await fetch(`/goods/${goodsId}/delete/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': storeManager.getCookie('csrftoken'),
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (data.success) {
            storeManager.showAlert('success', 'Goods deleted successfully');
            const goodsItem = document.querySelector(`[data-goods-id="${goodsId}"]`);
            if (goodsItem) {
                goodsItem.remove();
                storeManager.filterGoods(); // Refresh the view
            }
        } else {
            storeManager.showAlert('danger', data.error || 'Failed to delete goods');
        }
    } catch (error) {
        console.error('Error deleting goods:', error);
        storeManager.showAlert('danger', 'Error deleting goods. Please try again.');
    }
}

// Initialize the manager when DOM is loaded
let storeManager;
document.addEventListener('DOMContentLoaded', function() {
    storeManager = new StoreGoodsManager();
});
</script>
{% endblock %}