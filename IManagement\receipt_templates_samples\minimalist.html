<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Minimalist Receipt</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: Arial, sans-serif;
      max-width: 500px;
      margin: 30px auto;
      color: #444;
      padding: 15px;
      background: #fff;
      border: 1px solid #ddd;
      line-height: 1.4;
    }

    .receipt-header {
      text-align: center;
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 15px;
    }

    .logo-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      object-fit: cover;
      border: 1px solid #ddd;
      background: white;
    }

    h1 {
      font-weight: 700;
      color: #2c3e50;
      font-size: 1.8em;
    }

    .company-info {
      font-size: 0.95em;
      text-align: center;
      margin-bottom: 25px;
      color: #7f8c8d;
    }

    .company-name {
      font-weight: 600;
      color: #2c3e50;
      margin-bottom: 5px;
    }

    .receipt-details {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
      font-size: 0.9em;
      color: #666;
    }

    .client-info {
      margin-bottom: 20px;
      padding: 10px;
      background: #f9f9f9;
      border-radius: 4px;
      font-size: 0.9em;
    }

    .client-info strong {
      color: #2c3e50;
    }

    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 25px;
    }

    th, td {
      border-bottom: 1px solid #eee;
      padding: 8px 6px;
      text-align: left;
    }

    th {
      font-weight: 700;
      color: #555;
      background: #f8f9fa;
    }

    .item-quantity, .item-price {
      text-align: right;
      font-family: 'Courier New', monospace;
    }

    th:nth-child(2), th:nth-child(3), th:nth-child(4) {
      text-align: right;
    }

    .totals-section {
      border-top: 2px solid #eee;
      padding-top: 15px;
      margin-bottom: 25px;
    }

    .total-row {
      display: flex;
      justify-content: space-between;
      padding: 3px 0;
      font-size: 0.95em;
    }

    .total-row:last-child {
      font-size: 1.3em;
      font-weight: 700;
      margin-top: 10px;
      padding-top: 10px;
      border-top: 1px solid #ddd;
      color: #2c3e50;
    }

    .total-value {
      font-family: 'Courier New', monospace;
    }

    .signatures-section {
      margin-top: 35px;
      display: flex;
      justify-content: space-between;
      font-size: 0.85em;
      color: #7f8c8d;
    }

    .signature-block {
      width: 45%;
      text-align: center;
    }

    .signature-label {
      margin-bottom: 10px;
      font-weight: 600;
    }

    .signature-area {
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 5px;
    }

    .signature-area img {
      max-width: 100%;
      max-height: 45px;
      object-fit: contain;
    }

    .signature-line {
      border-top: 1px solid #ccc;
      padding-top: 5px;
    }

    .receipt-footer {
      text-align: center;
      margin-top: 20px;
      font-size: 0.8em;
      color: #7f8c8d;
      font-style: italic;
    }

    @media (max-width: 600px) {
      body {
        margin: 10px;
        padding: 10px;
      }
      
      .receipt-header {
        flex-direction: column;
        gap: 10px;
      }
      
      .receipt-details {
        flex-direction: column;
        gap: 5px;
      }
      
      .signatures-section {
        flex-direction: column;
        gap: 20px;
      }
      
      .signature-block {
        width: 100%;
      }
    }

    @media print {
      body {
        margin: 0;
        border: none;
      }
    }
  </style>
  <script>
    // Auto-generate receipt number and date if not provided
    document.addEventListener('DOMContentLoaded', function() {
      // Generate random receipt number
      const receiptNumberElements = document.querySelectorAll('[data-auto="receipt-number"]');
      receiptNumberElements.forEach(element => {
        if (!element.textContent.trim()) {
          const randomNumber = Math.floor(Math.random() * 900000) + 100000;
          element.textContent = `#RCP${randomNumber}`;
        }
      });
      
      // Generate current date and time
      const dateElements = document.querySelectorAll('[data-auto="date"]');
      dateElements.forEach(element => {
        if (!element.textContent.trim()) {
          const now = new Date();
          const day = String(now.getDate()).padStart(2, '0');
          const month = String(now.getMonth() + 1).padStart(2, '0');
          const year = now.getFullYear();
          const hours = String(now.getHours()).padStart(2, '0');
          const minutes = String(now.getMinutes()).padStart(2, '0');
          
          element.textContent = `${day}/${month}/${year} ${hours}:${minutes}`;
        }
      });
    });
  </script>
</head>
<body>
  <header class="receipt-header">
    {% if avatar_url %}
      <img src="{{ avatar_url }}" alt="Company Logo" class="logo-image" />
    {% endif %}
    <h1>Receipt</h1>
  </header>

  <section class="company-info">
    <div class="company-name">{{ company_name }}</div>
    {% if phone_number %}
    <div>Phone: {{ phone_number }}</div>
    {% endif %}
    {% if secondary_email %}
    <div>Email: {{ secondary_email }}</div>
    {% endif %}
    <p>Thank you for your business!</p>
  </section>

  <section class="receipt-details">
    <div>
      Receipt No: 
      {% if receipt_number %}
        {{ receipt_number }}
      {% else %}
        <span data-auto="receipt-number"></span>
      {% endif %}
    </div>
    <div>
      Date: 
      {% if transaction_date %}
        {{ transaction_date }}
      {% else %}
        <span data-auto="date"></span>
      {% endif %}
    </div>
  </section>

  {% if client_name or client_email or client_phone or client_address %}
  <section class="client-info">
    <strong>Customer Details:</strong><br>
    {% if client_name %}{{ client_name }}<br>{% endif %}
    {% if client_email %}{{ client_email }}<br>{% endif %}
    {% if client_phone %}{{ client_phone }}<br>{% endif %}
    {% if client_address %}{{ client_address }}{% endif %}
  </section>
  {% endif %}

  <table>
    <thead>
      <tr>
        <th>Item</th>
        <th>Qty</th>
        <th>Unit Price</th>
        <th>Total</th>
      </tr>
    </thead>
    <tbody>
      {% for item in items %}
      <tr>
        <td>{{ item.name }}</td>
        <td class="item-quantity">{{ item.quantity }}</td>
        <td class="item-price">{{ currency_symbol }}{{ item.unit_price }}</td>
        <td class="item-price">{{ currency_symbol }}{{ item.total_price }}</td>
      </tr>
      {% endfor %}
    </tbody>
  </table>

  <section class="totals-section">
    <div class="total-row">
      <span>Subtotal:</span>
      <span class="total-value">{{ currency_symbol }} {{ subtotal|default:total_price }}</span>
    </div>
    {% if tax_amount %}
    <div class="total-row">
      <span>Tax:</span>
      <span class="total-value">{{ currency_symbol }} {{ tax_amount }}</span>
    </div>
    {% endif %}
    {% if discount_amount %}
    <div class="total-row">
      <span>Discount:</span>
      <span class="total-value">-{{ currency_symbol }} {{ discount_amount }}</span>
    </div>
    {% endif %}
    <div class="total-row">
      <span>Total:</span>
      <span class="total-value">{{ currency_symbol }} {{ total_price }}</span>
    </div>
  </section>

  <section class="signatures-section">
    <div class="signature-block">
      <div class="signature-label">Signature</div>
      <div class="signature-area">
        {% if signature_url %}
          <img src="{{ signature_url }}" alt="Signature" />
        {% endif %}
      </div>
      <div class="signature-line">____________________</div>
    </div>

    <div class="signature-block">
      <div class="signature-label">Stamp</div>
      <div class="signature-area">
        {% if stamp_url %}
          <img src="{{ stamp_url }}" alt="Official Stamp" />
        {% endif %}
      </div>
      <div class="signature-line">____________________</div>
    </div>
  </section>

  <footer class="receipt-footer">
    <p>Please retain this receipt for your records</p>
  </footer>
</body>
</html>