<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Net Total Value</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .total-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        }
        .total-value {
            font-size: 4rem;
            font-weight: 300;
            text-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="container">
            <a class="navbar-brand" href="{% url 'goods-management' %}">
                <i class="fas fa-arrow-left me-2"></i>Back to Goods
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">Welcome, {{ user.username }}!</span>
                <form method="post" action="{% url 'logout' %}" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-outline-light btn-sm border-0">
                        <i class="fas fa-sign-out-alt me-1"></i>Logout
                    </button>
                </form>
            </div>
        </div>
    </nav>

    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-lg-6">
                <div class="card total-card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-chart-line fa-4x mb-4 opacity-75"></i>
                        <h2 class="mb-4">Net Total Value</h2>
                        <div class="total-value">${{ net_total_value|floatformat:2 }}</div>
                        <p class="mt-3 opacity-75">Total value of all your goods in inventory</p>
                        <a href="{% url 'goods-management' %}" class="btn btn-light btn-lg mt-3">
                            <i class="fas fa-boxes me-2"></i>View All Goods
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
</body>
</html>