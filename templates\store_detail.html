{% extends 'base.html' %}

{% block title %}{{ store.name }} - Store Details{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- Breadcrumb Navigation -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'stores_list_create' %}">Stores</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ store.name }}</li>
        </ol>
    </nav>

    <!-- Alert Container -->
    <div id="alert-container"></div>

    

    <!-- Store Header -->
    <div class="store-header mb-4">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h2 class="store-title">{{ store.name }}</h2>
                <p class="store-description text-muted">{{ store.description|default:"No description" }}</p>
                <small class="text-muted">Created: {{ store.created_at|date:"M d, Y H:i" }}</small>
            </div>
            {% comment %} <div class="btn-group">
                <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#editStoreModal">
                    <i class="fas fa-edit"></i> Edit Store
                </button>
                <button class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteStoreModal">
                    <i class="fas fa-trash"></i> Delete Store
                </button>
            </div> {% endcomment %}
        </div>
    </div>

       <!-- Buttons for add and remove user -->
<div class="mb-3">
<a href="{% url 'store_add_users' store.id %}" class="btn btn-success me-2">
    Add User to Store
</a>

<!-- Replace store.id with the actual store ID in the template context -->
<a href="{% url 'remove_users_from_store' store.id %}" class="btn btn-danger me-2">
    Remove Users from Store
</a>

<!-- Store Sharing Section -->
<div class="card mb-4 mt-2 border-primary">
    <div class="card-header bg-primary text-white">
        <h6 class="mb-0">
            <i class="fas fa-share-alt"></i> Store Sharing & Collaboration
        </h6>
    </div>
    <div class="card-body">
        <p class="card-text mb-3">Share your store with customers, partners, or collaborators via secure links or email invitations.</p>
        <div class="d-flex flex-wrap gap-2">
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#shareStoreModal">
                <i class="fas fa-link"></i> Create Share Link
            </button>
            <button class="btn btn-success" onclick="openQuickEmailShare()">
                <i class="fas fa-envelope"></i> Email Share
            </button>
            <button class="btn btn-info" onclick="viewStoreShares()">
                <i class="fas fa-eye"></i> Manage Shares
            </button>
            <button class="btn btn-outline-secondary" onclick="copyStoreUrl()">
                <i class="fas fa-copy"></i> Copy Store URL
            </button>
        </div>
    </div>
</div>
 </div>

    <!-- Goods Management Section -->
    <div class="goods-section">
        <div class="card shadow-sm">
            <div class="card-header bg-white">
                <div class="d-flex justify-content-between align-items-center flex-wrap">
                    <h5 class="mb-0">
                        <i class="fas fa-boxes text-primary"></i> Store Goods
                        <span class="badge bg-primary ms-2">{{ store.goods.count }}</span>
                    </h5>
                    <div class="header-actions d-flex gap-2 align-items-center">
                        <!-- Search Bar -->
                        <div class="search-container">
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchGoods" placeholder="Search goods...">
                                <button class="btn btn-outline-secondary" type="button" id="clearSearch">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <!-- Filter Dropdown -->
                        <div class="dropdown">
                            <button class="btn btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <i class="fas fa-filter"></i> Filter
                            </button>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item filter-option" href="#" data-filter="all">All Items</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="in-stock">In Stock</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="low-stock">Low Stock</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="out-of-stock">Out of Stock</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="used">Used Items</a></li>
                                <li><a class="dropdown-item filter-option" href="#" data-filter="new">New Items</a></li>
                            </ul>
                        </div>
                        <!-- Add Goods Button -->
                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addGoodsModal">
                            <i class="fas fa-plus"></i> Add Goods
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                <!-- Goods List -->
                <div id="goods-container">
                    {% if store.goods.all %}
                        <div id="goods-list">
                            {% for good in store.goods.all %}
                                <div class="goods-item border-bottom py-3" 
                                     data-goods-id="{{ good.id }}"
                                     data-name="{{ good.name|lower }}"
                                     data-category="{{ good.category|default:'uncategorized'|lower }}"
                                     data-stock-status="{% if good.quantity == 0 %}out-of-stock{% elif good.quantity <= 5 %}low-stock{% else %}in-stock{% endif %}"
                                     data-condition="{% if good.is_used %}used{% else %}new{% endif %}">
                                    <div class="row align-items-center">
    
<!-- Goods Information -->
<div class="col-md-8">
    <div class="d-flex justify-content-between align-items-start mb-2">
        <h6 class="goods-name mb-1 fw-bold">{{ good.name }}</h6>
        <span class="price-tag fw-bold text-success fs-5">{{ good.currency_symbol }}{{ good.price|floatformat:2 }}</span>
    </div>
                                            
                                            {% if good.description %}
                                                <p class="text-muted mb-2 small">{{ good.description|truncatewords:20 }}</p>
                                            {% endif %}
                                            
                                            <div class="goods-details">
                                                <div class="row g-2">
                                                    <div class="col-auto">
                                                        <span class="badge bg-light text-dark border">
                                                            <i class="fas fa-cubes"></i> Qty: {{ good.quantity }}
                                                        </span>
                                                    </div>
                                                    
                                                    {% if good.category %}
                                                        <div class="col-auto">
                                                            <span class="badge bg-secondary">
                                                                <i class="fas fa-tag"></i> {{ good.category }}
                                                            </span>
                                                        </div>
                                                    {% endif %}
                                                    
                                                    <div class="col-auto">
                                                        {% if good.is_used %}
                                                            <span class="badge bg-warning text-dark">
                                                                <i class="fas fa-recycle"></i> Used
                                                            </span>
                                                        {% else %}
                                                            <span class="badge bg-success">
                                                                <i class="fas fa-star"></i> New
                                                            </span>
                                                        {% endif %}
                                                    </div>
                                                    
                                                    {% if good.available_for_delivery %}
                                                        <div class="col-auto">
                                                            <span class="badge bg-info">
                                                                <i class="fas fa-truck"></i> Delivery
                                                            </span>
                                                        </div>
                                                    {% endif %}
                                                    
                                                    {% if good.available_for_bulk_sales %}
                                                        <div class="col-auto">
                                                            <span class="badge bg-primary">
                                                                <i class="fas fa-boxes"></i> Bulk Sales
                                                            </span>
                                                        </div>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <!-- Stock Status -->
                                        <div class="col-md-4 text-md-end">
                                            {% if good.quantity == 0 %}
                                                <div class="stock-status out-of-stock">
                                                    <span class="badge bg-danger fs-6">
                                                        <i class="fas fa-exclamation-triangle"></i> Out of Stock
                                                    </span>
                                                </div>
                                            {% elif good.quantity <= 5 %}
                                                <div class="stock-status low-stock">
                                                    <span class="badge bg-warning text-dark fs-6">
                                                        <i class="fas fa-exclamation-circle"></i> Low Stock
                                                    </span>
                                                </div>
                                            {% else %}
                                                <div class="stock-status in-stock">
                                                    <span class="badge bg-success fs-6">
                                                        <i class="fas fa-check-circle"></i> In Stock
                                                    </span>
                                                </div>
                                            {% endif %}
                                            
                                            <!-- View Details Button -->
                                            <div class="mt-2">
                                                <a href="{% url 'goods_detail' good.id %}" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-eye"></i> View Details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        
                        <!-- No Results Message (Hidden by default) -->
                        <div id="no-results-message" class="text-center py-5" style="display: none;">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h6>No goods found</h6>
                            <p class="text-muted">Try adjusting your search or filter criteria</p>
                        </div>
                        
                    {% else %}
                        <!-- Empty State -->
                        <div class="text-center py-5" id="empty-state">
                            <i class="fas fa-boxes fa-4x text-muted mb-3"></i>
                            <h5>No goods in this store yet</h5>
                            <p class="text-muted mb-4">Add your first goods to get started with inventory management</p>
                            <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addGoodsModal">
                                <i class="fas fa-plus"></i> Add Your First Goods
                            </button>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Goods Modal -->
<div class="modal fade" id="addGoodsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus-circle text-primary"></i> Add New Goods
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            
            <form id="addGoodsForm" enctype="multipart/form-data">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="goodsName" class="form-label">Goods Name *</label>
                                <input type="text" class="form-control" id="goodsName" name="name" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="goodsCategory" class="form-label">Category</label>
                                <div class="input-group">
                                    <select class="form-select" id="goodsCategory" name="category_id">
                                        <option value="">Select Category (Optional)</option>
                                    </select>
                                    <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#createCategoryModal">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="goodsDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="goodsDescription" name="description" rows="3" placeholder="Describe your goods..."></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="goodsQuantity" class="form-label">Quantity *</label>
                                <input type="number" class="form-control" id="goodsQuantity" name="quantity" min="0" required>
                                <div class="invalid-feedback"></div>
                            </div>
                        </div>
<div class="col-md-4">
    <div class="mb-3">
        <label for="goodsPrice" class="form-label">Price *</label>
        <div class="input-group">
            <select class="form-select" id="goodsCurrency" name="currency" style="max-width: 100px;">
                <option value="NGN" selected>₦</option>
                <option value="USD">$</option>
                <option value="EUR">€</option>
                <option value="GBP">£</option>
            </select>
            <input type="number" class="form-control" id="goodsPrice" name="price" step="0.01" min="0" required placeholder="0.00">
        </div>
        <div class="invalid-feedback"></div>
    </div>
</div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">Store</label>
                                <input type="text" class="form-control" value="{{ store.name }}" readonly>
                                <input type="hidden" name="store_id" value="{{ store.id }}">
                            </div>
                        </div>
                    </div>


<div class="mb-3">
                        <label for="goodsImages" class="form-label">Images</label>
                        <input type="file" class="form-control" id="goodsImages" name="images" multiple accept="image/*">
                        <div class="form-text">Select up to 5 images (JPEG, PNG)</div>
                        
                        <!-- Image Preview Container -->
                        <div id="imagePreviewContainer" class="mt-3 d-none">
                            <div class="row g-2" id="imagePreviewGrid">
                                <!-- Preview images will be inserted here -->
                            </div>
                        </div>
                    </div>
                    
                    <!-- Options -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="isUsed" name="is_used">
                                <label class="form-check-label" for="isUsed">This is a used item</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="availableForDelivery" name="available_for_delivery">
                                <label class="form-check-label" for="availableForDelivery">Available for delivery</label>
                            </div>
                        </div>
                    </div>
                    
                    <div id="deliveryTypeSection" class="mb-3" style="display: none;">
                        <label for="deliveryType" class="form-label">Delivery Type</label>
                        <select class="form-select" id="deliveryType" name="delivery_type">
                            <option value="">Select Delivery Type</option>
                            <option value="within_state">Within State</option>
                            <option value="within_country">Within Country</option>
                            <option value="outside_country">Outside Country</option>
                        </select>
                    </div>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="availableForBulk" name="available_for_bulk_sales">
                        <label class="form-check-label" for="availableForBulk">Available for bulk sales</label>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="addGoodsBtn">
                        <span class="spinner-border spinner-border-sm d-none me-2" id="addGoodsSpinner"></span>
                        Add Goods
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Category Modal -->
<div class="modal fade" id="createCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="quickCategoryForm">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">Category Name</label>
                        <input type="text" class="form-control" id="categoryName" name="name" required placeholder="Enter category name">
                        <div class="invalid-feedback" id="categoryNameError"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveCategoryBtn">
                    <span class="spinner-border spinner-border-sm d-none me-2" id="categorySpinner"></span>
                    Create Category
                </button>
            </div>
        </div>
    </div>
</div>


<script>
class StoreGoodsManager {
    constructor() {
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.init();
    }

    init() {
        this.loadCategories();
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Form submissions
        document.getElementById('addGoodsForm').addEventListener('submit', (e) => this.handleAddGoods(e));
        document.getElementById('saveCategoryBtn').addEventListener('click', () => this.createCategory());

        // Search functionality
        const searchInput = document.getElementById('searchGoods');
        searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        
        document.getElementById('clearSearch').addEventListener('click', () => {
            searchInput.value = '';
            this.handleSearch('');
        });

        // Filter functionality
        document.querySelectorAll('.filter-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleFilter(e.target.dataset.filter);
            });
        });

        // Modal events
        document.getElementById('addGoodsModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm('addGoodsForm');
        });

        document.getElementById('createCategoryModal').addEventListener('hidden.bs.modal', () => {
            this.resetForm('quickCategoryForm');
        });

        // Delivery checkbox
        document.getElementById('availableForDelivery').addEventListener('change', (e) => {
            document.getElementById('deliveryTypeSection').style.display = e.target.checked ? 'block' : 'none';
        });

        // Category name enter key
        document.getElementById('categoryName').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                this.createCategory();
            }
        });
    }

    handleSearch(query) {
        this.searchQuery = query.toLowerCase();
        this.filterGoods();
    }

    handleFilter(filter) {
        this.currentFilter = filter;
        this.filterGoods();
        
        // Update filter button text
        const filterBtn = document.querySelector('.dropdown-toggle');
        const filterText = filter === 'all' ? 'Filter' : `Filter: ${filter.replace('-', ' ')}`;
        filterBtn.innerHTML = `<i class="fas fa-filter"></i> ${filterText}`;
    }

    filterGoods() {
        const goodsItems = document.querySelectorAll('.goods-item');
        let visibleCount = 0;

        goodsItems.forEach(item => {
            const name = item.dataset.name;
            const category = item.dataset.category;
            const stockStatus = item.dataset.stockStatus;
            const condition = item.dataset.condition;

            // Search filter
            const matchesSearch = !this.searchQuery || 
                name.includes(this.searchQuery) || 
                category.includes(this.searchQuery);

            // Category/Status filter
            let matchesFilter = true;
            switch (this.currentFilter) {
                case 'in-stock':
                    matchesFilter = stockStatus === 'in-stock';
                    break;
                case 'low-stock':
                    matchesFilter = stockStatus === 'low-stock';
                    break;
                case 'out-of-stock':
                    matchesFilter = stockStatus === 'out-of-stock';
                    break;
                case 'used':
                    matchesFilter = condition === 'used';
                    break;
                case 'new':
                    matchesFilter = condition === 'new';
                    break;
                default:
                    matchesFilter = true;
            }

            const shouldShow = matchesSearch && matchesFilter;
            item.style.display = shouldShow ? 'block' : 'none';
            
            if (shouldShow) visibleCount++;
        });

        // Show/hide no results message
        const noResultsMsg = document.getElementById('no-results-message');
        const goodsList = document.getElementById('goods-list');
        
        if (visibleCount === 0 && goodsItems.length > 0) {
            goodsList.style.display = 'none';
            noResultsMsg.style.display = 'block';
        } else {
            goodsList.style.display = 'block';
            noResultsMsg.style.display = 'none';
        }
    }

async handleAddGoods(event) {
    event.preventDefault();
    
    const form = document.getElementById('addGoodsForm');
    const formData = new FormData(form);
    const submitBtn = document.getElementById('addGoodsBtn');
    const spinner = document.getElementById('addGoodsSpinner');
    
    // Explicitly add currency value to ensure it's included
    const currencySelect = document.getElementById('goodsCurrency');
    formData.set('currency', currencySelect.value);
    
    this.clearFormErrors();
    this.setLoadingState(submitBtn, spinner, true, 'Adding...');
    
    try {
        // Get CSRF token from the form
        const csrfToken = form.querySelector('[name=csrfmiddlewaretoken]').value;
        
        const response = await fetch('/goods/', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': csrfToken,
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const responseText = await response.text();
        
        if (response.ok) {
            if (responseText.includes('Good created successfully!') || 
                responseText.includes('message')) {
                this.showAlert('success', 'Goods added successfully!');
                form.reset();
                bootstrap.Modal.getInstance(document.getElementById('addGoodsModal')).hide();
                
                setTimeout(() => window.location.reload(), 1000);
            } else if (responseText.includes('error')) {
                const errorMatch = responseText.match(/error['"]\s*:\s*['"]([^'"]+)['"]/);
                const errorMessage = errorMatch ? errorMatch[1] : 'Failed to add goods';
                this.showAlert('danger', errorMessage);
            } else {
                this.showAlert('success', 'Goods added successfully!');
                form.reset();
                bootstrap.Modal.getInstance(document.getElementById('addGoodsModal')).hide();
                setTimeout(() => window.location.reload(), 1000);
            }
        } else {
            if (responseText.includes('Name, Price, and Store ID are required')) {
                this.showAlert('danger', 'Please fill in all required fields (Name, Price)');
            } else {
                this.showAlert('danger', 'Failed to add goods. Please try again.');
            }
        }
    } catch (error) {
        console.error('Error adding goods:', error);
        this.showAlert('danger', 'Network error. Please check your connection and try again.');
    } finally {
        this.setLoadingState(submitBtn, spinner, false, 'Add Goods');
    }
}
    async createCategory() {
        const nameInput = document.getElementById('categoryName');
        const saveBtn = document.getElementById('saveCategoryBtn');
        const spinner = document.getElementById('categorySpinner');
        const errorDiv = document.getElementById('categoryNameError');
        
        const name = nameInput.value.trim();
        
        if (!name) {
            this.showFieldError(nameInput, errorDiv, 'Category name is required');
            return;
        }
        
        this.setLoadingState(saveBtn, spinner, true, 'Creating...');
        this.clearFieldError(nameInput, errorDiv);
        
        const formData = new FormData();
        formData.append('name', name);
        
        try {
            const response = await fetch('/categories/create/', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': this.getCookie('csrftoken')
                }
            });
            
            const data = await response.json();
            
            if (data.message) {
                bootstrap.Modal.getInstance(document.getElementById('createCategoryModal')).hide();
                this.showAlert('success', data.message);
                this.loadCategories();
                
                setTimeout(() => {
                    const select = document.getElementById('goodsCategory');
                    const newOption = Array.from(select.options).find(option => 
                        option.textContent.toLowerCase() === name.toLowerCase()
                    );
                    if (newOption) newOption.selected = true;
                }, 500);
            } else if (data.error) {
                this.showFieldError(nameInput, errorDiv, data.error);
            }
        } catch (error) {
            console.error('Error creating category:', error);
            this.showFieldError(nameInput, errorDiv, 'An error occurred. Please try again.');
        } finally {
            this.setLoadingState(saveBtn, spinner, false, 'Create Category');
        }
    }

    async loadCategories() {
        const select = document.getElementById('goodsCategory');
        
        try {
            const response = await fetch('/categories/seller/', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                }
            });
            
            if (!response.ok) throw new Error('Network response was not ok');
            
            const data = await response.json();
            
            // Clear existing options except first
            while (select.children.length > 1) {
                select.removeChild(select.lastChild);
            }
            
            if (data.categories && data.categories.length > 0) {
                data.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;
                    select.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Error loading categories:', error);
            this.showAlert('danger', 'Error loading categories. Please try again.');
        }
    }

    // Utility Methods
    setLoadingState(button, spinner, isLoading, text = '') {
        button.disabled = isLoading;
        if (spinner) {
            spinner.classList.toggle('d-none', !isLoading);
        }
        if (text) {
            button.innerHTML = isLoading ? 
                `<span class="spinner-border spinner-border-sm me-2"></span>${text}` : 
                text;
        }
    }

    showAlert(type, message) {
        const alertContainer = document.getElementById('alert-container');
        const alertId = 'alert-' + Date.now();
        
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        alertContainer.insertAdjacentHTML('beforeend', alertHTML);
        
        setTimeout(() => {
            const alert = document.getElementById(alertId);
            if (alert) {
                bootstrap.Alert.getOrCreateInstance(alert).close();
            }
        }, 5000);
    }

    showFieldError(input, errorDiv, message) {
        input.classList.add('is-invalid');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
    }

    clearFieldError(input, errorDiv) {
        input.classList.remove('is-invalid');
        errorDiv.textContent = '';
        errorDiv.style.display = 'none';
    }

    clearFormErrors() {
        const inputs = document.querySelectorAll('#addGoodsForm .form-control, #addGoodsForm .form-select');
        const errorDivs = document.querySelectorAll('#addGoodsForm .invalid-feedback');
        
        inputs.forEach(input => input.classList.remove('is-invalid'));
        errorDivs.forEach(div => {
            div.textContent = '';
            div.style.display = 'none';
        });
    }

    displayFormErrors(errors) {
        Object.keys(errors).forEach(fieldName => {
            const input = document.querySelector(`[name="${fieldName}"]`);
            if (input) {
                const errorDiv = input.parentNode.querySelector('.invalid-feedback');
                if (errorDiv) {
                    this.showFieldError(input, errorDiv, errors[fieldName][0]);
                }
            }
        });
    }

    resetForm(formId) {
        const form = document.getElementById(formId);
        form.reset();
        this.clearFormErrors();
        
        if (formId === 'quickCategoryForm') {
            const nameInput = document.getElementById('categoryName');
            const errorDiv = document.getElementById('categoryNameError');
            this.clearFieldError(nameInput, errorDiv);
        }
    }

    getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
}

// Initialize the manager when DOM is loaded
let goodsManager;
document.addEventListener('DOMContentLoaded', function() {
    goodsManager = new StoreGoodsManager();
});

// Additional utility functions for the template
function refreshGoodsList() {
    if (goodsManager) {
        goodsManager.filterGoods();
    }
}

// Handle dynamic content updates
function updateGoodsCount(count) {
    const badge = document.querySelector('.badge.bg-primary');
    if (badge) {
        badge.textContent = count;
    }
}

// Export for global access if needed
window.goodsManager = goodsManager;
</script>



{% comment %} for image preview {% endcomment %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('goodsImages');
    const previewContainer = document.getElementById('imagePreviewContainer');
    const previewGrid = document.getElementById('imagePreviewGrid');
    let selectedFiles = [];

    imageInput.addEventListener('change', function(e) {
        const files = Array.from(e.target.files);
        
        // Limit to 5 images
        if (files.length > 5) {
            alert('You can only upload up to 5 images');
            return;
        }
        
        selectedFiles = files;
        displayImagePreviews(files);
    });

    function displayImagePreviews(files) {
        previewGrid.innerHTML = '';
        
        if (files.length === 0) {
            previewContainer.classList.add('d-none');
            return;
        }
        
        previewContainer.classList.remove('d-none');
        
        files.forEach((file, index) => {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const previewCol = document.createElement('div');
                previewCol.className = 'col-md-2 col-sm-3 col-4';
                
                previewCol.innerHTML = `
                    <div class="position-relative">
                        <img src="${e.target.result}" class="img-fluid rounded shadow-sm" style="height: 100px; width: 100%; object-fit: cover;">
                        <button type="button" class="btn btn-danger btn-sm position-absolute rounded-circle d-flex align-items-center justify-content-center" 
                                style="width: 20px; height: 20px; top: -5px; right: -5px; padding: 0; font-size: 10px; line-height: 1;" onclick="removeImage(${index})">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                
                previewGrid.appendChild(previewCol);
            };
            
            reader.readAsDataURL(file);
        });
    }

    // Make removeImage function globally available
    window.removeImage = function(index) {
        selectedFiles.splice(index, 1);
        
        // Update the file input
        const dt = new DataTransfer();
        selectedFiles.forEach(file => dt.items.add(file));
        imageInput.files = dt.files;
        
        displayImagePreviews(selectedFiles);
    };
});
</script>

<!-- Store Sharing JavaScript -->
<script>
// Store Sharing Functionality
let currentShareToken = null;

// Toggle password section
document.getElementById('passwordProtected').addEventListener('change', function() {
    const passwordSection = document.getElementById('passwordSection');
    passwordSection.style.display = this.checked ? 'block' : 'none';
});

// Create Share Link Form
document.getElementById('createShareForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const data = {
        title: formData.get('title'),
        description: formData.get('description'),
        expires_at: formData.get('expires_at'),
        password_protected: formData.get('password_protected') === 'on',
        access_password: formData.get('access_password')
    };

    const button = this.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
    button.disabled = true;

    fetch(`/stores/{{ store.id }}/share/create/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            currentShareToken = data.share_token;
            document.getElementById('generatedLink').value = data.share_url;
            document.getElementById('generatedLinkSection').style.display = 'block';
            document.getElementById('emailShareToken').value = data.share_token;

            // Show success message
            showAlert('Share link created successfully!', 'success');
        } else {
            showAlert('Error: ' + (data.error || 'Failed to create share link'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while creating the share link', 'danger');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
});

// Email Share Form
document.getElementById('emailShareForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const emails = formData.get('emails').split(',').map(email => email.trim()).filter(email => email);

    if (emails.length === 0) {
        showAlert('Please enter at least one email address', 'warning');
        return;
    }

    const data = {
        emails: emails,
        message: formData.get('message'),
        share_token: formData.get('share_token') || currentShareToken
    };

    const button = this.querySelector('button[type="submit"]');
    const originalText = button.innerHTML;
    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Sending...';
    button.disabled = true;

    fetch(`/stores/{{ store.id }}/share/email/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const resultsHtml = `
                <p><strong>Successfully sent:</strong> ${data.total_sent} email(s)</p>
                ${data.successful_emails.length > 0 ? `<p class="text-success">✓ ${data.successful_emails.join(', ')}</p>` : ''}
                ${data.failed_emails.length > 0 ? `<p class="text-danger">✗ Failed: ${data.failed_emails.join(', ')}</p>` : ''}
                <p><strong>Share URL:</strong> <a href="${data.share_url}" target="_blank">${data.share_url}</a></p>
            `;
            document.getElementById('emailResults').innerHTML = resultsHtml;
            document.getElementById('emailResultsSection').style.display = 'block';

            showAlert(`Emails sent successfully! ${data.total_sent} sent, ${data.total_failed} failed.`, 'success');
        } else {
            showAlert('Error: ' + (data.error || 'Failed to send emails'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred while sending emails', 'danger');
    })
    .finally(() => {
        button.innerHTML = originalText;
        button.disabled = false;
    });
});

// Copy share link to clipboard
function copyShareLink() {
    const linkInput = document.getElementById('generatedLink');
    linkInput.select();
    linkInput.setSelectionRange(0, 99999);

    try {
        document.execCommand('copy');
        showAlert('Share link copied to clipboard!', 'success');
    } catch (err) {
        console.error('Failed to copy: ', err);
        showAlert('Failed to copy link. Please copy manually.', 'warning');
    }
}

// View Store Shares
function viewStoreShares() {
    const modal = new bootstrap.Modal(document.getElementById('storeSharesModal'));
    modal.show();

    // Load shares
    fetch(`/stores/{{ store.id }}/shares/`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayStoreShares(data.shares);
        } else {
            document.getElementById('sharesListContainer').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading shares: ${data.error}
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('sharesListContainer').innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle"></i> An error occurred while loading shares
            </div>
        `;
    });
}

// Display store shares
function displayStoreShares(shares) {
    if (shares.length === 0) {
        document.getElementById('sharesListContainer').innerHTML = `
            <div class="text-center py-4">
                <i class="fas fa-share-alt fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No Shares Created</h5>
                <p class="text-muted">You haven't shared this store yet.</p>
            </div>
        `;
        return;
    }

    const sharesHtml = shares.map(share => `
        <div class="card mb-3">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h6 class="card-title">${share.title}</h6>
                        ${share.description ? `<p class="card-text text-muted">${share.description}</p>` : ''}
                        <small class="text-muted">
                            Created: ${new Date(share.created_at).toLocaleDateString()}
                            ${share.expires_at ? ` | Expires: ${new Date(share.expires_at).toLocaleDateString()}` : ''}
                        </small>
                    </div>
                    <div class="text-end">
                        <span class="badge ${share.is_active && !share.is_expired ? 'bg-success' : 'bg-danger'} mb-2">
                            ${share.is_active && !share.is_expired ? 'Active' : 'Inactive'}
                        </span>
                        <br>
                        <small class="text-muted">${share.view_count} views</small>
                    </div>
                </div>

                <div class="mt-3">
                    <div class="input-group">
                        <input type="text" class="form-control" value="${share.share_url}" readonly>
                        <button class="btn btn-outline-secondary" onclick="copyToClipboard('${share.share_url}')">
                            <i class="fas fa-copy"></i>
                        </button>
                        <a href="${share.share_url}" target="_blank" class="btn btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                </div>

                ${share.password_protected ? '<div class="mt-2"><small class="text-warning"><i class="fas fa-lock"></i> Password Protected</small></div>' : ''}
                ${share.shared_via_email ? `<div class="mt-2"><small class="text-info"><i class="fas fa-envelope"></i> Shared with: ${share.recipient_emails.join(', ')}</small></div>` : ''}
            </div>
        </div>
    `).join('');

    document.getElementById('sharesListContainer').innerHTML = sharesHtml;
}

// Copy to clipboard utility
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('Link copied to clipboard!', 'success');
    }).catch(() => {
        showAlert('Failed to copy link', 'warning');
    });
}

// Utility function to show alerts
function showAlert(message, type) {
    const alertContainer = document.getElementById('alert-container');
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    alertContainer.innerHTML = alertHtml;

    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        const alert = alertContainer.querySelector('.alert');
        if (alert) {
            const bsAlert = new bootstrap.Alert(alert);
            bsAlert.close();
        }
    }, 5000);
}

// Quick Email Share from Store Detail
function openQuickEmailShare() {
    // Create a quick email share for this specific store
    const storeId = {{ store.id }};
    const storeName = "{{ store.name|escapejs }}";

    // Create and show email modal
    const modalHtml = `
        <div class="modal fade" id="quickEmailModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Email Share: ${storeName}</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="quickEmailForm">
                            <div class="mb-3">
                                <label for="quickEmails" class="form-label">Recipient Emails *</label>
                                <textarea class="form-control" id="quickEmails" rows="3"
                                          placeholder="Enter email addresses separated by commas" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="quickMessage" class="form-label">Message (Optional)</label>
                                <textarea class="form-control" id="quickMessage" rows="3"
                                          placeholder="Add a personal message..."></textarea>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-success" onclick="sendQuickEmail()">
                            <i class="fas fa-paper-plane"></i> Send Emails
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if any
    const existingModal = document.getElementById('quickEmailModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHtml);

    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('quickEmailModal'));
    modal.show();

    // Remove modal from DOM when hidden
    document.getElementById('quickEmailModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

function sendQuickEmail() {
    const emails = document.getElementById('quickEmails').value.split(',').map(email => email.trim()).filter(email => email);
    const message = document.getElementById('quickMessage').value;

    if (emails.length === 0) {
        showAlert('Please enter at least one email address', 'warning');
        return;
    }

    // First create a share link
    const shareData = {
        title: "Shared Store: {{ store.name|escapejs }}",
        description: message,
        password_protected: false,
        access_password: ''
    };

    fetch('/stores/{{ store.id }}/share/create/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(shareData)
    })
    .then(response => response.json())
    .then(shareResult => {
        if (shareResult.success) {
            // Now send emails
            const emailData = {
                emails: emails,
                message: message,
                share_token: shareResult.share_token
            };

            return fetch('/stores/{{ store.id }}/share/email/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(emailData)
            });
        } else {
            throw new Error(shareResult.error || 'Failed to create share link');
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(`Emails sent successfully! ${data.total_sent} sent, ${data.total_failed} failed.`, 'success');

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('quickEmailModal'));
            modal.hide();
        } else {
            showAlert('Error: ' + (data.error || 'Failed to send emails'), 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('An error occurred: ' + error.message, 'danger');
    });
}

function copyStoreUrl() {
    const storeUrl = window.location.href;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(storeUrl).then(() => {
            showAlert('Store URL copied to clipboard!', 'success');
        }).catch(() => {
            fallbackCopyTextToClipboard(storeUrl);
        });
    } else {
        fallbackCopyTextToClipboard(storeUrl);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";

    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        document.execCommand('copy');
        showAlert('Store URL copied to clipboard!', 'success');
    } catch (err) {
        showAlert('Failed to copy URL. Please copy manually: ' + text, 'warning');
    }

    document.body.removeChild(textArea);
}

// Get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>

<style>
#imagePreviewContainer img {
    transition: transform 0.2s ease;
}

#imagePreviewContainer img:hover {
    transform: scale(1.05);
}

.position-relative .btn-danger {
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    border: 2px solid white;
}

.position-relative .btn-danger:hover {
    transform: scale(1.1);
}
</style>

{% comment %} <!-- Edit Store Modal -->
<div class="modal fade" id="editStoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit text-primary"></i> Edit Store
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            
            <form id="editStoreForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="editStoreName" class="form-label">Store Name *</label>
                        <input type="text" class="form-control" id="editStoreName" name="name" value="{{ store.name }}" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="editStoreDescription" class="form-label">Description</label>
                        <textarea class="form-control" id="editStoreDescription" name="description" rows="3">{{ store.description|default:"" }}</textarea>
                    </div>
                </div>
                
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary" id="editStoreBtn">
                        <span class="spinner-border spinner-border-sm d-none me-2" id="editStoreSpinner"></span>
                        Update Store
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Store Modal -->
<div class="modal fade" id="deleteStoreModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle"></i> Delete Store
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Warning:</strong> This action cannot be undone.
                </div>
                <p>Are you sure you want to delete <strong>"{{ store.name }}"</strong>?</p>
                <p class="text-muted small">This will also delete all goods associated with this store.</p>
            </div>
            
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteStore">
                    <span class="spinner-border spinner-border-sm d-none me-2" id="deleteStoreSpinner"></span>
                    Delete Store
                </button>
            </div>
        </div>
    </div>
</div> {% endcomment %}

<!-- Store Sharing Modal -->
<div class="modal fade" id="shareStoreModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Share Store: {{ store.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <!-- Share Options Tabs -->
                <ul class="nav nav-tabs" id="shareTabsNav" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="url-tab" data-bs-toggle="tab" data-bs-target="#url-share" type="button" role="tab">
                            <i class="fas fa-link"></i> Share via URL
                        </button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email-share" type="button" role="tab">
                            <i class="fas fa-envelope"></i> Share via Email
                        </button>
                    </li>
                </ul>

                <div class="tab-content mt-3" id="shareTabsContent">
                    <!-- URL Share Tab -->
                    <div class="tab-pane fade show active" id="url-share" role="tabpanel">
                        <form id="createShareForm">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="shareTitle" class="form-label">Share Title</label>
                                    <input type="text" class="form-control" id="shareTitle" name="title" value="Shared Store: {{ store.name }}">
                                </div>
                                <div class="col-md-6">
                                    <label for="shareExpiry" class="form-label">Expires At (Optional)</label>
                                    <input type="datetime-local" class="form-control" id="shareExpiry" name="expires_at">
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="shareDescription" class="form-label">Description (Optional)</label>
                                <textarea class="form-control" id="shareDescription" name="description" rows="2" placeholder="Add a custom description for this shared store..."></textarea>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="passwordProtected" name="password_protected">
                                    <label class="form-check-label" for="passwordProtected">
                                        Password protect this share
                                    </label>
                                </div>
                            </div>

                            <div id="passwordSection" style="display: none;">
                                <div class="mb-3">
                                    <label for="sharePassword" class="form-label">Access Password</label>
                                    <input type="password" class="form-control" id="sharePassword" name="access_password" placeholder="Enter password for access">
                                </div>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-link"></i> Create Share Link
                            </button>
                        </form>

                        <!-- Generated Link Display -->
                        <div id="generatedLinkSection" style="display: none;" class="mt-4">
                            <div class="alert alert-success">
                                <h6><i class="fas fa-check-circle"></i> Share Link Created!</h6>
                                <div class="input-group mt-2">
                                    <input type="text" class="form-control" id="generatedLink" readonly>
                                    <button class="btn btn-outline-secondary" type="button" onclick="copyShareLink()">
                                        <i class="fas fa-copy"></i> Copy
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Email Share Tab -->
                    <div class="tab-pane fade" id="email-share" role="tabpanel">
                        <form id="emailShareForm">
                            <div class="mb-3">
                                <label for="recipientEmails" class="form-label">Recipient Emails</label>
                                <textarea class="form-control" id="recipientEmails" name="emails" rows="3"
                                          placeholder="Enter email addresses separated by commas&#10;<EMAIL>, <EMAIL>" required></textarea>
                                <small class="form-text text-muted">Separate multiple emails with commas</small>
                            </div>

                            <div class="mb-3">
                                <label for="emailMessage" class="form-label">Custom Message (Optional)</label>
                                <textarea class="form-control" id="emailMessage" name="message" rows="4"
                                          placeholder="Add a personal message to include with the store share..."></textarea>
                            </div>

                            <input type="hidden" id="emailShareToken" name="share_token">

                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-paper-plane"></i> Send Email Invitations
                            </button>
                        </form>

                        <!-- Email Results -->
                        <div id="emailResultsSection" style="display: none;" class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> Email Results</h6>
                                <div id="emailResults"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Store Shares List Modal -->
<div class="modal fade" id="storeSharesModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Store Shares: {{ store.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="sharesListContainer">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">Loading shares...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}